{"version": 3, "sources": ["../../src/build/utils.ts"], "sourcesContent": ["import type { NextConfig, NextConfigComplete } from '../server/config-shared'\nimport type { ExperimentalPPRConfig } from '../server/lib/experimental/ppr'\nimport type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { AssetBinding } from './webpack/loaders/get-module-build-info'\nimport type {\n  GetStaticPaths,\n  GetStaticPathsResult,\n  PageConfig,\n  ServerRuntime,\n} from '../types'\nimport type { BuildManifest } from '../server/get-page-files'\nimport type {\n  Redirect,\n  Rewrite,\n  Header,\n  CustomRoutes,\n} from '../lib/load-custom-routes'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from './webpack/plugins/middleware-plugin'\nimport type { WebpackLayerName } from '../lib/constants'\nimport type { AppPageModule } from '../server/route-modules/app-page/module'\nimport type { RouteModule } from '../server/route-modules/route-module'\nimport type { NextComponentType } from '../shared/lib/utils'\n\nimport '../server/require-hook'\nimport '../server/node-polyfill-crypto'\nimport '../server/node-environment'\n\nimport {\n  green,\n  yellow,\n  red,\n  cyan,\n  white,\n  bold,\n  underline,\n} from '../lib/picocolors'\nimport getGzipSize from 'next/dist/compiled/gzip-size'\nimport textTable from 'next/dist/compiled/text-table'\nimport path from 'path'\nimport { promises as fs } from 'fs'\nimport { isValidElementType } from 'next/dist/compiled/react-is'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport browserslist from 'next/dist/compiled/browserslist'\nimport {\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  INSTRUMENTATION_HOOK_FILENAME,\n  WEBPACK_LAYERS,\n} from '../lib/constants'\nimport {\n  MODERN_BROWSERSLIST_TARGET,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../shared/lib/constants'\nimport prettyBytes from '../lib/pretty-bytes'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport escapePathDelimiters from '../shared/lib/router/utils/escape-path-delimiters'\nimport { findPageFile } from '../server/lib/find-page-file'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport * as Log from './output/log'\nimport { loadComponents } from '../server/load-components'\nimport type { LoadComponentsReturnType } from '../server/load-components'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getRuntimeContext } from '../server/web/sandbox'\nimport { isClientReference } from '../lib/client-reference'\nimport { createWorkStore } from '../server/async-storage/work-store'\nimport type { CacheHandler } from '../server/lib/incremental-cache'\nimport { IncrementalCache } from '../server/lib/incremental-cache'\nimport { nodeFs } from '../server/lib/node-fs-methods'\nimport * as ciEnvironment from '../server/ci-info'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { denormalizeAppPagePath } from '../shared/lib/page-path/denormalize-app-path'\nimport { RouteKind } from '../server/route-kind'\nimport { interopDefault } from '../lib/interop-default'\nimport type { PageExtensions } from './page-extensions-type'\nimport { formatDynamicImportPath } from '../lib/format-dynamic-import-path'\nimport { isInterceptionRouteAppPath } from '../server/lib/interception-routes'\nimport { checkIsRoutePPREnabled } from '../server/lib/experimental/ppr'\nimport type { Params } from '../server/request/params'\nimport { FallbackMode } from '../lib/fallback'\nimport {\n  fallbackModeToStaticPathsResult,\n  parseStaticPathsResult,\n} from '../lib/fallback'\nimport { getParamKeys } from '../server/request/fallback-params'\nimport type { OutgoingHttpHeaders } from 'http'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport type { AppSegment } from './segment-config/app/app-segments'\nimport { collectSegments } from './segment-config/app/app-segments'\nimport { createIncrementalCache } from '../export/helpers/create-incremental-cache'\nimport { AfterRunner } from '../server/after/run-with-after'\n\nexport type ROUTER_TYPE = 'pages' | 'app'\n\n// Use `print()` for expected console output\nconst print = console.log\n\nconst RESERVED_PAGE = /^\\/(_app|_error|_document|api(\\/|$))/\nconst fileGzipStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStatGzip = (file: string) => {\n  const cached = fileGzipStats[file]\n  if (cached) return cached\n  return (fileGzipStats[file] = getGzipSize.file(file))\n}\n\nconst fileSize = async (file: string) => (await fs.stat(file)).size\n\nconst fileStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStat = (file: string) => {\n  const cached = fileStats[file]\n  if (cached) return cached\n  return (fileStats[file] = fileSize(file))\n}\n\nexport function unique<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  return [...new Set([...main, ...sub])]\n}\n\nexport function difference<T>(\n  main: ReadonlyArray<T> | ReadonlySet<T>,\n  sub: ReadonlyArray<T> | ReadonlySet<T>\n): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...a].filter((x) => !b.has(x))\n}\n\n/**\n * Return an array of the items shared by both arrays.\n */\nfunction intersect<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...new Set([...a].filter((x) => b.has(x)))]\n}\n\nfunction sum(a: ReadonlyArray<number>): number {\n  return a.reduce((size, stat) => size + stat, 0)\n}\n\ntype ComputeFilesGroup = {\n  files: ReadonlyArray<string>\n  size: {\n    total: number\n  }\n}\n\ntype ComputeFilesManifest = {\n  unique: ComputeFilesGroup\n  common: ComputeFilesGroup\n}\n\ntype ComputeFilesManifestResult = {\n  router: {\n    pages: ComputeFilesManifest\n    app?: ComputeFilesManifest\n  }\n  sizes: Map<string, number>\n}\n\nlet cachedBuildManifest: BuildManifest | undefined\nlet cachedAppBuildManifest: AppBuildManifest | undefined\n\nlet lastCompute: ComputeFilesManifestResult | undefined\nlet lastComputePageInfo: boolean | undefined\n\nexport async function computeFromManifest(\n  manifests: {\n    build: BuildManifest\n    app?: AppBuildManifest\n  },\n  distPath: string,\n  gzipSize: boolean = true,\n  pageInfos?: Map<string, PageInfo>\n): Promise<ComputeFilesManifestResult> {\n  if (\n    Object.is(cachedBuildManifest, manifests.build) &&\n    lastComputePageInfo === !!pageInfos &&\n    Object.is(cachedAppBuildManifest, manifests.app)\n  ) {\n    return lastCompute!\n  }\n\n  // Determine the files that are in pages and app and count them, this will\n  // tell us if they are unique or common.\n\n  const countBuildFiles = (\n    map: Map<string, number>,\n    key: string,\n    manifest: Record<string, ReadonlyArray<string>>\n  ) => {\n    for (const file of manifest[key]) {\n      if (key === '/_app') {\n        map.set(file, Infinity)\n      } else if (map.has(file)) {\n        map.set(file, map.get(file)! + 1)\n      } else {\n        map.set(file, 1)\n      }\n    }\n  }\n\n  const files: {\n    pages: {\n      each: Map<string, number>\n      expected: number\n    }\n    app?: {\n      each: Map<string, number>\n      expected: number\n    }\n  } = {\n    pages: { each: new Map(), expected: 0 },\n  }\n\n  for (const key in manifests.build.pages) {\n    if (pageInfos) {\n      const pageInfo = pageInfos.get(key)\n      // don't include AMP pages since they don't rely on shared bundles\n      // AMP First pages are not under the pageInfos key\n      if (pageInfo?.isHybridAmp) {\n        continue\n      }\n    }\n\n    files.pages.expected++\n    countBuildFiles(files.pages.each, key, manifests.build.pages)\n  }\n\n  // Collect the build files form the app manifest.\n  if (manifests.app?.pages) {\n    files.app = { each: new Map<string, number>(), expected: 0 }\n\n    for (const key in manifests.app.pages) {\n      files.app.expected++\n      countBuildFiles(files.app.each, key, manifests.app.pages)\n    }\n  }\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n  const stats = new Map<string, number>()\n\n  // For all of the files in the pages and app manifests, compute the file size\n  // at once.\n\n  await Promise.all(\n    [\n      ...new Set<string>([\n        ...files.pages.each.keys(),\n        ...(files.app?.each.keys() ?? []),\n      ]),\n    ].map(async (f) => {\n      try {\n        // Add the file size to the stats.\n        stats.set(f, await getSize(path.join(distPath, f)))\n      } catch {}\n    })\n  )\n\n  const groupFiles = async (listing: {\n    each: Map<string, number>\n    expected: number\n  }): Promise<ComputeFilesManifest> => {\n    const entries = [...listing.each.entries()]\n\n    const shapeGroup = (group: [string, number][]): ComputeFilesGroup =>\n      group.reduce(\n        (acc, [f]) => {\n          acc.files.push(f)\n\n          const size = stats.get(f)\n          if (typeof size === 'number') {\n            acc.size.total += size\n          }\n\n          return acc\n        },\n        {\n          files: [] as string[],\n          size: {\n            total: 0,\n          },\n        }\n      )\n\n    return {\n      unique: shapeGroup(entries.filter(([, len]) => len === 1)),\n      common: shapeGroup(\n        entries.filter(\n          ([, len]) => len === listing.expected || len === Infinity\n        )\n      ),\n    }\n  }\n\n  lastCompute = {\n    router: {\n      pages: await groupFiles(files.pages),\n      app: files.app ? await groupFiles(files.app) : undefined,\n    },\n    sizes: stats,\n  }\n\n  cachedBuildManifest = manifests.build\n  cachedAppBuildManifest = manifests.app\n  lastComputePageInfo = !!pageInfos\n  return lastCompute!\n}\n\nexport function isMiddlewareFilename(file?: string | null) {\n  return file === MIDDLEWARE_FILENAME || file === `src/${MIDDLEWARE_FILENAME}`\n}\n\nexport function isInstrumentationHookFilename(file?: string | null) {\n  return (\n    file === INSTRUMENTATION_HOOK_FILENAME ||\n    file === `src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nconst filterAndSortList = (\n  list: ReadonlyArray<string>,\n  routeType: ROUTER_TYPE,\n  hasCustomApp: boolean\n) => {\n  let pages: string[]\n  if (routeType === 'app') {\n    // filter out static app route of /favicon.ico\n    pages = list.filter((e) => e !== '/favicon.ico')\n  } else {\n    // filter built-in pages\n    pages = list\n      .slice()\n      .filter(\n        (e) =>\n          !(\n            e === '/_document' ||\n            e === '/_error' ||\n            (!hasCustomApp && e === '/_app')\n          )\n      )\n  }\n  return pages.sort((a, b) => a.localeCompare(b))\n}\n\nexport interface PageInfo {\n  isHybridAmp?: boolean\n  size: number\n  totalSize: number\n  isStatic: boolean\n  isSSG: boolean\n  /**\n   * If true, it means that the route has partial prerendering enabled.\n   */\n  isRoutePPREnabled: boolean\n  ssgPageRoutes: string[] | null\n  initialRevalidateSeconds: number | false\n  pageDuration: number | undefined\n  ssgPageDurations: number[] | undefined\n  runtime: ServerRuntime\n  hasEmptyPrelude?: boolean\n  hasPostponed?: boolean\n  isDynamicAppRoute?: boolean\n}\n\nexport type PageInfos = Map<string, PageInfo>\n\nexport interface RoutesUsingEdgeRuntime {\n  [route: string]: 0\n}\n\nexport function collectRoutesUsingEdgeRuntime(\n  input: PageInfos\n): RoutesUsingEdgeRuntime {\n  const routesUsingEdgeRuntime: RoutesUsingEdgeRuntime = {}\n  for (const [route, info] of input.entries()) {\n    if (isEdgeRuntime(info.runtime)) {\n      routesUsingEdgeRuntime[route] = 0\n    }\n  }\n\n  return routesUsingEdgeRuntime\n}\n\nexport async function printTreeView(\n  lists: {\n    pages: ReadonlyArray<string>\n    app: ReadonlyArray<string> | undefined\n  },\n  pageInfos: Map<string, PageInfo>,\n  {\n    distPath,\n    buildId,\n    pagesDir,\n    pageExtensions,\n    buildManifest,\n    appBuildManifest,\n    middlewareManifest,\n    useStaticPages404,\n    gzipSize = true,\n  }: {\n    distPath: string\n    buildId: string\n    pagesDir?: string\n    pageExtensions: PageExtensions\n    buildManifest: BuildManifest\n    appBuildManifest?: AppBuildManifest\n    middlewareManifest: MiddlewareManifest\n    useStaticPages404: boolean\n    gzipSize?: boolean\n  }\n) {\n  const getPrettySize = (_size: number): string => {\n    const size = prettyBytes(_size)\n    return white(bold(size))\n  }\n\n  const MIN_DURATION = 300\n  const getPrettyDuration = (_duration: number): string => {\n    const duration = `${_duration} ms`\n    // green for 300-1000ms\n    if (_duration < 1000) return green(duration)\n    // yellow for 1000-2000ms\n    if (_duration < 2000) return yellow(duration)\n    // red for >= 2000ms\n    return red(bold(duration))\n  }\n\n  const getCleanName = (fileName: string) =>\n    fileName\n      // Trim off `static/`\n      .replace(/^static\\//, '')\n      // Re-add `static/` for root files\n      .replace(/^<buildId>/, 'static')\n      // Remove file hash\n      .replace(/(?:^|[.-])([0-9a-z]{6})[0-9a-z]{14}(?=\\.)/, '.$1')\n\n  // Check if we have a custom app.\n  const hasCustomApp = !!(\n    pagesDir && (await findPageFile(pagesDir, '/_app', pageExtensions, false))\n  )\n\n  // Collect all the symbols we use so we can print the icons out.\n  const usedSymbols = new Set()\n\n  const messages: [string, string, string][] = []\n\n  const stats = await computeFromManifest(\n    { build: buildManifest, app: appBuildManifest },\n    distPath,\n    gzipSize,\n    pageInfos\n  )\n\n  const printFileTree = async ({\n    list,\n    routerType,\n  }: {\n    list: ReadonlyArray<string>\n    routerType: ROUTER_TYPE\n  }) => {\n    const filteredPages = filterAndSortList(list, routerType, hasCustomApp)\n    if (filteredPages.length === 0) {\n      return\n    }\n\n    messages.push(\n      [\n        routerType === 'app' ? 'Route (app)' : 'Route (pages)',\n        'Size',\n        'First Load JS',\n      ].map((entry) => underline(entry)) as [string, string, string]\n    )\n\n    filteredPages.forEach((item, i, arr) => {\n      const border =\n        i === 0\n          ? arr.length === 1\n            ? '─'\n            : '┌'\n          : i === arr.length - 1\n            ? '└'\n            : '├'\n\n      const pageInfo = pageInfos.get(item)\n      const ampFirst = buildManifest.ampFirstPages.includes(item)\n      const totalDuration =\n        (pageInfo?.pageDuration || 0) +\n        (pageInfo?.ssgPageDurations?.reduce((a, b) => a + (b || 0), 0) || 0)\n\n      let symbol: string\n\n      if (item === '/_app' || item === '/_app.server') {\n        symbol = ' '\n      } else if (isEdgeRuntime(pageInfo?.runtime)) {\n        symbol = 'ƒ'\n      } else if (pageInfo?.isRoutePPREnabled) {\n        if (\n          // If the page has an empty prelude, then it's equivalent to a dynamic page\n          pageInfo?.hasEmptyPrelude ||\n          // ensure we don't mark dynamic paths that postponed as being dynamic\n          // since in this case we're able to partially prerender it\n          (pageInfo.isDynamicAppRoute && !pageInfo.hasPostponed)\n        ) {\n          symbol = 'ƒ'\n        } else if (!pageInfo?.hasPostponed) {\n          symbol = '○'\n        } else {\n          symbol = '◐'\n        }\n      } else if (pageInfo?.isStatic) {\n        symbol = '○'\n      } else if (pageInfo?.isSSG) {\n        symbol = '●'\n      } else {\n        symbol = 'ƒ'\n      }\n\n      usedSymbols.add(symbol)\n\n      if (pageInfo?.initialRevalidateSeconds) usedSymbols.add('ISR')\n\n      messages.push([\n        `${border} ${symbol} ${\n          pageInfo?.initialRevalidateSeconds\n            ? `${item} (ISR: ${pageInfo?.initialRevalidateSeconds} Seconds)`\n            : item\n        }${\n          totalDuration > MIN_DURATION\n            ? ` (${getPrettyDuration(totalDuration)})`\n            : ''\n        }`,\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? prettyBytes(pageInfo.size)\n              : ''\n          : '',\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? getPrettySize(pageInfo.totalSize)\n              : ''\n          : '',\n      ])\n\n      const uniqueCssFiles =\n        buildManifest.pages[item]?.filter(\n          (file) =>\n            file.endsWith('.css') &&\n            stats.router[routerType]?.unique.files.includes(file)\n        ) || []\n\n      if (uniqueCssFiles.length > 0) {\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        uniqueCssFiles.forEach((file, index, { length }) => {\n          const innerSymbol = index === length - 1 ? '└' : '├'\n          const size = stats.sizes.get(file)\n          messages.push([\n            `${contSymbol}   ${innerSymbol} ${getCleanName(file)}`,\n            typeof size === 'number' ? prettyBytes(size) : '',\n            '',\n          ])\n        })\n      }\n\n      if (pageInfo?.ssgPageRoutes?.length) {\n        const totalRoutes = pageInfo.ssgPageRoutes.length\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        let routes: { route: string; duration: number; avgDuration?: number }[]\n        if (\n          pageInfo.ssgPageDurations &&\n          pageInfo.ssgPageDurations.some((d) => d > MIN_DURATION)\n        ) {\n          const previewPages = totalRoutes === 8 ? 8 : Math.min(totalRoutes, 7)\n          const routesWithDuration = pageInfo.ssgPageRoutes\n            .map((route, idx) => ({\n              route,\n              duration: pageInfo.ssgPageDurations![idx] || 0,\n            }))\n            .sort(({ duration: a }, { duration: b }) =>\n              // Sort by duration\n              // keep too small durations in original order at the end\n              a <= MIN_DURATION && b <= MIN_DURATION ? 0 : b - a\n            )\n          routes = routesWithDuration.slice(0, previewPages)\n          const remainingRoutes = routesWithDuration.slice(previewPages)\n          if (remainingRoutes.length) {\n            const remaining = remainingRoutes.length\n            const avgDuration = Math.round(\n              remainingRoutes.reduce(\n                (total, { duration }) => total + duration,\n                0\n              ) / remainingRoutes.length\n            )\n            routes.push({\n              route: `[+${remaining} more paths]`,\n              duration: 0,\n              avgDuration,\n            })\n          }\n        } else {\n          const previewPages = totalRoutes === 4 ? 4 : Math.min(totalRoutes, 3)\n          routes = pageInfo.ssgPageRoutes\n            .slice(0, previewPages)\n            .map((route) => ({ route, duration: 0 }))\n          if (totalRoutes > previewPages) {\n            const remaining = totalRoutes - previewPages\n            routes.push({ route: `[+${remaining} more paths]`, duration: 0 })\n          }\n        }\n\n        routes.forEach(\n          ({ route, duration, avgDuration }, index, { length }) => {\n            const innerSymbol = index === length - 1 ? '└' : '├'\n            messages.push([\n              `${contSymbol}   ${innerSymbol} ${route}${\n                duration > MIN_DURATION\n                  ? ` (${getPrettyDuration(duration)})`\n                  : ''\n              }${\n                avgDuration && avgDuration > MIN_DURATION\n                  ? ` (avg ${getPrettyDuration(avgDuration)})`\n                  : ''\n              }`,\n              '',\n              '',\n            ])\n          }\n        )\n      }\n    })\n\n    const sharedFilesSize = stats.router[routerType]?.common.size.total\n    const sharedFiles = stats.router[routerType]?.common.files ?? []\n\n    messages.push([\n      '+ First Load JS shared by all',\n      typeof sharedFilesSize === 'number' ? getPrettySize(sharedFilesSize) : '',\n      '',\n    ])\n    const sharedCssFiles: string[] = []\n    const sharedJsChunks = [\n      ...sharedFiles\n        .filter((file) => {\n          if (file.endsWith('.css')) {\n            sharedCssFiles.push(file)\n            return false\n          }\n          return true\n        })\n        .map((e) => e.replace(buildId, '<buildId>'))\n        .sort(),\n      ...sharedCssFiles.map((e) => e.replace(buildId, '<buildId>')).sort(),\n    ]\n\n    // if the some chunk are less than 10kb or we don't know the size, we only show the total size of the rest\n    const tenKbLimit = 10 * 1000\n    let restChunkSize = 0\n    let restChunkCount = 0\n    sharedJsChunks.forEach((fileName, index, { length }) => {\n      const innerSymbol = index + restChunkCount === length - 1 ? '└' : '├'\n\n      const originalName = fileName.replace('<buildId>', buildId)\n      const cleanName = getCleanName(fileName)\n      const size = stats.sizes.get(originalName)\n\n      if (!size || size < tenKbLimit) {\n        restChunkCount++\n        restChunkSize += size || 0\n        return\n      }\n\n      messages.push([`  ${innerSymbol} ${cleanName}`, prettyBytes(size), ''])\n    })\n\n    if (restChunkCount > 0) {\n      messages.push([\n        `  └ other shared chunks (total)`,\n        prettyBytes(restChunkSize),\n        '',\n      ])\n    }\n  }\n\n  // If enabled, then print the tree for the app directory.\n  if (lists.app && stats.router.app) {\n    await printFileTree({\n      routerType: 'app',\n      list: lists.app,\n    })\n\n    messages.push(['', '', ''])\n  }\n\n  pageInfos.set('/404', {\n    ...(pageInfos.get('/404') || pageInfos.get('/_error'))!,\n    isStatic: useStaticPages404,\n  })\n\n  // If there's no app /_notFound page present, then the 404 is still using the pages/404\n  if (\n    !lists.pages.includes('/404') &&\n    !lists.app?.includes(UNDERSCORE_NOT_FOUND_ROUTE)\n  ) {\n    lists.pages = [...lists.pages, '/404']\n  }\n\n  // Print the tree view for the pages directory.\n  await printFileTree({\n    routerType: 'pages',\n    list: lists.pages,\n  })\n\n  const middlewareInfo = middlewareManifest.middleware?.['/']\n  if (middlewareInfo?.files.length > 0) {\n    const middlewareSizes = await Promise.all(\n      middlewareInfo.files\n        .map((dep) => `${distPath}/${dep}`)\n        .map(gzipSize ? fsStatGzip : fsStat)\n    )\n\n    messages.push(['', '', ''])\n    messages.push(['ƒ Middleware', getPrettySize(sum(middlewareSizes)), ''])\n  }\n\n  print(\n    textTable(messages, {\n      align: ['l', 'l', 'r'],\n      stringLength: (str) => stripAnsi(str).length,\n    })\n  )\n\n  const staticFunctionInfo =\n    lists.app && stats.router.app ? 'generateStaticParams' : 'getStaticProps'\n  print()\n  print(\n    textTable(\n      [\n        usedSymbols.has('○') && [\n          '○',\n          '(Static)',\n          'prerendered as static content',\n        ],\n        usedSymbols.has('●') && [\n          '●',\n          '(SSG)',\n          `prerendered as static HTML (uses ${cyan(staticFunctionInfo)})`,\n        ],\n        usedSymbols.has('ISR') && [\n          '',\n          '(ISR)',\n          `incremental static regeneration (uses revalidate in ${cyan(\n            staticFunctionInfo\n          )})`,\n        ],\n        usedSymbols.has('◐') && [\n          '◐',\n          '(Partial Prerender)',\n          'prerendered as static HTML with dynamic server-streamed content',\n        ],\n        usedSymbols.has('ƒ') && ['ƒ', '(Dynamic)', `server-rendered on demand`],\n      ].filter((x) => x) as [string, string, string][],\n      {\n        align: ['l', 'l', 'l'],\n        stringLength: (str) => stripAnsi(str).length,\n      }\n    )\n  )\n\n  print()\n}\n\nexport function printCustomRoutes({\n  redirects,\n  rewrites,\n  headers,\n}: CustomRoutes) {\n  const printRoutes = (\n    routes: Redirect[] | Rewrite[] | Header[],\n    type: 'Redirects' | 'Rewrites' | 'Headers'\n  ) => {\n    const isRedirects = type === 'Redirects'\n    const isHeaders = type === 'Headers'\n    print(underline(type))\n\n    /*\n        ┌ source\n        ├ permanent/statusCode\n        └ destination\n     */\n    const routesStr = (routes as any[])\n      .map((route: { source: string }) => {\n        let routeStr = `┌ source: ${route.source}\\n`\n\n        if (!isHeaders) {\n          const r = route as Rewrite\n          routeStr += `${isRedirects ? '├' : '└'} destination: ${\n            r.destination\n          }\\n`\n        }\n        if (isRedirects) {\n          const r = route as Redirect\n          routeStr += `└ ${\n            r.statusCode\n              ? `status: ${r.statusCode}`\n              : `permanent: ${r.permanent}`\n          }\\n`\n        }\n\n        if (isHeaders) {\n          const r = route as Header\n          routeStr += `└ headers:\\n`\n\n          for (let i = 0; i < r.headers.length; i++) {\n            const header = r.headers[i]\n            const last = i === headers.length - 1\n\n            routeStr += `  ${last ? '└' : '├'} ${header.key}: ${header.value}\\n`\n          }\n        }\n\n        return routeStr\n      })\n      .join('\\n')\n\n    print(`${routesStr}\\n`)\n  }\n\n  print()\n  if (redirects.length) {\n    printRoutes(redirects, 'Redirects')\n  }\n  if (headers.length) {\n    printRoutes(headers, 'Headers')\n  }\n\n  const combinedRewrites = [\n    ...rewrites.beforeFiles,\n    ...rewrites.afterFiles,\n    ...rewrites.fallback,\n  ]\n  if (combinedRewrites.length) {\n    printRoutes(combinedRewrites, 'Rewrites')\n  }\n}\n\nexport async function getJsPageSizeInKb(\n  routerType: ROUTER_TYPE,\n  page: string,\n  distPath: string,\n  buildManifest: BuildManifest,\n  appBuildManifest?: AppBuildManifest,\n  gzipSize: boolean = true,\n  cachedStats?: ComputeFilesManifestResult\n): Promise<[number, number]> {\n  const pageManifest = routerType === 'pages' ? buildManifest : appBuildManifest\n  if (!pageManifest) {\n    throw new Error('expected appBuildManifest with an \"app\" pageType')\n  }\n\n  // Normalize appBuildManifest keys\n  if (routerType === 'app') {\n    pageManifest.pages = Object.entries(pageManifest.pages).reduce(\n      (acc: Record<string, string[]>, [key, value]) => {\n        const newKey = normalizeAppPath(key)\n        acc[newKey] = value as string[]\n        return acc\n      },\n      {}\n    )\n  }\n\n  // If stats was not provided, then compute it again.\n  const stats =\n    cachedStats ??\n    (await computeFromManifest(\n      { build: buildManifest, app: appBuildManifest },\n      distPath,\n      gzipSize\n    ))\n\n  const pageData = stats.router[routerType]\n  if (!pageData) {\n    // This error shouldn't happen and represents an error in Next.js.\n    throw new Error('expected \"app\" manifest data with an \"app\" pageType')\n  }\n\n  const pagePath =\n    routerType === 'pages'\n      ? denormalizePagePath(page)\n      : denormalizeAppPagePath(page)\n\n  const fnFilterJs = (entry: string) => entry.endsWith('.js')\n\n  const pageFiles = (pageManifest.pages[pagePath] ?? []).filter(fnFilterJs)\n  const appFiles = (pageManifest.pages['/_app'] ?? []).filter(fnFilterJs)\n\n  const fnMapRealPath = (dep: string) => `${distPath}/${dep}`\n\n  const allFilesReal = unique(pageFiles, appFiles).map(fnMapRealPath)\n  const selfFilesReal = difference(\n    // Find the files shared by the pages files and the unique files...\n    intersect(pageFiles, pageData.unique.files),\n    // but without the common files.\n    pageData.common.files\n  ).map(fnMapRealPath)\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  // Try to get the file size from the page data if available, otherwise do a\n  // raw compute.\n  const getCachedSize = async (file: string) => {\n    const key = file.slice(distPath.length + 1)\n    const size: number | undefined = stats.sizes.get(key)\n\n    // If the size wasn't in the stats bundle, then get it from the file\n    // directly.\n    if (typeof size !== 'number') {\n      return getSize(file)\n    }\n\n    return size\n  }\n\n  try {\n    // Doesn't use `Promise.all`, as we'd double compute duplicate files. This\n    // function is memoized, so the second one will instantly resolve.\n    const allFilesSize = sum(await Promise.all(allFilesReal.map(getCachedSize)))\n    const selfFilesSize = sum(\n      await Promise.all(selfFilesReal.map(getCachedSize))\n    )\n\n    return [selfFilesSize, allFilesSize]\n  } catch {}\n  return [-1, -1]\n}\n\ntype StaticPrerenderedRoute = {\n  path: string\n  encoded: string\n  fallbackRouteParams: undefined\n}\n\ntype FallbackPrerenderedRoute = {\n  path: string\n  encoded: string\n  fallbackRouteParams: readonly string[]\n}\n\nexport type PrerenderedRoute = StaticPrerenderedRoute | FallbackPrerenderedRoute\n\nexport type StaticPathsResult = {\n  fallbackMode: FallbackMode\n  prerenderedRoutes: PrerenderedRoute[]\n}\n\nexport async function buildStaticPaths({\n  page,\n  getStaticPaths,\n  staticPathsResult,\n  configFileName,\n  locales,\n  defaultLocale,\n  appDir,\n}: {\n  page: string\n  getStaticPaths?: GetStaticPaths\n  staticPathsResult?: GetStaticPathsResult\n  configFileName: string\n  locales?: string[]\n  defaultLocale?: string\n  appDir?: boolean\n}): Promise<StaticPathsResult> {\n  const prerenderedRoutes: PrerenderedRoute[] = []\n  const _routeRegex = getRouteRegex(page)\n  const _routeMatcher = getRouteMatcher(_routeRegex)\n\n  // Get the default list of allowed params.\n  const routeParameterKeys = Object.keys(_routeMatcher(page))\n\n  if (!staticPathsResult) {\n    if (getStaticPaths) {\n      staticPathsResult = await getStaticPaths({ locales, defaultLocale })\n    } else {\n      throw new Error(\n        `invariant: attempted to buildStaticPaths without \"staticPathsResult\" or \"getStaticPaths\" ${page}`\n      )\n    }\n  }\n\n  const expectedReturnVal =\n    `Expected: { paths: [], fallback: boolean }\\n` +\n    `See here for more info: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n\n  if (\n    !staticPathsResult ||\n    typeof staticPathsResult !== 'object' ||\n    Array.isArray(staticPathsResult)\n  ) {\n    throw new Error(\n      `Invalid value returned from getStaticPaths in ${page}. Received ${typeof staticPathsResult} ${expectedReturnVal}`\n    )\n  }\n\n  const invalidStaticPathKeys = Object.keys(staticPathsResult).filter(\n    (key) => !(key === 'paths' || key === 'fallback')\n  )\n\n  if (invalidStaticPathKeys.length > 0) {\n    throw new Error(\n      `Extra keys returned from getStaticPaths in ${page} (${invalidStaticPathKeys.join(\n        ', '\n      )}) ${expectedReturnVal}`\n    )\n  }\n\n  if (\n    !(\n      typeof staticPathsResult.fallback === 'boolean' ||\n      staticPathsResult.fallback === 'blocking'\n    )\n  ) {\n    throw new Error(\n      `The \\`fallback\\` key must be returned from getStaticPaths in ${page}.\\n` +\n        expectedReturnVal\n    )\n  }\n\n  const toPrerender = staticPathsResult.paths\n\n  if (!Array.isArray(toPrerender)) {\n    throw new Error(\n      `Invalid \\`paths\\` value returned from getStaticPaths in ${page}.\\n` +\n        `\\`paths\\` must be an array of strings or objects of shape { params: [key: string]: string }`\n    )\n  }\n\n  toPrerender.forEach((entry) => {\n    // For a string-provided path, we must make sure it matches the dynamic\n    // route.\n    if (typeof entry === 'string') {\n      entry = removeTrailingSlash(entry)\n\n      const localePathResult = normalizeLocalePath(entry, locales)\n      let cleanedEntry = entry\n\n      if (localePathResult.detectedLocale) {\n        cleanedEntry = entry.slice(localePathResult.detectedLocale.length + 1)\n      } else if (defaultLocale) {\n        entry = `/${defaultLocale}${entry}`\n      }\n\n      const result = _routeMatcher(cleanedEntry)\n      if (!result) {\n        throw new Error(\n          `The provided path \\`${cleanedEntry}\\` does not match the page: \\`${page}\\`.`\n        )\n      }\n\n      // If leveraging the string paths variant the entry should already be\n      // encoded so we decode the segments ensuring we only escape path\n      // delimiters\n      prerenderedRoutes.push({\n        path: entry\n          .split('/')\n          .map((segment) =>\n            escapePathDelimiters(decodeURIComponent(segment), true)\n          )\n          .join('/'),\n        encoded: entry,\n        fallbackRouteParams: undefined,\n      })\n    }\n    // For the object-provided path, we must make sure it specifies all\n    // required keys.\n    else {\n      const invalidKeys = Object.keys(entry).filter(\n        (key) => key !== 'params' && key !== 'locale'\n      )\n\n      if (invalidKeys.length) {\n        throw new Error(\n          `Additional keys were returned from \\`getStaticPaths\\` in page \"${page}\". ` +\n            `URL Parameters intended for this dynamic route must be nested under the \\`params\\` key, i.e.:` +\n            `\\n\\n\\treturn { params: { ${routeParameterKeys\n              .map((k) => `${k}: ...`)\n              .join(', ')} } }` +\n            `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.\\n`\n        )\n      }\n\n      const { params = {} } = entry\n      let builtPage = page\n      let encodedBuiltPage = page\n\n      routeParameterKeys.forEach((validParamKey) => {\n        const { repeat, optional } = _routeRegex.groups[validParamKey]\n        let paramValue = params[validParamKey]\n        if (\n          optional &&\n          params.hasOwnProperty(validParamKey) &&\n          (paramValue === null ||\n            paramValue === undefined ||\n            (paramValue as any) === false)\n        ) {\n          paramValue = []\n        }\n        if (\n          (repeat && !Array.isArray(paramValue)) ||\n          (!repeat && typeof paramValue !== 'string')\n        ) {\n          // If this is from app directory, and not all params were provided,\n          // then filter this out.\n          if (appDir && typeof paramValue === 'undefined') {\n            builtPage = ''\n            encodedBuiltPage = ''\n            return\n          }\n\n          throw new Error(\n            `A required parameter (${validParamKey}) was not provided as ${\n              repeat ? 'an array' : 'a string'\n            } received ${typeof paramValue} in ${\n              appDir ? 'generateStaticParams' : 'getStaticPaths'\n            } for ${page}`\n          )\n        }\n        let replaced = `[${repeat ? '...' : ''}${validParamKey}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n        builtPage = builtPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[])\n                  .map((segment) => escapePathDelimiters(segment, true))\n                  .join('/')\n              : escapePathDelimiters(paramValue as string, true)\n          )\n          .replace(/\\\\/g, '/')\n          .replace(/(?!^)\\/$/, '')\n\n        encodedBuiltPage = encodedBuiltPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[]).map(encodeURIComponent).join('/')\n              : encodeURIComponent(paramValue as string)\n          )\n          .replace(/\\\\/g, '/')\n          .replace(/(?!^)\\/$/, '')\n      })\n\n      if (!builtPage && !encodedBuiltPage) {\n        return\n      }\n\n      if (entry.locale && !locales?.includes(entry.locale)) {\n        throw new Error(\n          `Invalid locale returned from getStaticPaths for ${page}, the locale ${entry.locale} is not specified in ${configFileName}`\n        )\n      }\n      const curLocale = entry.locale || defaultLocale || ''\n\n      prerenderedRoutes.push({\n        path: `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && builtPage === '/' ? '' : builtPage\n        }`,\n        encoded: `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && encodedBuiltPage === '/' ? '' : encodedBuiltPage\n        }`,\n        fallbackRouteParams: undefined,\n      })\n    }\n  })\n\n  const seen = new Set<string>()\n\n  return {\n    fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n    prerenderedRoutes: prerenderedRoutes.filter((route) => {\n      if (seen.has(route.path)) return false\n\n      // Filter out duplicate paths.\n      seen.add(route.path)\n      return true\n    }),\n  }\n}\n\nexport type PartialStaticPathsResult = {\n  [P in keyof StaticPathsResult]: StaticPathsResult[P] | undefined\n}\n\nexport async function buildAppStaticPaths({\n  dir,\n  page,\n  distDir,\n  dynamicIO,\n  authInterrupts,\n  configFileName,\n  segments,\n  isrFlushToDisk,\n  cacheHandler,\n  cacheLifeProfiles,\n  requestHeaders,\n  maxMemoryCacheSize,\n  fetchCacheKeyPrefix,\n  nextConfigOutput,\n  ComponentMod,\n  isRoutePPREnabled,\n  buildId,\n}: {\n  dir: string\n  page: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  configFileName: string\n  segments: AppSegment[]\n  distDir: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  cacheHandler?: string\n  cacheLifeProfiles?: {\n    [profile: string]: import('../server/use-cache/cache-life').CacheLife\n  }\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  ComponentMod: AppPageModule\n  isRoutePPREnabled: boolean | undefined\n  buildId: string\n}): Promise<PartialStaticPathsResult> {\n  if (\n    segments.some((generate) => generate.config?.dynamicParams === true) &&\n    nextConfigOutput === 'export'\n  ) {\n    throw new Error(\n      '\"dynamicParams: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/app/building-your-application/deploying/static-exports'\n    )\n  }\n\n  ComponentMod.patchFetch()\n\n  let CurCacheHandler: typeof CacheHandler | undefined\n  if (cacheHandler) {\n    CurCacheHandler = interopDefault(\n      await import(formatDynamicImportPath(dir, cacheHandler)).then(\n        (mod) => mod.default || mod\n      )\n    )\n  }\n\n  const incrementalCache = new IncrementalCache({\n    fs: nodeFs,\n    dev: true,\n    dynamicIO,\n    flushToDisk: isrFlushToDisk,\n    serverDistDir: path.join(distDir, 'server'),\n    fetchCacheKeyPrefix,\n    maxMemoryCacheSize,\n    getPrerenderManifest: () => ({\n      version: -1 as any, // letting us know this doesn't conform to spec\n      routes: {},\n      dynamicRoutes: {},\n      notFoundRoutes: [],\n      preview: null as any, // `preview` is special case read in next-dev-server\n    }),\n    CurCacheHandler,\n    requestHeaders,\n    minimalMode: ciEnvironment.hasNextSupport,\n  })\n\n  const paramKeys = new Set<string>()\n\n  const staticParamKeys = new Set<string>()\n  for (const segment of segments) {\n    if (segment.param) {\n      paramKeys.add(segment.param)\n\n      if (segment.config?.dynamicParams === false) {\n        staticParamKeys.add(segment.param)\n      }\n    }\n  }\n\n  const afterRunner = new AfterRunner()\n\n  const store = createWorkStore({\n    page,\n    // We're discovering the parameters here, so we don't have any unknown\n    // ones.\n    fallbackRouteParams: null,\n    renderOpts: {\n      incrementalCache,\n      cacheLifeProfiles,\n      supportsDynamicResponse: true,\n      isRevalidate: false,\n      experimental: {\n        dynamicIO,\n        authInterrupts,\n      },\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n      buildId,\n    },\n  })\n\n  const routeParams = await ComponentMod.workAsyncStorage.run(\n    store,\n    async () => {\n      async function builtRouteParams(\n        parentsParams: Params[] = [],\n        idx = 0\n      ): Promise<Params[]> {\n        // If we don't have any more to process, then we're done.\n        if (idx === segments.length) return parentsParams\n\n        const current = segments[idx]\n\n        if (\n          typeof current.generateStaticParams !== 'function' &&\n          idx < segments.length\n        ) {\n          return builtRouteParams(parentsParams, idx + 1)\n        }\n\n        const params: Params[] = []\n\n        if (current.generateStaticParams) {\n          // fetchCache can be used to inform the fetch() defaults used inside\n          // of generateStaticParams. revalidate and dynamic options don't come into\n          // play within generateStaticParams.\n          if (typeof current.config?.fetchCache !== 'undefined') {\n            store.fetchCache = current.config.fetchCache\n          }\n\n          if (parentsParams.length > 0) {\n            for (const parentParams of parentsParams) {\n              const result = await current.generateStaticParams({\n                params: parentParams,\n              })\n\n              for (const item of result) {\n                params.push({ ...parentParams, ...item })\n              }\n            }\n          } else {\n            const result = await current.generateStaticParams({ params: {} })\n\n            params.push(...result)\n          }\n        }\n\n        if (idx < segments.length) {\n          return builtRouteParams(params, idx + 1)\n        }\n\n        return params\n      }\n\n      return builtRouteParams()\n    }\n  )\n\n  let lastDynamicSegmentHadGenerateStaticParams = false\n  for (const segment of segments) {\n    // Check to see if there are any missing params for segments that have\n    // dynamicParams set to false.\n    if (\n      segment.param &&\n      segment.isDynamicSegment &&\n      segment.config?.dynamicParams === false\n    ) {\n      for (const params of routeParams) {\n        if (segment.param in params) continue\n\n        const relative = segment.filePath\n          ? path.relative(dir, segment.filePath)\n          : undefined\n\n        throw new Error(\n          `Segment \"${relative}\" exports \"dynamicParams: false\" but the param \"${segment.param}\" is missing from the generated route params.`\n        )\n      }\n    }\n\n    if (\n      segment.isDynamicSegment &&\n      typeof segment.generateStaticParams !== 'function'\n    ) {\n      lastDynamicSegmentHadGenerateStaticParams = false\n    } else if (typeof segment.generateStaticParams === 'function') {\n      lastDynamicSegmentHadGenerateStaticParams = true\n    }\n  }\n\n  // Determine if all the segments have had their parameters provided. If there\n  // was no dynamic parameters, then we've collected all the params.\n  const hadAllParamsGenerated =\n    paramKeys.size === 0 ||\n    (routeParams.length > 0 &&\n      routeParams.every((params) => {\n        for (const key of paramKeys) {\n          if (key in params) continue\n          return false\n        }\n        return true\n      }))\n\n  // TODO: dynamic params should be allowed to be granular per segment but\n  // we need additional information stored/leveraged in the prerender\n  // manifest to allow this behavior.\n  const dynamicParams = segments.every(\n    (segment) => segment.config?.dynamicParams !== false\n  )\n\n  const supportsRoutePreGeneration =\n    hadAllParamsGenerated || process.env.NODE_ENV === 'production'\n\n  const fallbackMode = dynamicParams\n    ? supportsRoutePreGeneration\n      ? isRoutePPREnabled\n        ? FallbackMode.PRERENDER\n        : FallbackMode.BLOCKING_STATIC_RENDER\n      : undefined\n    : FallbackMode.NOT_FOUND\n\n  let result: PartialStaticPathsResult = {\n    fallbackMode,\n    prerenderedRoutes: lastDynamicSegmentHadGenerateStaticParams\n      ? []\n      : undefined,\n  }\n\n  if (hadAllParamsGenerated && fallbackMode) {\n    result = await buildStaticPaths({\n      staticPathsResult: {\n        fallback: fallbackModeToStaticPathsResult(fallbackMode),\n        paths: routeParams.map((params) => ({ params })),\n      },\n      page,\n      configFileName,\n      appDir: true,\n    })\n  }\n\n  // If the fallback mode is a prerender, we want to include the dynamic\n  // route in the prerendered routes too.\n  if (isRoutePPREnabled) {\n    result.prerenderedRoutes ??= []\n    result.prerenderedRoutes.unshift({\n      path: page,\n      encoded: page,\n      fallbackRouteParams: getParamKeys(page),\n    })\n  }\n\n  await afterRunner.executeAfter()\n\n  return result\n}\n\ntype PageIsStaticResult = {\n  isRoutePPREnabled?: boolean\n  isStatic?: boolean\n  isAmpOnly?: boolean\n  isHybridAmp?: boolean\n  hasServerProps?: boolean\n  hasStaticProps?: boolean\n  prerenderedRoutes: PrerenderedRoute[] | undefined\n  prerenderFallbackMode: FallbackMode | undefined\n  isNextImageImported?: boolean\n  traceIncludes?: string[]\n  traceExcludes?: string[]\n  appConfig?: AppSegmentConfig\n}\n\nexport async function isPageStatic({\n  dir,\n  page,\n  distDir,\n  configFileName,\n  runtimeEnvConfig,\n  httpAgentOptions,\n  locales,\n  defaultLocale,\n  parentId,\n  pageRuntime,\n  edgeInfo,\n  pageType,\n  dynamicIO,\n  authInterrupts,\n  originalAppPath,\n  isrFlushToDisk,\n  maxMemoryCacheSize,\n  nextConfigOutput,\n  cacheHandler,\n  cacheHandlers,\n  cacheLifeProfiles,\n  pprConfig,\n  buildId,\n}: {\n  dir: string\n  page: string\n  distDir: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  configFileName: string\n  runtimeEnvConfig: any\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  locales?: string[]\n  defaultLocale?: string\n  parentId?: any\n  edgeInfo?: any\n  pageType?: 'pages' | 'app'\n  pageRuntime?: ServerRuntime\n  originalAppPath?: string\n  isrFlushToDisk?: boolean\n  maxMemoryCacheSize?: number\n  cacheHandler?: string\n  cacheHandlers?: Record<string, string | undefined>\n  cacheLifeProfiles?: {\n    [profile: string]: import('../server/use-cache/cache-life').CacheLife\n  }\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  pprConfig: ExperimentalPPRConfig | undefined\n  buildId: string\n}): Promise<PageIsStaticResult> {\n  await createIncrementalCache({\n    cacheHandler,\n    cacheHandlers,\n    distDir,\n    dir,\n    dynamicIO,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const isPageStaticSpan = trace('is-page-static-utils', parentId)\n  return isPageStaticSpan\n    .traceAsyncFn(async (): Promise<PageIsStaticResult> => {\n      require('../shared/lib/runtime-config.external').setConfig(\n        runtimeEnvConfig\n      )\n      setHttpClientAndAgentOptions({\n        httpAgentOptions,\n      })\n\n      let componentsResult: LoadComponentsReturnType\n      let prerenderedRoutes: PrerenderedRoute[] | undefined\n      let prerenderFallbackMode: FallbackMode | undefined\n      let appConfig: AppSegmentConfig = {}\n      let isClientComponent: boolean = false\n      const pathIsEdgeRuntime = isEdgeRuntime(pageRuntime)\n\n      if (pathIsEdgeRuntime) {\n        const runtime = await getRuntimeContext({\n          paths: edgeInfo.files.map((file: string) => path.join(distDir, file)),\n          edgeFunctionEntry: {\n            ...edgeInfo,\n            wasm: (edgeInfo.wasm ?? []).map((binding: AssetBinding) => ({\n              ...binding,\n              filePath: path.join(distDir, binding.filePath),\n            })),\n          },\n          name: edgeInfo.name,\n          useCache: true,\n          distDir,\n        })\n        const mod = (\n          await runtime.context._ENTRIES[`middleware_${edgeInfo.name}`]\n        ).ComponentMod\n\n        // This is not needed during require.\n        const buildManifest = {} as BuildManifest\n\n        isClientComponent = isClientReference(mod)\n        componentsResult = {\n          Component: mod.default,\n          Document: mod.Document,\n          App: mod.App,\n          routeModule: mod.routeModule,\n          page,\n          ComponentMod: mod,\n          pageConfig: mod.config || {},\n          buildManifest,\n          reactLoadableManifest: {},\n          getServerSideProps: mod.getServerSideProps,\n          getStaticPaths: mod.getStaticPaths,\n          getStaticProps: mod.getStaticProps,\n        }\n      } else {\n        componentsResult = await loadComponents({\n          distDir,\n          page: originalAppPath || page,\n          isAppPath: pageType === 'app',\n          isDev: false,\n        })\n      }\n      const Comp = componentsResult.Component as NextComponentType | undefined\n      let staticPathsResult: GetStaticPathsResult | undefined\n\n      const routeModule: RouteModule = componentsResult.routeModule\n\n      let isRoutePPREnabled: boolean = false\n\n      if (pageType === 'app') {\n        const ComponentMod: AppPageModule = componentsResult.ComponentMod\n\n        isClientComponent = isClientReference(componentsResult.ComponentMod)\n\n        let segments\n        try {\n          segments = await collectSegments(componentsResult)\n        } catch (err) {\n          throw new Error(`Failed to collect configuration for ${page}`, {\n            cause: err,\n          })\n        }\n\n        appConfig = reduceAppConfig(await collectSegments(componentsResult))\n\n        if (appConfig.dynamic === 'force-static' && pathIsEdgeRuntime) {\n          Log.warn(\n            `Page \"${page}\" is using runtime = 'edge' which is currently incompatible with dynamic = 'force-static'. Please remove either \"runtime\" or \"force-static\" for correct behavior`\n          )\n        }\n\n        // A page supports partial prerendering if it is an app page and either\n        // the whole app has PPR enabled or this page has PPR enabled when we're\n        // in incremental mode.\n        isRoutePPREnabled =\n          routeModule.definition.kind === RouteKind.APP_PAGE &&\n          !isInterceptionRouteAppPath(page) &&\n          checkIsRoutePPREnabled(pprConfig, appConfig)\n\n        // If force dynamic was set and we don't have PPR enabled, then set the\n        // revalidate to 0.\n        // TODO: (PPR) remove this once PPR is enabled by default\n        if (appConfig.dynamic === 'force-dynamic' && !isRoutePPREnabled) {\n          appConfig.revalidate = 0\n        }\n\n        if (isDynamicRoute(page)) {\n          ;({ fallbackMode: prerenderFallbackMode, prerenderedRoutes } =\n            await buildAppStaticPaths({\n              dir,\n              page,\n              dynamicIO,\n              authInterrupts,\n              configFileName,\n              segments,\n              distDir,\n              requestHeaders: {},\n              isrFlushToDisk,\n              maxMemoryCacheSize,\n              cacheHandler,\n              cacheLifeProfiles,\n              ComponentMod,\n              nextConfigOutput,\n              isRoutePPREnabled,\n              buildId,\n            }))\n        }\n      } else {\n        if (!Comp || !isValidElementType(Comp) || typeof Comp === 'string') {\n          throw new Error('INVALID_DEFAULT_EXPORT')\n        }\n      }\n\n      const hasGetInitialProps = !!Comp?.getInitialProps\n      const hasStaticProps = !!componentsResult.getStaticProps\n      const hasStaticPaths = !!componentsResult.getStaticPaths\n      const hasServerProps = !!componentsResult.getServerSideProps\n\n      // A page cannot be prerendered _and_ define a data requirement. That's\n      // contradictory!\n      if (hasGetInitialProps && hasStaticProps) {\n        throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT)\n      }\n\n      if (hasGetInitialProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT)\n      }\n\n      if (hasStaticProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n\n      const pageIsDynamic = isDynamicRoute(page)\n      // A page cannot have static parameters if it is not a dynamic page.\n      if (hasStaticProps && hasStaticPaths && !pageIsDynamic) {\n        throw new Error(\n          `getStaticPaths can only be used with dynamic pages, not '${page}'.` +\n            `\\nLearn more: https://nextjs.org/docs/routing/dynamic-routes`\n        )\n      }\n\n      if (hasStaticProps && pageIsDynamic && !hasStaticPaths) {\n        throw new Error(\n          `getStaticPaths is required for dynamic SSG pages and is missing for '${page}'.` +\n            `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n        )\n      }\n\n      if ((hasStaticProps && hasStaticPaths) || staticPathsResult) {\n        ;({ fallbackMode: prerenderFallbackMode, prerenderedRoutes } =\n          await buildStaticPaths({\n            page,\n            locales,\n            defaultLocale,\n            configFileName,\n            staticPathsResult,\n            getStaticPaths: componentsResult.getStaticPaths!,\n          }))\n      }\n\n      const isNextImageImported = (globalThis as any).__NEXT_IMAGE_IMPORTED\n      const config: PageConfig = isClientComponent\n        ? {}\n        : componentsResult.pageConfig\n\n      let isStatic = false\n      if (!hasStaticProps && !hasGetInitialProps && !hasServerProps) {\n        isStatic = true\n      }\n\n      // When PPR is enabled, any route may be completely static, so\n      // mark this route as static.\n      if (isRoutePPREnabled) {\n        isStatic = true\n      }\n\n      return {\n        isStatic,\n        isRoutePPREnabled,\n        isHybridAmp: config.amp === 'hybrid',\n        isAmpOnly: config.amp === true,\n        prerenderFallbackMode,\n        prerenderedRoutes,\n        hasStaticProps,\n        hasServerProps,\n        isNextImageImported,\n        appConfig,\n      }\n    })\n    .catch((err) => {\n      if (err.message === 'INVALID_DEFAULT_EXPORT') {\n        throw err\n      }\n      console.error(err)\n      throw new Error(`Failed to collect page data for ${page}`)\n    })\n}\n\ntype ReducedAppConfig = Pick<\n  AppSegmentConfig,\n  | 'revalidate'\n  | 'dynamic'\n  | 'fetchCache'\n  | 'preferredRegion'\n  | 'experimental_ppr'\n  | 'runtime'\n  | 'maxDuration'\n>\n\n/**\n * Collect the app config from the generate param segments. This only gets a\n * subset of the config options.\n *\n * @param segments the generate param segments\n * @returns the reduced app config\n */\nexport function reduceAppConfig(\n  segments: Pick<AppSegment, 'config'>[]\n): ReducedAppConfig {\n  const config: ReducedAppConfig = {}\n\n  for (const segment of segments) {\n    const {\n      dynamic,\n      fetchCache,\n      preferredRegion,\n      revalidate,\n      experimental_ppr,\n      runtime,\n      maxDuration,\n    } = segment.config || {}\n\n    // TODO: should conflicting configs here throw an error\n    // e.g. if layout defines one region but page defines another\n\n    if (typeof preferredRegion !== 'undefined') {\n      config.preferredRegion = preferredRegion\n    }\n\n    if (typeof dynamic !== 'undefined') {\n      config.dynamic = dynamic\n    }\n\n    if (typeof fetchCache !== 'undefined') {\n      config.fetchCache = fetchCache\n    }\n\n    if (typeof revalidate !== 'undefined') {\n      config.revalidate = revalidate\n    }\n\n    // Any revalidate number overrides false, and shorter revalidate overrides\n    // longer (initially).\n    if (\n      typeof revalidate === 'number' &&\n      (typeof config.revalidate !== 'number' || revalidate < config.revalidate)\n    ) {\n      config.revalidate = revalidate\n    }\n\n    // If partial prerendering has been set, only override it if the current\n    // value is provided as it's resolved from root layout to leaf page.\n    if (typeof experimental_ppr !== 'undefined') {\n      config.experimental_ppr = experimental_ppr\n    }\n\n    if (typeof runtime !== 'undefined') {\n      config.runtime = runtime\n    }\n\n    if (typeof maxDuration !== 'undefined') {\n      config.maxDuration = maxDuration\n    }\n  }\n\n  return config\n}\n\nexport async function hasCustomGetInitialProps({\n  page,\n  distDir,\n  runtimeEnvConfig,\n  checkingApp,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n  checkingApp: boolean\n}): Promise<boolean> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n  })\n  let mod = components.ComponentMod\n\n  if (checkingApp) {\n    mod = (await mod._app) || mod.default || mod\n  } else {\n    mod = mod.default || mod\n  }\n  mod = await mod\n  return mod.getInitialProps !== mod.origGetInitialProps\n}\n\nexport async function getDefinedNamedExports({\n  page,\n  distDir,\n  runtimeEnvConfig,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n}): Promise<ReadonlyArray<string>> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n  })\n\n  return Object.keys(components.ComponentMod).filter((key) => {\n    return typeof components.ComponentMod[key] !== 'undefined'\n  })\n}\n\nexport function detectConflictingPaths(\n  combinedPages: string[],\n  ssgPages: Set<string>,\n  additionalGeneratedSSGPaths: Map<string, string[]>\n) {\n  const conflictingPaths = new Map<\n    string,\n    Array<{\n      path: string\n      page: string\n    }>\n  >()\n\n  const dynamicSsgPages = [...ssgPages].filter((page) => isDynamicRoute(page))\n  const additionalSsgPathsByPath: {\n    [page: string]: { [path: string]: string }\n  } = {}\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    additionalSsgPathsByPath[pathsPage] ||= {}\n    paths.forEach((curPath) => {\n      const currentPath = curPath.toLowerCase()\n      additionalSsgPathsByPath[pathsPage][currentPath] = curPath\n    })\n  })\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    paths.forEach((curPath) => {\n      const lowerPath = curPath.toLowerCase()\n      let conflictingPage = combinedPages.find(\n        (page) => page.toLowerCase() === lowerPath\n      )\n\n      if (conflictingPage) {\n        conflictingPaths.set(lowerPath, [\n          { path: curPath, page: pathsPage },\n          { path: conflictingPage, page: conflictingPage },\n        ])\n      } else {\n        let conflictingPath: string | undefined\n\n        conflictingPage = dynamicSsgPages.find((page) => {\n          if (page === pathsPage) return false\n\n          conflictingPath =\n            additionalGeneratedSSGPaths.get(page) == null\n              ? undefined\n              : additionalSsgPathsByPath[page][lowerPath]\n          return conflictingPath\n        })\n\n        if (conflictingPage && conflictingPath) {\n          conflictingPaths.set(lowerPath, [\n            { path: curPath, page: pathsPage },\n            { path: conflictingPath, page: conflictingPage },\n          ])\n        }\n      }\n    })\n  })\n\n  if (conflictingPaths.size > 0) {\n    let conflictingPathsOutput = ''\n\n    conflictingPaths.forEach((pathItems) => {\n      pathItems.forEach((pathItem, idx) => {\n        const isDynamic = pathItem.page !== pathItem.path\n\n        if (idx > 0) {\n          conflictingPathsOutput += 'conflicts with '\n        }\n\n        conflictingPathsOutput += `path: \"${pathItem.path}\"${\n          isDynamic ? ` from page: \"${pathItem.page}\" ` : ' '\n        }`\n      })\n      conflictingPathsOutput += '\\n'\n    })\n\n    Log.error(\n      'Conflicting paths returned from getStaticPaths, paths must be unique per page.\\n' +\n        'See more info here: https://nextjs.org/docs/messages/conflicting-ssg-paths\\n\\n' +\n        conflictingPathsOutput\n    )\n    process.exit(1)\n  }\n}\n\nexport async function copyTracedFiles(\n  dir: string,\n  distDir: string,\n  pageKeys: readonly string[],\n  appPageKeys: readonly string[] | undefined,\n  tracingRoot: string,\n  serverConfig: NextConfig,\n  middlewareManifest: MiddlewareManifest,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>\n) {\n  const outputPath = path.join(distDir, 'standalone')\n  let moduleType = false\n  const nextConfig = {\n    ...serverConfig,\n    distDir: `./${path.relative(dir, distDir)}`,\n  }\n  try {\n    const packageJsonPath = path.join(distDir, '../package.json')\n    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'))\n    moduleType = packageJson.type === 'module'\n  } catch {}\n  const copiedFiles = new Set()\n  await fs.rm(outputPath, { recursive: true, force: true })\n\n  async function handleTraceFiles(traceFilePath: string) {\n    const traceData = JSON.parse(await fs.readFile(traceFilePath, 'utf8')) as {\n      files: string[]\n    }\n    const copySema = new Sema(10, { capacity: traceData.files.length })\n    const traceFileDir = path.dirname(traceFilePath)\n\n    await Promise.all(\n      traceData.files.map(async (relativeFile) => {\n        await copySema.acquire()\n\n        const tracedFilePath = path.join(traceFileDir, relativeFile)\n        const fileOutputPath = path.join(\n          outputPath,\n          path.relative(tracingRoot, tracedFilePath)\n        )\n\n        if (!copiedFiles.has(fileOutputPath)) {\n          copiedFiles.add(fileOutputPath)\n\n          await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n          const symlink = await fs.readlink(tracedFilePath).catch(() => null)\n\n          if (symlink) {\n            try {\n              await fs.symlink(symlink, fileOutputPath)\n            } catch (e: any) {\n              if (e.code !== 'EEXIST') {\n                throw e\n              }\n            }\n          } else {\n            await fs.copyFile(tracedFilePath, fileOutputPath)\n          }\n        }\n\n        await copySema.release()\n      })\n    )\n  }\n\n  async function handleEdgeFunction(page: EdgeFunctionDefinition) {\n    async function handleFile(file: string) {\n      const originalPath = path.join(distDir, file)\n      const fileOutputPath = path.join(\n        outputPath,\n        path.relative(tracingRoot, distDir),\n        file\n      )\n      await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n      await fs.copyFile(originalPath, fileOutputPath)\n    }\n    await Promise.all([\n      page.files.map(handleFile),\n      page.wasm?.map((file) => handleFile(file.filePath)),\n      page.assets?.map((file) => handleFile(file.filePath)),\n    ])\n  }\n\n  const edgeFunctionHandlers: Promise<any>[] = []\n\n  for (const middleware of Object.values(middlewareManifest.middleware)) {\n    if (isMiddlewareFilename(middleware.name)) {\n      edgeFunctionHandlers.push(handleEdgeFunction(middleware))\n    }\n  }\n\n  for (const page of Object.values(middlewareManifest.functions)) {\n    edgeFunctionHandlers.push(handleEdgeFunction(page))\n  }\n\n  await Promise.all(edgeFunctionHandlers)\n\n  for (const page of pageKeys) {\n    if (middlewareManifest.functions.hasOwnProperty(page)) {\n      continue\n    }\n    const route = normalizePagePath(page)\n\n    if (staticPages.has(route)) {\n      continue\n    }\n\n    const pageFile = path.join(\n      distDir,\n      'server',\n      'pages',\n      `${normalizePagePath(page)}.js`\n    )\n    const pageTraceFile = `${pageFile}.nft.json`\n    await handleTraceFiles(pageTraceFile).catch((err) => {\n      if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      }\n    })\n  }\n\n  if (appPageKeys) {\n    for (const page of appPageKeys) {\n      if (middlewareManifest.functions.hasOwnProperty(page)) {\n        continue\n      }\n      const pageFile = path.join(distDir, 'server', 'app', `${page}.js`)\n      const pageTraceFile = `${pageFile}.nft.json`\n      await handleTraceFiles(pageTraceFile).catch((err) => {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      })\n    }\n  }\n\n  if (hasInstrumentationHook) {\n    await handleTraceFiles(\n      path.join(distDir, 'server', 'instrumentation.js.nft.json')\n    )\n  }\n\n  await handleTraceFiles(path.join(distDir, 'next-server.js.nft.json'))\n  const serverOutputPath = path.join(\n    outputPath,\n    path.relative(tracingRoot, dir),\n    'server.js'\n  )\n  await fs.mkdir(path.dirname(serverOutputPath), { recursive: true })\n\n  await fs.writeFile(\n    serverOutputPath,\n    `${\n      moduleType\n        ? `performance.mark('next-start');\nimport path from 'path'\nimport { fileURLToPath } from 'url'\nimport module from 'module'\nconst require = module.createRequire(import.meta.url)\nconst __dirname = fileURLToPath(new URL('.', import.meta.url))\n`\n        : `const path = require('path')`\n    }\n\nconst dir = path.join(__dirname)\n\nprocess.env.NODE_ENV = 'production'\nprocess.chdir(__dirname)\n\nconst currentPort = parseInt(process.env.PORT, 10) || 3000\nconst hostname = process.env.HOSTNAME || '0.0.0.0'\n\nlet keepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT, 10)\nconst nextConfig = ${JSON.stringify(nextConfig)}\n\nprocess.env.__NEXT_PRIVATE_STANDALONE_CONFIG = JSON.stringify(nextConfig)\n\nrequire('next')\nconst { startServer } = require('next/dist/server/lib/start-server')\n\nif (\n  Number.isNaN(keepAliveTimeout) ||\n  !Number.isFinite(keepAliveTimeout) ||\n  keepAliveTimeout < 0\n) {\n  keepAliveTimeout = undefined\n}\n\nstartServer({\n  dir,\n  isDev: false,\n  config: nextConfig,\n  hostname,\n  port: currentPort,\n  allowRetry: false,\n  keepAliveTimeout,\n}).catch((err) => {\n  console.error(err);\n  process.exit(1);\n});`\n  )\n}\n\nexport function isReservedPage(page: string) {\n  return RESERVED_PAGE.test(page)\n}\n\nexport function isAppBuiltinNotFoundPage(page: string) {\n  return /next[\\\\/]dist[\\\\/]client[\\\\/]components[\\\\/]not-found-error/.test(\n    page\n  )\n}\n\nexport function isCustomErrorPage(page: string) {\n  return page === '/404' || page === '/500'\n}\n\nexport function isMiddlewareFile(file: string) {\n  return (\n    file === `/${MIDDLEWARE_FILENAME}` || file === `/src/${MIDDLEWARE_FILENAME}`\n  )\n}\n\nexport function isInstrumentationHookFile(file: string) {\n  return (\n    file === `/${INSTRUMENTATION_HOOK_FILENAME}` ||\n    file === `/src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nexport function getPossibleInstrumentationHookFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  const files = []\n  for (const extension of extensions) {\n    files.push(\n      path.join(folder, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`),\n      path.join(folder, `src`, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`)\n    )\n  }\n\n  return files\n}\n\nexport function getPossibleMiddlewareFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  return extensions.map((extension) =>\n    path.join(folder, `${MIDDLEWARE_FILENAME}.${extension}`)\n  )\n}\n\nexport class NestedMiddlewareError extends Error {\n  constructor(\n    nestedFileNames: string[],\n    mainDir: string,\n    pagesOrAppDir: string\n  ) {\n    super(\n      `Nested Middleware is not allowed, found:\\n` +\n        `${nestedFileNames.map((file) => `pages${file}`).join('\\n')}\\n` +\n        `Please move your code to a single file at ${path.join(\n          path.posix.sep,\n          path.relative(mainDir, path.resolve(pagesOrAppDir, '..')),\n          'middleware'\n        )} instead.\\n` +\n        `Read More - https://nextjs.org/docs/messages/nested-middleware`\n    )\n  }\n}\n\nexport function getSupportedBrowsers(\n  dir: string,\n  isDevelopment: boolean\n): string[] {\n  let browsers: any\n  try {\n    const browsersListConfig = browserslist.loadConfig({\n      path: dir,\n      env: isDevelopment ? 'development' : 'production',\n    })\n    // Running `browserslist` resolves `extends` and other config features into a list of browsers\n    if (browsersListConfig && browsersListConfig.length > 0) {\n      browsers = browserslist(browsersListConfig)\n    }\n  } catch {}\n\n  // When user has browserslist use that target\n  if (browsers && browsers.length > 0) {\n    return browsers\n  }\n\n  // Uses modern browsers as the default.\n  return MODERN_BROWSERSLIST_TARGET\n}\n\nexport function isWebpackServerOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.serverOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackClientOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.clientOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackDefaultLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return layer === null || layer === undefined\n}\n\nexport function isWebpackBundledLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.bundled.includes(layer as any))\n}\n\nexport function isWebpackAppPagesLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.appPages.includes(layer as any))\n}\n\nexport function collectMeta({\n  status,\n  headers,\n}: {\n  status?: number\n  headers?: OutgoingHttpHeaders\n}): {\n  status?: number\n  headers?: Record<string, string>\n} {\n  const meta: {\n    status?: number\n    headers?: Record<string, string>\n  } = {}\n\n  if (status !== 200) {\n    meta.status = status\n  }\n\n  if (headers && Object.keys(headers).length) {\n    meta.headers = {}\n\n    // normalize header values as initialHeaders\n    // must be Record<string, string>\n    for (const key in headers) {\n      // set-cookie is already handled - the middleware cookie setting case\n      // isn't needed for the prerender manifest since it can't read cookies\n      if (key === 'x-middleware-set-cookie') continue\n\n      let value = headers[key]\n\n      if (Array.isArray(value)) {\n        if (key === 'set-cookie') {\n          value = value.join(',')\n        } else {\n          value = value[value.length - 1]\n        }\n      }\n\n      if (typeof value === 'string') {\n        meta.headers[key] = value\n      }\n    }\n  }\n\n  return meta\n}\n"], "names": ["NestedMiddlewareError", "buildAppStaticPaths", "buildStaticPaths", "collectMeta", "collectRoutesUsingEdgeRuntime", "computeFromManifest", "copyTracedFiles", "detectConflictingPaths", "difference", "getDefinedNamedExports", "getJsPageSizeInKb", "getPossibleInstrumentationHookFilenames", "getPossibleMiddlewareFilenames", "getSupportedBrowsers", "hasCustomGetInitialProps", "isAppBuiltinNotFoundPage", "isCustomErrorPage", "isInstrumentationHookFile", "isInstrumentationHookFilename", "isMiddlewareFile", "isMiddlewareFilename", "isPageStatic", "isReservedPage", "isWebpackAppPagesLayer", "isWebpackBundledLayer", "isWebpackClientOnlyLayer", "isWebpackDefaultLayer", "isWebpackServerOnlyLayer", "printCustomRoutes", "printTreeView", "reduceAppConfig", "unique", "print", "console", "log", "RESERVED_PAGE", "fileGzipStats", "fsStatGzip", "file", "cached", "getGzipSize", "fileSize", "fs", "stat", "size", "fileStats", "fsStat", "main", "sub", "Set", "a", "b", "filter", "x", "has", "intersect", "sum", "reduce", "cachedBuildManifest", "cachedAppBuildManifest", "lastCompute", "lastComputePageInfo", "manifests", "distPath", "gzipSize", "pageInfos", "files", "Object", "is", "build", "app", "countBuildFiles", "map", "key", "manifest", "set", "Infinity", "get", "pages", "each", "Map", "expected", "pageInfo", "isHybridAmp", "getSize", "stats", "Promise", "all", "keys", "f", "path", "join", "groupFiles", "listing", "entries", "shapeGroup", "group", "acc", "push", "total", "len", "common", "router", "undefined", "sizes", "MIDDLEWARE_FILENAME", "INSTRUMENTATION_HOOK_FILENAME", "filterAndSortList", "list", "routeType", "hasCustomApp", "e", "slice", "sort", "localeCompare", "input", "routesUsingEdgeRuntime", "route", "info", "isEdgeRuntime", "runtime", "lists", "buildId", "pagesDir", "pageExtensions", "buildManifest", "appBuildManifest", "middlewareManifest", "useStaticPages404", "getPrettySize", "_size", "prettyBytes", "white", "bold", "MIN_DURATION", "getPrettyDuration", "_duration", "duration", "green", "yellow", "red", "getCleanName", "fileName", "replace", "findPageFile", "usedSymbols", "messages", "printFileTree", "routerType", "filteredPages", "length", "entry", "underline", "for<PERSON>ach", "item", "i", "arr", "border", "ampFirs<PERSON>", "ampFirstPages", "includes", "totalDuration", "pageDuration", "ssgPageDurations", "symbol", "isRoutePPREnabled", "hasEmptyPrelude", "isDynamicAppRoute", "hasPostponed", "isStatic", "isSSG", "add", "initialRevalidateSeconds", "cyan", "totalSize", "uniqueCssFiles", "endsWith", "contSymbol", "index", "innerSymbol", "ssgPageRoutes", "totalRoutes", "routes", "some", "d", "previewPages", "Math", "min", "routesWithDuration", "idx", "remainingRoutes", "remaining", "avgDuration", "round", "sharedFilesSize", "sharedFiles", "sharedCssFiles", "sharedJsChunks", "tenKbLimit", "restChunkSize", "restChunkCount", "originalName", "cleanName", "UNDERSCORE_NOT_FOUND_ROUTE", "middlewareInfo", "middleware", "middlewareSizes", "dep", "textTable", "align", "stringLength", "str", "stripAnsi", "staticFunctionInfo", "redirects", "rewrites", "headers", "printRoutes", "type", "isRedirects", "isHeaders", "routesStr", "routeStr", "source", "r", "destination", "statusCode", "permanent", "header", "last", "value", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "page", "cachedStats", "pageManifest", "Error", "new<PERSON>ey", "normalizeAppPath", "pageData", "pagePath", "denormalizePagePath", "denormalizeAppPagePath", "fnFilterJs", "pageFiles", "appFiles", "fnMapRealPath", "allFilesReal", "selfFilesReal", "getCachedSize", "allFilesSize", "selfFilesSize", "getStaticPaths", "staticPathsResult", "configFileName", "locales", "defaultLocale", "appDir", "prerenderedRoutes", "_routeRegex", "getRouteRegex", "_routeMatcher", "getRouteMatcher", "routeParameterKeys", "expectedReturnVal", "Array", "isArray", "invalidStatic<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "paths", "removeTrailingSlash", "localePathResult", "normalizeLocalePath", "cleanedEntry", "detectedLocale", "result", "split", "segment", "escapePathDelimiters", "decodeURIComponent", "encoded", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON>", "k", "params", "builtPage", "encodedBuiltPage", "validParamKey", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "replaced", "encodeURIComponent", "locale", "cur<PERSON><PERSON><PERSON>", "seen", "fallbackMode", "parseStaticPathsResult", "dir", "distDir", "dynamicIO", "authInterrupts", "segments", "isrFlushToDisk", "cache<PERSON><PERSON><PERSON>", "cacheLifeProfiles", "requestHeaders", "maxMemoryCacheSize", "fetchCacheKeyPrefix", "nextConfigOutput", "ComponentMod", "generate", "config", "dynamicParams", "patchFetch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "formatDynamicImportPath", "then", "mod", "default", "incrementalCache", "IncrementalCache", "nodeFs", "dev", "flushToDisk", "serverDistDir", "getPrerenderManifest", "version", "dynamicRoutes", "notFoundRoutes", "preview", "minimalMode", "ciEnvironment", "hasNextSupport", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "staticParamKeys", "param", "after<PERSON><PERSON>ner", "After<PERSON><PERSON>ner", "store", "createWorkStore", "renderOpts", "supportsDynamicResponse", "isRevalidate", "experimental", "waitUntil", "context", "onClose", "onAfterTaskError", "onTaskError", "routeParams", "workAsyncStorage", "run", "builtRouteParams", "parents<PERSON><PERSON><PERSON>", "current", "generateStaticParams", "fetchCache", "parentParams", "lastDynamicSegmentHadGenerateStaticParams", "isDynamicSegment", "relative", "filePath", "hadAllParamsGenerated", "every", "supportsRoutePreGeneration", "process", "env", "NODE_ENV", "FallbackMode", "PRERENDER", "BLOCKING_STATIC_RENDER", "NOT_FOUND", "fallbackModeToStaticPathsResult", "unshift", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "executeAfter", "runtimeEnvConfig", "httpAgentOptions", "parentId", "pageRuntime", "edgeInfo", "pageType", "originalAppPath", "cacheHandlers", "pprConfig", "createIncrementalCache", "cacheMaxMemorySize", "isPageStaticSpan", "trace", "traceAsyncFn", "require", "setConfig", "setHttpClientAndAgentOptions", "componentsResult", "prerenderFallbackMode", "appConfig", "isClientComponent", "pathIsEdgeRuntime", "getRuntimeContext", "edgeFunctionEntry", "wasm", "binding", "name", "useCache", "_ENTRIES", "isClientReference", "Component", "Document", "App", "routeModule", "pageConfig", "reactLoadableManifest", "getServerSideProps", "getStaticProps", "loadComponents", "isAppPath", "isDev", "Comp", "collectSegments", "err", "cause", "dynamic", "Log", "warn", "definition", "kind", "RouteKind", "APP_PAGE", "isInterceptionRouteAppPath", "checkIsRoutePPREnabled", "revalidate", "isDynamicRoute", "isValidElementType", "hasGetInitialProps", "getInitialProps", "hasStaticProps", "hasStaticPaths", "hasServerProps", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "pageIsDynamic", "isNextImageImported", "globalThis", "__NEXT_IMAGE_IMPORTED", "amp", "isAmpOnly", "catch", "message", "error", "preferredRegion", "experimental_ppr", "maxDuration", "checkingApp", "components", "_app", "origGetInitialProps", "combinedPages", "ssgPages", "additionalGeneratedSSGPaths", "conflictingPaths", "dynamicSsgPages", "additionalSsgPathsByPath", "pathsPage", "curPath", "currentPath", "toLowerCase", "lowerPath", "conflictingPage", "find", "conflicting<PERSON><PERSON>", "conflictingPathsOutput", "pathItems", "pathItem", "isDynamic", "exit", "pageKeys", "appPageKeys", "tracingRoot", "serverConfig", "hasInstrumentationHook", "staticPages", "outputPath", "moduleType", "nextConfig", "packageJsonPath", "packageJson", "JSON", "parse", "readFile", "copiedFiles", "rm", "recursive", "force", "handleTraceFiles", "traceFilePath", "traceData", "copySema", "<PERSON><PERSON>", "capacity", "traceFileDir", "dirname", "relativeFile", "acquire", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileOutputPath", "mkdir", "symlink", "readlink", "code", "copyFile", "release", "handleEdgeFunction", "handleFile", "originalPath", "assets", "edgeFunctionHandlers", "values", "functions", "normalizePagePath", "pageFile", "pageTraceFile", "serverOutputPath", "writeFile", "stringify", "test", "folder", "extensions", "extension", "constructor", "nestedFileNames", "mainDir", "pagesOrAppDir", "posix", "sep", "resolve", "isDevelopment", "browsers", "browsersListConfig", "browserslist", "loadConfig", "MODERN_BROWSERSLIST_TARGET", "layer", "Boolean", "WEBPACK_LAYERS", "GROUP", "serverOnly", "clientOnly", "bundled", "appPages", "status", "meta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwsEaA,qBAAqB;eAArBA;;IA5gCSC,mBAAmB;eAAnBA;;IA/OAC,gBAAgB;eAAhBA;;IAy0CNC,WAAW;eAAXA;;IAv5DAC,6BAA6B;eAA7BA;;IA7MMC,mBAAmB;eAAnBA;;IAyxDAC,eAAe;eAAfA;;IAxFNC,sBAAsB;eAAtBA;;IAjvDAC,UAAU;eAAVA;;IA2tDMC,sBAAsB;eAAtBA;;IA9/BAC,iBAAiB;eAAjBA;;IAi1CNC,uCAAuC;eAAvCA;;IAeAC,8BAA8B;eAA9BA;;IA4BAC,oBAAoB;eAApBA;;IA5ZMC,wBAAwB;eAAxBA;;IA0VNC,wBAAwB;eAAxBA;;IAMAC,iBAAiB;eAAjBA;;IAUAC,yBAAyB;eAAzBA;;IAp2DAC,6BAA6B;eAA7BA;;IA81DAC,gBAAgB;eAAhBA;;IAl2DAC,oBAAoB;eAApBA;;IAwpCMC,YAAY;eAAZA;;IA4rBNC,cAAc;eAAdA;;IA2HAC,sBAAsB;eAAtBA;;IANAC,qBAAqB;eAArBA;;IAdAC,wBAAwB;eAAxBA;;IAQAC,qBAAqB;eAArBA;;IAhBAC,wBAAwB;eAAxBA;;IA/9CAC,iBAAiB;eAAjBA;;IAzYMC,aAAa;eAAbA;;IAq3CNC,eAAe;eAAfA;;IAnoDAC,MAAM;eAANA;;;QApGT;QACA;QACA;4BAUA;iEACiB;kEACF;6DACL;oBACc;yBACI;kEACb;qEACG;2BAQlB;4BAIA;oEACiB;4BACM;8BACE;2BACD;6EACE;8BACJ;qCACO;+BACN;qCACM;6DACf;gCACU;uBAET;mCACuB;2BACxB;qCACe;mCACF;yBACA;iCACA;2BACF;kCAEC;+BACV;gEACQ;0BACE;oCACM;2BACb;gCACK;yCAES;oCACG;qBACJ;0BAEV;gCAKA;6BAIG;wCACO;8BACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI5B,4CAA4C;AAC5C,MAAMC,QAAQC,QAAQC,GAAG;AAEzB,MAAMC,gBAAgB;AACtB,MAAMC,gBAA8D,CAAC;AACrE,MAAMC,aAAa,CAACC;IAClB,MAAMC,SAASH,aAAa,CAACE,KAAK;IAClC,IAAIC,QAAQ,OAAOA;IACnB,OAAQH,aAAa,CAACE,KAAK,GAAGE,iBAAW,CAACF,IAAI,CAACA;AACjD;AAEA,MAAMG,WAAW,OAAOH,OAAiB,AAAC,CAAA,MAAMI,YAAE,CAACC,IAAI,CAACL,KAAI,EAAGM,IAAI;AAEnE,MAAMC,YAA0D,CAAC;AACjE,MAAMC,SAAS,CAACR;IACd,MAAMC,SAASM,SAAS,CAACP,KAAK;IAC9B,IAAIC,QAAQ,OAAOA;IACnB,OAAQM,SAAS,CAACP,KAAK,GAAGG,SAASH;AACrC;AAEO,SAASP,OAAUgB,IAAsB,EAAEC,GAAqB;IACrE,OAAO;WAAI,IAAIC,IAAI;eAAIF;eAASC;SAAI;KAAE;AACxC;AAEO,SAASxC,WACduC,IAAuC,EACvCC,GAAsC;IAEtC,MAAME,IAAI,IAAID,IAAIF;IAClB,MAAMI,IAAI,IAAIF,IAAID;IAClB,OAAO;WAAIE;KAAE,CAACE,MAAM,CAAC,CAACC,IAAM,CAACF,EAAEG,GAAG,CAACD;AACrC;AAEA;;CAEC,GACD,SAASE,UAAaR,IAAsB,EAAEC,GAAqB;IACjE,MAAME,IAAI,IAAID,IAAIF;IAClB,MAAMI,IAAI,IAAIF,IAAID;IAClB,OAAO;WAAI,IAAIC,IAAI;eAAIC;SAAE,CAACE,MAAM,CAAC,CAACC,IAAMF,EAAEG,GAAG,CAACD;KAAK;AACrD;AAEA,SAASG,IAAIN,CAAwB;IACnC,OAAOA,EAAEO,MAAM,CAAC,CAACb,MAAMD,OAASC,OAAOD,MAAM;AAC/C;AAsBA,IAAIe;AACJ,IAAIC;AAEJ,IAAIC;AACJ,IAAIC;AAEG,eAAexD,oBACpByD,SAGC,EACDC,QAAgB,EAChBC,WAAoB,IAAI,EACxBC,SAAiC;QAyD7BH,gBAmBMI;IA1EV,IACEC,OAAOC,EAAE,CAACV,qBAAqBI,UAAUO,KAAK,KAC9CR,wBAAwB,CAAC,CAACI,aAC1BE,OAAOC,EAAE,CAACT,wBAAwBG,UAAUQ,GAAG,GAC/C;QACA,OAAOV;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IAExC,MAAMW,kBAAkB,CACtBC,KACAC,KACAC;QAEA,KAAK,MAAMpC,QAAQoC,QAAQ,CAACD,IAAI,CAAE;YAChC,IAAIA,QAAQ,SAAS;gBACnBD,IAAIG,GAAG,CAACrC,MAAMsC;YAChB,OAAO,IAAIJ,IAAIlB,GAAG,CAAChB,OAAO;gBACxBkC,IAAIG,GAAG,CAACrC,MAAMkC,IAAIK,GAAG,CAACvC,QAAS;YACjC,OAAO;gBACLkC,IAAIG,GAAG,CAACrC,MAAM;YAChB;QACF;IACF;IAEA,MAAM4B,QASF;QACFY,OAAO;YAAEC,MAAM,IAAIC;YAAOC,UAAU;QAAE;IACxC;IAEA,IAAK,MAAMR,OAAOX,UAAUO,KAAK,CAACS,KAAK,CAAE;QACvC,IAAIb,WAAW;YACb,MAAMiB,WAAWjB,UAAUY,GAAG,CAACJ;YAC/B,kEAAkE;YAClE,kDAAkD;YAClD,IAAIS,4BAAAA,SAAUC,WAAW,EAAE;gBACzB;YACF;QACF;QAEAjB,MAAMY,KAAK,CAACG,QAAQ;QACpBV,gBAAgBL,MAAMY,KAAK,CAACC,IAAI,EAAEN,KAAKX,UAAUO,KAAK,CAACS,KAAK;IAC9D;IAEA,iDAAiD;IACjD,KAAIhB,iBAAAA,UAAUQ,GAAG,qBAAbR,eAAegB,KAAK,EAAE;QACxBZ,MAAMI,GAAG,GAAG;YAAES,MAAM,IAAIC;YAAuBC,UAAU;QAAE;QAE3D,IAAK,MAAMR,OAAOX,UAAUQ,GAAG,CAACQ,KAAK,CAAE;YACrCZ,MAAMI,GAAG,CAACW,QAAQ;YAClBV,gBAAgBL,MAAMI,GAAG,CAACS,IAAI,EAAEN,KAAKX,UAAUQ,GAAG,CAACQ,KAAK;QAC1D;IACF;IAEA,MAAMM,UAAUpB,WAAW3B,aAAaS;IACxC,MAAMuC,QAAQ,IAAIL;IAElB,6EAA6E;IAC7E,WAAW;IAEX,MAAMM,QAAQC,GAAG,CACf;WACK,IAAItC,IAAY;eACdiB,MAAMY,KAAK,CAACC,IAAI,CAACS,IAAI;eACpBtB,EAAAA,aAAAA,MAAMI,GAAG,qBAATJ,WAAWa,IAAI,CAACS,IAAI,OAAM,EAAE;SACjC;KACF,CAAChB,GAAG,CAAC,OAAOiB;QACX,IAAI;YACF,kCAAkC;YAClCJ,MAAMV,GAAG,CAACc,GAAG,MAAML,QAAQM,aAAI,CAACC,IAAI,CAAC5B,UAAU0B;QACjD,EAAE,OAAM,CAAC;IACX;IAGF,MAAMG,aAAa,OAAOC;QAIxB,MAAMC,UAAU;eAAID,QAAQd,IAAI,CAACe,OAAO;SAAG;QAE3C,MAAMC,aAAa,CAACC,QAClBA,MAAMvC,MAAM,CACV,CAACwC,KAAK,CAACR,EAAE;gBACPQ,IAAI/B,KAAK,CAACgC,IAAI,CAACT;gBAEf,MAAM7C,OAAOyC,MAAMR,GAAG,CAACY;gBACvB,IAAI,OAAO7C,SAAS,UAAU;oBAC5BqD,IAAIrD,IAAI,CAACuD,KAAK,IAAIvD;gBACpB;gBAEA,OAAOqD;YACT,GACA;gBACE/B,OAAO,EAAE;gBACTtB,MAAM;oBACJuD,OAAO;gBACT;YACF;QAGJ,OAAO;YACLpE,QAAQgE,WAAWD,QAAQ1C,MAAM,CAAC,CAAC,GAAGgD,IAAI,GAAKA,QAAQ;YACvDC,QAAQN,WACND,QAAQ1C,MAAM,CACZ,CAAC,GAAGgD,IAAI,GAAKA,QAAQP,QAAQZ,QAAQ,IAAImB,QAAQxB;QAGvD;IACF;IAEAhB,cAAc;QACZ0C,QAAQ;YACNxB,OAAO,MAAMc,WAAW1B,MAAMY,KAAK;YACnCR,KAAKJ,MAAMI,GAAG,GAAG,MAAMsB,WAAW1B,MAAMI,GAAG,IAAIiC;QACjD;QACAC,OAAOnB;IACT;IAEA3B,sBAAsBI,UAAUO,KAAK;IACrCV,yBAAyBG,UAAUQ,GAAG;IACtCT,sBAAsB,CAAC,CAACI;IACxB,OAAOL;AACT;AAEO,SAASxC,qBAAqBkB,IAAoB;IACvD,OAAOA,SAASmE,8BAAmB,IAAInE,SAAS,CAAC,IAAI,EAAEmE,8BAAmB,EAAE;AAC9E;AAEO,SAASvF,8BAA8BoB,IAAoB;IAChE,OACEA,SAASoE,wCAA6B,IACtCpE,SAAS,CAAC,IAAI,EAAEoE,wCAA6B,EAAE;AAEnD;AAEA,MAAMC,oBAAoB,CACxBC,MACAC,WACAC;IAEA,IAAIhC;IACJ,IAAI+B,cAAc,OAAO;QACvB,8CAA8C;QAC9C/B,QAAQ8B,KAAKxD,MAAM,CAAC,CAAC2D,IAAMA,MAAM;IACnC,OAAO;QACL,wBAAwB;QACxBjC,QAAQ8B,KACLI,KAAK,GACL5D,MAAM,CACL,CAAC2D,IACC,CACEA,CAAAA,MAAM,gBACNA,MAAM,aACL,CAACD,gBAAgBC,MAAM,OAAO;IAGzC;IACA,OAAOjC,MAAMmC,IAAI,CAAC,CAAC/D,GAAGC,IAAMD,EAAEgE,aAAa,CAAC/D;AAC9C;AA4BO,SAAS/C,8BACd+G,KAAgB;IAEhB,MAAMC,yBAAiD,CAAC;IACxD,KAAK,MAAM,CAACC,OAAOC,KAAK,IAAIH,MAAMrB,OAAO,GAAI;QAC3C,IAAIyB,IAAAA,4BAAa,EAACD,KAAKE,OAAO,GAAG;YAC/BJ,sBAAsB,CAACC,MAAM,GAAG;QAClC;IACF;IAEA,OAAOD;AACT;AAEO,eAAevF,cACpB4F,KAGC,EACDxD,SAAgC,EAChC,EACEF,QAAQ,EACR2D,OAAO,EACPC,QAAQ,EACRC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBhE,WAAW,IAAI,EAWhB;QAySEyD,YAWoBM;IAlTvB,MAAME,gBAAgB,CAACC;QACrB,MAAMtF,OAAOuF,IAAAA,oBAAW,EAACD;QACzB,OAAOE,IAAAA,iBAAK,EAACC,IAAAA,gBAAI,EAACzF;IACpB;IAEA,MAAM0F,eAAe;IACrB,MAAMC,oBAAoB,CAACC;QACzB,MAAMC,WAAW,GAAGD,UAAU,GAAG,CAAC;QAClC,uBAAuB;QACvB,IAAIA,YAAY,MAAM,OAAOE,IAAAA,iBAAK,EAACD;QACnC,yBAAyB;QACzB,IAAID,YAAY,MAAM,OAAOG,IAAAA,kBAAM,EAACF;QACpC,oBAAoB;QACpB,OAAOG,IAAAA,eAAG,EAACP,IAAAA,gBAAI,EAACI;IAClB;IAEA,MAAMI,eAAe,CAACC,WACpBA,QACE,qBAAqB;SACpBC,OAAO,CAAC,aAAa,GACtB,kCAAkC;SACjCA,OAAO,CAAC,cAAc,SACvB,mBAAmB;SAClBA,OAAO,CAAC,6CAA6C;IAE1D,iCAAiC;IACjC,MAAMjC,eAAe,CAAC,CACpBa,CAAAA,YAAa,MAAMqB,IAAAA,0BAAY,EAACrB,UAAU,SAASC,gBAAgB,MAAM;IAG3E,gEAAgE;IAChE,MAAMqB,cAAc,IAAIhG;IAExB,MAAMiG,WAAuC,EAAE;IAE/C,MAAM7D,QAAQ,MAAMhF,oBAClB;QAAEgE,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC,UACAC;IAGF,MAAMkF,gBAAgB,OAAO,EAC3BvC,IAAI,EACJwC,UAAU,EAIX;YAiLyB/D,0BACJA;QAjLpB,MAAMgE,gBAAgB1C,kBAAkBC,MAAMwC,YAAYtC;QAC1D,IAAIuC,cAAcC,MAAM,KAAK,GAAG;YAC9B;QACF;QAEAJ,SAAShD,IAAI,CACX;YACEkD,eAAe,QAAQ,gBAAgB;YACvC;YACA;SACD,CAAC5E,GAAG,CAAC,CAAC+E,QAAUC,IAAAA,qBAAS,EAACD;QAG7BF,cAAcI,OAAO,CAAC,CAACC,MAAMC,GAAGC;gBAc3B1E,4BA6DD2C,2BAoBE3C;YA9FJ,MAAM2E,SACJF,MAAM,IACFC,IAAIN,MAAM,KAAK,IACb,MACA,MACFK,MAAMC,IAAIN,MAAM,GAAG,IACjB,MACA;YAER,MAAMpE,WAAWjB,UAAUY,GAAG,CAAC6E;YAC/B,MAAMI,WAAWjC,cAAckC,aAAa,CAACC,QAAQ,CAACN;YACtD,MAAMO,gBACJ,AAAC/E,CAAAA,CAAAA,4BAAAA,SAAUgF,YAAY,KAAI,CAAA,IAC1BhF,CAAAA,CAAAA,6BAAAA,6BAAAA,SAAUiF,gBAAgB,qBAA1BjF,2BAA4BzB,MAAM,CAAC,CAACP,GAAGC,IAAMD,IAAKC,CAAAA,KAAK,CAAA,GAAI,OAAM,CAAA;YAEpE,IAAIiH;YAEJ,IAAIV,SAAS,WAAWA,SAAS,gBAAgB;gBAC/CU,SAAS;YACX,OAAO,IAAI7C,IAAAA,4BAAa,EAACrC,4BAAAA,SAAUsC,OAAO,GAAG;gBAC3C4C,SAAS;YACX,OAAO,IAAIlF,4BAAAA,SAAUmF,iBAAiB,EAAE;gBACtC,IACE,2EAA2E;gBAC3EnF,CAAAA,4BAAAA,SAAUoF,eAAe,KACzB,qEAAqE;gBACrE,0DAA0D;gBACzDpF,SAASqF,iBAAiB,IAAI,CAACrF,SAASsF,YAAY,EACrD;oBACAJ,SAAS;gBACX,OAAO,IAAI,EAAClF,4BAAAA,SAAUsF,YAAY,GAAE;oBAClCJ,SAAS;gBACX,OAAO;oBACLA,SAAS;gBACX;YACF,OAAO,IAAIlF,4BAAAA,SAAUuF,QAAQ,EAAE;gBAC7BL,SAAS;YACX,OAAO,IAAIlF,4BAAAA,SAAUwF,KAAK,EAAE;gBAC1BN,SAAS;YACX,OAAO;gBACLA,SAAS;YACX;YAEAnB,YAAY0B,GAAG,CAACP;YAEhB,IAAIlF,4BAAAA,SAAU0F,wBAAwB,EAAE3B,YAAY0B,GAAG,CAAC;YAExDzB,SAAShD,IAAI,CAAC;gBACZ,GAAG2D,OAAO,CAAC,EAAEO,OAAO,CAAC,EACnBlF,CAAAA,4BAAAA,SAAU0F,wBAAwB,IAC9B,GAAGlB,KAAK,OAAO,EAAExE,4BAAAA,SAAU0F,wBAAwB,CAAC,SAAS,CAAC,GAC9DlB,OAEJO,gBAAgB3B,eACZ,CAAC,EAAE,EAAEC,kBAAkB0B,eAAe,CAAC,CAAC,GACxC,IACJ;gBACF/E,WACI4E,WACEe,IAAAA,gBAAI,EAAC,SACL3F,SAAStC,IAAI,IAAI,IACfuF,IAAAA,oBAAW,EAACjD,SAAStC,IAAI,IACzB,KACJ;gBACJsC,WACI4E,WACEe,IAAAA,gBAAI,EAAC,SACL3F,SAAStC,IAAI,IAAI,IACfqF,cAAc/C,SAAS4F,SAAS,IAChC,KACJ;aACL;YAED,MAAMC,iBACJlD,EAAAA,4BAAAA,cAAc/C,KAAK,CAAC4E,KAAK,qBAAzB7B,0BAA2BzE,MAAM,CAC/B,CAACd;oBAEC+C;uBADA/C,KAAK0I,QAAQ,CAAC,aACd3F,2BAAAA,MAAMiB,MAAM,CAAC8C,WAAW,qBAAxB/D,yBAA0BtD,MAAM,CAACmC,KAAK,CAAC8F,QAAQ,CAAC1H;mBAC/C,EAAE;YAET,IAAIyI,eAAezB,MAAM,GAAG,GAAG;gBAC7B,MAAM2B,aAAatB,MAAMC,IAAIN,MAAM,GAAG,IAAI,MAAM;gBAEhDyB,eAAetB,OAAO,CAAC,CAACnH,MAAM4I,OAAO,EAAE5B,MAAM,EAAE;oBAC7C,MAAM6B,cAAcD,UAAU5B,SAAS,IAAI,MAAM;oBACjD,MAAM1G,OAAOyC,MAAMmB,KAAK,CAAC3B,GAAG,CAACvC;oBAC7B4G,SAAShD,IAAI,CAAC;wBACZ,GAAG+E,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAEtC,aAAavG,OAAO;wBACtD,OAAOM,SAAS,WAAWuF,IAAAA,oBAAW,EAACvF,QAAQ;wBAC/C;qBACD;gBACH;YACF;YAEA,IAAIsC,6BAAAA,0BAAAA,SAAUkG,aAAa,qBAAvBlG,wBAAyBoE,MAAM,EAAE;gBACnC,MAAM+B,cAAcnG,SAASkG,aAAa,CAAC9B,MAAM;gBACjD,MAAM2B,aAAatB,MAAMC,IAAIN,MAAM,GAAG,IAAI,MAAM;gBAEhD,IAAIgC;gBACJ,IACEpG,SAASiF,gBAAgB,IACzBjF,SAASiF,gBAAgB,CAACoB,IAAI,CAAC,CAACC,IAAMA,IAAIlD,eAC1C;oBACA,MAAMmD,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnE,MAAMO,qBAAqB1G,SAASkG,aAAa,CAC9C5G,GAAG,CAAC,CAAC6C,OAAOwE,MAAS,CAAA;4BACpBxE;4BACAoB,UAAUvD,SAASiF,gBAAgB,AAAC,CAAC0B,IAAI,IAAI;wBAC/C,CAAA,GACC5E,IAAI,CAAC,CAAC,EAAEwB,UAAUvF,CAAC,EAAE,EAAE,EAAEuF,UAAUtF,CAAC,EAAE,GACrC,mBAAmB;wBACnB,wDAAwD;wBACxDD,KAAKoF,gBAAgBnF,KAAKmF,eAAe,IAAInF,IAAID;oBAErDoI,SAASM,mBAAmB5E,KAAK,CAAC,GAAGyE;oBACrC,MAAMK,kBAAkBF,mBAAmB5E,KAAK,CAACyE;oBACjD,IAAIK,gBAAgBxC,MAAM,EAAE;wBAC1B,MAAMyC,YAAYD,gBAAgBxC,MAAM;wBACxC,MAAM0C,cAAcN,KAAKO,KAAK,CAC5BH,gBAAgBrI,MAAM,CACpB,CAAC0C,OAAO,EAAEsC,QAAQ,EAAE,GAAKtC,QAAQsC,UACjC,KACEqD,gBAAgBxC,MAAM;wBAE5BgC,OAAOpF,IAAI,CAAC;4BACVmB,OAAO,CAAC,EAAE,EAAE0E,UAAU,YAAY,CAAC;4BACnCtD,UAAU;4BACVuD;wBACF;oBACF;gBACF,OAAO;oBACL,MAAMP,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnEC,SAASpG,SAASkG,aAAa,CAC5BpE,KAAK,CAAC,GAAGyE,cACTjH,GAAG,CAAC,CAAC6C,QAAW,CAAA;4BAAEA;4BAAOoB,UAAU;wBAAE,CAAA;oBACxC,IAAI4C,cAAcI,cAAc;wBAC9B,MAAMM,YAAYV,cAAcI;wBAChCH,OAAOpF,IAAI,CAAC;4BAAEmB,OAAO,CAAC,EAAE,EAAE0E,UAAU,YAAY,CAAC;4BAAEtD,UAAU;wBAAE;oBACjE;gBACF;gBAEA6C,OAAO7B,OAAO,CACZ,CAAC,EAAEpC,KAAK,EAAEoB,QAAQ,EAAEuD,WAAW,EAAE,EAAEd,OAAO,EAAE5B,MAAM,EAAE;oBAClD,MAAM6B,cAAcD,UAAU5B,SAAS,IAAI,MAAM;oBACjDJ,SAAShD,IAAI,CAAC;wBACZ,GAAG+E,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAE9D,QAChCoB,WAAWH,eACP,CAAC,EAAE,EAAEC,kBAAkBE,UAAU,CAAC,CAAC,GACnC,KAEJuD,eAAeA,cAAc1D,eACzB,CAAC,MAAM,EAAEC,kBAAkByD,aAAa,CAAC,CAAC,GAC1C,IACJ;wBACF;wBACA;qBACD;gBACH;YAEJ;QACF;QAEA,MAAME,mBAAkB7G,2BAAAA,MAAMiB,MAAM,CAAC8C,WAAW,qBAAxB/D,yBAA0BgB,MAAM,CAACzD,IAAI,CAACuD,KAAK;QACnE,MAAMgG,cAAc9G,EAAAA,4BAAAA,MAAMiB,MAAM,CAAC8C,WAAW,qBAAxB/D,0BAA0BgB,MAAM,CAACnC,KAAK,KAAI,EAAE;QAEhEgF,SAAShD,IAAI,CAAC;YACZ;YACA,OAAOgG,oBAAoB,WAAWjE,cAAciE,mBAAmB;YACvE;SACD;QACD,MAAME,iBAA2B,EAAE;QACnC,MAAMC,iBAAiB;eAClBF,YACA/I,MAAM,CAAC,CAACd;gBACP,IAAIA,KAAK0I,QAAQ,CAAC,SAAS;oBACzBoB,eAAelG,IAAI,CAAC5D;oBACpB,OAAO;gBACT;gBACA,OAAO;YACT,GACCkC,GAAG,CAAC,CAACuC,IAAMA,EAAEgC,OAAO,CAACrB,SAAS,cAC9BT,IAAI;eACJmF,eAAe5H,GAAG,CAAC,CAACuC,IAAMA,EAAEgC,OAAO,CAACrB,SAAS,cAAcT,IAAI;SACnE;QAED,0GAA0G;QAC1G,MAAMqF,aAAa,KAAK;QACxB,IAAIC,gBAAgB;QACpB,IAAIC,iBAAiB;QACrBH,eAAe5C,OAAO,CAAC,CAACX,UAAUoC,OAAO,EAAE5B,MAAM,EAAE;YACjD,MAAM6B,cAAcD,QAAQsB,mBAAmBlD,SAAS,IAAI,MAAM;YAElE,MAAMmD,eAAe3D,SAASC,OAAO,CAAC,aAAarB;YACnD,MAAMgF,YAAY7D,aAAaC;YAC/B,MAAMlG,OAAOyC,MAAMmB,KAAK,CAAC3B,GAAG,CAAC4H;YAE7B,IAAI,CAAC7J,QAAQA,OAAO0J,YAAY;gBAC9BE;gBACAD,iBAAiB3J,QAAQ;gBACzB;YACF;YAEAsG,SAAShD,IAAI,CAAC;gBAAC,CAAC,EAAE,EAAEiF,YAAY,CAAC,EAAEuB,WAAW;gBAAEvE,IAAAA,oBAAW,EAACvF;gBAAO;aAAG;QACxE;QAEA,IAAI4J,iBAAiB,GAAG;YACtBtD,SAAShD,IAAI,CAAC;gBACZ,CAAC,+BAA+B,CAAC;gBACjCiC,IAAAA,oBAAW,EAACoE;gBACZ;aACD;QACH;IACF;IAEA,yDAAyD;IACzD,IAAI9E,MAAMnD,GAAG,IAAIe,MAAMiB,MAAM,CAAChC,GAAG,EAAE;QACjC,MAAM6E,cAAc;YAClBC,YAAY;YACZxC,MAAMa,MAAMnD,GAAG;QACjB;QAEA4E,SAAShD,IAAI,CAAC;YAAC;YAAI;YAAI;SAAG;IAC5B;IAEAjC,UAAUU,GAAG,CAAC,QAAQ;QACpB,GAAIV,UAAUY,GAAG,CAAC,WAAWZ,UAAUY,GAAG,CAAC,UAAU;QACrD4F,UAAUzC;IACZ;IAEA,uFAAuF;IACvF,IACE,CAACP,MAAM3C,KAAK,CAACkF,QAAQ,CAAC,WACtB,GAACvC,aAAAA,MAAMnD,GAAG,qBAATmD,WAAWuC,QAAQ,CAAC2C,sCAA0B,IAC/C;QACAlF,MAAM3C,KAAK,GAAG;eAAI2C,MAAM3C,KAAK;YAAE;SAAO;IACxC;IAEA,+CAA+C;IAC/C,MAAMqE,cAAc;QAClBC,YAAY;QACZxC,MAAMa,MAAM3C,KAAK;IACnB;IAEA,MAAM8H,kBAAiB7E,iCAAAA,mBAAmB8E,UAAU,qBAA7B9E,8BAA+B,CAAC,IAAI;IAC3D,IAAI6E,CAAAA,kCAAAA,eAAgB1I,KAAK,CAACoF,MAAM,IAAG,GAAG;QACpC,MAAMwD,kBAAkB,MAAMxH,QAAQC,GAAG,CACvCqH,eAAe1I,KAAK,CACjBM,GAAG,CAAC,CAACuI,MAAQ,GAAGhJ,SAAS,CAAC,EAAEgJ,KAAK,EACjCvI,GAAG,CAACR,WAAW3B,aAAaS;QAGjCoG,SAAShD,IAAI,CAAC;YAAC;YAAI;YAAI;SAAG;QAC1BgD,SAAShD,IAAI,CAAC;YAAC;YAAgB+B,cAAczE,IAAIsJ;YAAmB;SAAG;IACzE;IAEA9K,MACEgL,IAAAA,kBAAS,EAAC9D,UAAU;QAClB+D,OAAO;YAAC;YAAK;YAAK;SAAI;QACtBC,cAAc,CAACC,MAAQC,IAAAA,kBAAS,EAACD,KAAK7D,MAAM;IAC9C;IAGF,MAAM+D,qBACJ5F,MAAMnD,GAAG,IAAIe,MAAMiB,MAAM,CAAChC,GAAG,GAAG,yBAAyB;IAC3DtC;IACAA,MACEgL,IAAAA,kBAAS,EACP;QACE/D,YAAY3F,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACD2F,YAAY3F,GAAG,CAAC,QAAQ;YACtB;YACA;YACA,CAAC,iCAAiC,EAAEuH,IAAAA,gBAAI,EAACwC,oBAAoB,CAAC,CAAC;SAChE;QACDpE,YAAY3F,GAAG,CAAC,UAAU;YACxB;YACA;YACA,CAAC,oDAAoD,EAAEuH,IAAAA,gBAAI,EACzDwC,oBACA,CAAC,CAAC;SACL;QACDpE,YAAY3F,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACD2F,YAAY3F,GAAG,CAAC,QAAQ;YAAC;YAAK;YAAa,CAAC,yBAAyB,CAAC;SAAC;KACxE,CAACF,MAAM,CAAC,CAACC,IAAMA,IAChB;QACE4J,OAAO;YAAC;YAAK;YAAK;SAAI;QACtBC,cAAc,CAACC,MAAQC,IAAAA,kBAAS,EAACD,KAAK7D,MAAM;IAC9C;IAIJtH;AACF;AAEO,SAASJ,kBAAkB,EAChC0L,SAAS,EACTC,QAAQ,EACRC,OAAO,EACM;IACb,MAAMC,cAAc,CAClBnC,QACAoC;QAEA,MAAMC,cAAcD,SAAS;QAC7B,MAAME,YAAYF,SAAS;QAC3B1L,MAAMwH,IAAAA,qBAAS,EAACkE;QAEhB;;;;KAIC,GACD,MAAMG,YAAY,AAACvC,OAChB9G,GAAG,CAAC,CAAC6C;YACJ,IAAIyG,WAAW,CAAC,UAAU,EAAEzG,MAAM0G,MAAM,CAAC,EAAE,CAAC;YAE5C,IAAI,CAACH,WAAW;gBACd,MAAMI,IAAI3G;gBACVyG,YAAY,GAAGH,cAAc,MAAM,IAAI,cAAc,EACnDK,EAAEC,WAAW,CACd,EAAE,CAAC;YACN;YACA,IAAIN,aAAa;gBACf,MAAMK,IAAI3G;gBACVyG,YAAY,CAAC,EAAE,EACbE,EAAEE,UAAU,GACR,CAAC,QAAQ,EAAEF,EAAEE,UAAU,EAAE,GACzB,CAAC,WAAW,EAAEF,EAAEG,SAAS,EAAE,CAChC,EAAE,CAAC;YACN;YAEA,IAAIP,WAAW;gBACb,MAAMI,IAAI3G;gBACVyG,YAAY,CAAC,YAAY,CAAC;gBAE1B,IAAK,IAAInE,IAAI,GAAGA,IAAIqE,EAAER,OAAO,CAAClE,MAAM,EAAEK,IAAK;oBACzC,MAAMyE,SAASJ,EAAER,OAAO,CAAC7D,EAAE;oBAC3B,MAAM0E,OAAO1E,MAAM6D,QAAQlE,MAAM,GAAG;oBAEpCwE,YAAY,CAAC,EAAE,EAAEO,OAAO,MAAM,IAAI,CAAC,EAAED,OAAO3J,GAAG,CAAC,EAAE,EAAE2J,OAAOE,KAAK,CAAC,EAAE,CAAC;gBACtE;YACF;YAEA,OAAOR;QACT,GACCnI,IAAI,CAAC;QAER3D,MAAM,GAAG6L,UAAU,EAAE,CAAC;IACxB;IAEA7L;IACA,IAAIsL,UAAUhE,MAAM,EAAE;QACpBmE,YAAYH,WAAW;IACzB;IACA,IAAIE,QAAQlE,MAAM,EAAE;QAClBmE,YAAYD,SAAS;IACvB;IAEA,MAAMe,mBAAmB;WACpBhB,SAASiB,WAAW;WACpBjB,SAASkB,UAAU;WACnBlB,SAASmB,QAAQ;KACrB;IACD,IAAIH,iBAAiBjF,MAAM,EAAE;QAC3BmE,YAAYc,kBAAkB;IAChC;AACF;AAEO,eAAe7N,kBACpB0I,UAAuB,EACvBuF,IAAY,EACZ5K,QAAgB,EAChB8D,aAA4B,EAC5BC,gBAAmC,EACnC9D,WAAoB,IAAI,EACxB4K,WAAwC;IAExC,MAAMC,eAAezF,eAAe,UAAUvB,gBAAgBC;IAC9D,IAAI,CAAC+G,cAAc;QACjB,MAAM,IAAIC,MAAM;IAClB;IAEA,kCAAkC;IAClC,IAAI1F,eAAe,OAAO;QACxByF,aAAa/J,KAAK,GAAGX,OAAO2B,OAAO,CAAC+I,aAAa/J,KAAK,EAAErB,MAAM,CAC5D,CAACwC,KAA+B,CAACxB,KAAK6J,MAAM;YAC1C,MAAMS,SAASC,IAAAA,0BAAgB,EAACvK;YAChCwB,GAAG,CAAC8I,OAAO,GAAGT;YACd,OAAOrI;QACT,GACA,CAAC;IAEL;IAEA,oDAAoD;IACpD,MAAMZ,QACJuJ,eACC,MAAMvO,oBACL;QAAEgE,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC;IAGJ,MAAMiL,WAAW5J,MAAMiB,MAAM,CAAC8C,WAAW;IACzC,IAAI,CAAC6F,UAAU;QACb,kEAAkE;QAClE,MAAM,IAAIH,MAAM;IAClB;IAEA,MAAMI,WACJ9F,eAAe,UACX+F,IAAAA,wCAAmB,EAACR,QACpBS,IAAAA,0CAAsB,EAACT;IAE7B,MAAMU,aAAa,CAAC9F,QAAkBA,MAAMyB,QAAQ,CAAC;IAErD,MAAMsE,YAAY,AAACT,CAAAA,aAAa/J,KAAK,CAACoK,SAAS,IAAI,EAAE,AAAD,EAAG9L,MAAM,CAACiM;IAC9D,MAAME,WAAW,AAACV,CAAAA,aAAa/J,KAAK,CAAC,QAAQ,IAAI,EAAE,AAAD,EAAG1B,MAAM,CAACiM;IAE5D,MAAMG,gBAAgB,CAACzC,MAAgB,GAAGhJ,SAAS,CAAC,EAAEgJ,KAAK;IAE3D,MAAM0C,eAAe1N,OAAOuN,WAAWC,UAAU/K,GAAG,CAACgL;IACrD,MAAME,gBAAgBlP,WACpB,mEAAmE;IACnE+C,UAAU+L,WAAWL,SAASlN,MAAM,CAACmC,KAAK,GAC1C,gCAAgC;IAChC+K,SAAS5I,MAAM,CAACnC,KAAK,EACrBM,GAAG,CAACgL;IAEN,MAAMpK,UAAUpB,WAAW3B,aAAaS;IAExC,2EAA2E;IAC3E,eAAe;IACf,MAAM6M,gBAAgB,OAAOrN;QAC3B,MAAMmC,MAAMnC,KAAK0E,KAAK,CAACjD,SAASuF,MAAM,GAAG;QACzC,MAAM1G,OAA2ByC,MAAMmB,KAAK,CAAC3B,GAAG,CAACJ;QAEjD,oEAAoE;QACpE,YAAY;QACZ,IAAI,OAAO7B,SAAS,UAAU;YAC5B,OAAOwC,QAAQ9C;QACjB;QAEA,OAAOM;IACT;IAEA,IAAI;QACF,0EAA0E;QAC1E,kEAAkE;QAClE,MAAMgN,eAAepM,IAAI,MAAM8B,QAAQC,GAAG,CAACkK,aAAajL,GAAG,CAACmL;QAC5D,MAAME,gBAAgBrM,IACpB,MAAM8B,QAAQC,GAAG,CAACmK,cAAclL,GAAG,CAACmL;QAGtC,OAAO;YAACE;YAAeD;SAAa;IACtC,EAAE,OAAM,CAAC;IACT,OAAO;QAAC,CAAC;QAAG,CAAC;KAAE;AACjB;AAqBO,eAAe1P,iBAAiB,EACrCyO,IAAI,EACJmB,cAAc,EACdC,iBAAiB,EACjBC,cAAc,EACdC,OAAO,EACPC,aAAa,EACbC,MAAM,EASP;IACC,MAAMC,oBAAwC,EAAE;IAChD,MAAMC,cAAcC,IAAAA,yBAAa,EAAC3B;IAClC,MAAM4B,gBAAgBC,IAAAA,6BAAe,EAACH;IAEtC,0CAA0C;IAC1C,MAAMI,qBAAqBtM,OAAOqB,IAAI,CAAC+K,cAAc5B;IAErD,IAAI,CAACoB,mBAAmB;QACtB,IAAID,gBAAgB;YAClBC,oBAAoB,MAAMD,eAAe;gBAAEG;gBAASC;YAAc;QACpE,OAAO;YACL,MAAM,IAAIpB,MACR,CAAC,yFAAyF,EAAEH,MAAM;QAEtG;IACF;IAEA,MAAM+B,oBACJ,CAAC,4CAA4C,CAAC,GAC9C,CAAC,qFAAqF,CAAC;IAEzF,IACE,CAACX,qBACD,OAAOA,sBAAsB,YAC7BY,MAAMC,OAAO,CAACb,oBACd;QACA,MAAM,IAAIjB,MACR,CAAC,8CAA8C,EAAEH,KAAK,WAAW,EAAE,OAAOoB,kBAAkB,CAAC,EAAEW,mBAAmB;IAEtH;IAEA,MAAMG,wBAAwB1M,OAAOqB,IAAI,CAACuK,mBAAmB3M,MAAM,CACjE,CAACqB,MAAQ,CAAEA,CAAAA,QAAQ,WAAWA,QAAQ,UAAS;IAGjD,IAAIoM,sBAAsBvH,MAAM,GAAG,GAAG;QACpC,MAAM,IAAIwF,MACR,CAAC,2CAA2C,EAAEH,KAAK,EAAE,EAAEkC,sBAAsBlL,IAAI,CAC/E,MACA,EAAE,EAAE+K,mBAAmB;IAE7B;IAEA,IACE,CACE,CAAA,OAAOX,kBAAkBrB,QAAQ,KAAK,aACtCqB,kBAAkBrB,QAAQ,KAAK,UAAS,GAE1C;QACA,MAAM,IAAII,MACR,CAAC,6DAA6D,EAAEH,KAAK,GAAG,CAAC,GACvE+B;IAEN;IAEA,MAAMI,cAAcf,kBAAkBgB,KAAK;IAE3C,IAAI,CAACJ,MAAMC,OAAO,CAACE,cAAc;QAC/B,MAAM,IAAIhC,MACR,CAAC,wDAAwD,EAAEH,KAAK,GAAG,CAAC,GAClE,CAAC,2FAA2F,CAAC;IAEnG;IAEAmC,YAAYrH,OAAO,CAAC,CAACF;QACnB,uEAAuE;QACvE,SAAS;QACT,IAAI,OAAOA,UAAU,UAAU;YAC7BA,QAAQyH,IAAAA,wCAAmB,EAACzH;YAE5B,MAAM0H,mBAAmBC,IAAAA,wCAAmB,EAAC3H,OAAO0G;YACpD,IAAIkB,eAAe5H;YAEnB,IAAI0H,iBAAiBG,cAAc,EAAE;gBACnCD,eAAe5H,MAAMvC,KAAK,CAACiK,iBAAiBG,cAAc,CAAC9H,MAAM,GAAG;YACtE,OAAO,IAAI4G,eAAe;gBACxB3G,QAAQ,CAAC,CAAC,EAAE2G,gBAAgB3G,OAAO;YACrC;YAEA,MAAM8H,SAASd,cAAcY;YAC7B,IAAI,CAACE,QAAQ;gBACX,MAAM,IAAIvC,MACR,CAAC,oBAAoB,EAAEqC,aAAa,8BAA8B,EAAExC,KAAK,GAAG,CAAC;YAEjF;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,aAAa;YACbyB,kBAAkBlK,IAAI,CAAC;gBACrBR,MAAM6D,MACH+H,KAAK,CAAC,KACN9M,GAAG,CAAC,CAAC+M,UACJC,IAAAA,6BAAoB,EAACC,mBAAmBF,UAAU,OAEnD5L,IAAI,CAAC;gBACR+L,SAASnI;gBACToI,qBAAqBpL;YACvB;QACF,OAGK;YACH,MAAMqL,cAAczN,OAAOqB,IAAI,CAAC+D,OAAOnG,MAAM,CAC3C,CAACqB,MAAQA,QAAQ,YAAYA,QAAQ;YAGvC,IAAImN,YAAYtI,MAAM,EAAE;gBACtB,MAAM,IAAIwF,MACR,CAAC,+DAA+D,EAAEH,KAAK,GAAG,CAAC,GACzE,CAAC,6FAA6F,CAAC,GAC/F,CAAC,yBAAyB,EAAE8B,mBACzBjM,GAAG,CAAC,CAACqN,IAAM,GAAGA,EAAE,KAAK,CAAC,EACtBlM,IAAI,CAAC,MAAM,IAAI,CAAC,GACnB,CAAC,gCAAgC,EAAEiM,YAAYjM,IAAI,CAAC,MAAM,GAAG,CAAC;YAEpE;YAEA,MAAM,EAAEmM,SAAS,CAAC,CAAC,EAAE,GAAGvI;YACxB,IAAIwI,YAAYpD;YAChB,IAAIqD,mBAAmBrD;YAEvB8B,mBAAmBhH,OAAO,CAAC,CAACwI;gBAC1B,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAG9B,YAAY+B,MAAM,CAACH,cAAc;gBAC9D,IAAII,aAAaP,MAAM,CAACG,cAAc;gBACtC,IACEE,YACAL,OAAOQ,cAAc,CAACL,kBACrBI,CAAAA,eAAe,QACdA,eAAe9L,aACf,AAAC8L,eAAuB,KAAI,GAC9B;oBACAA,aAAa,EAAE;gBACjB;gBACA,IACE,AAACH,UAAU,CAACvB,MAAMC,OAAO,CAACyB,eACzB,CAACH,UAAU,OAAOG,eAAe,UAClC;oBACA,mEAAmE;oBACnE,wBAAwB;oBACxB,IAAIlC,UAAU,OAAOkC,eAAe,aAAa;wBAC/CN,YAAY;wBACZC,mBAAmB;wBACnB;oBACF;oBAEA,MAAM,IAAIlD,MACR,CAAC,sBAAsB,EAAEmD,cAAc,sBAAsB,EAC3DC,SAAS,aAAa,WACvB,UAAU,EAAE,OAAOG,WAAW,IAAI,EACjClC,SAAS,yBAAyB,iBACnC,KAAK,EAAExB,MAAM;gBAElB;gBACA,IAAI4D,WAAW,CAAC,CAAC,EAAEL,SAAS,QAAQ,KAAKD,cAAc,CAAC,CAAC;gBACzD,IAAIE,UAAU;oBACZI,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC;gBAC5B;gBACAR,YAAYA,UACThJ,OAAO,CACNwJ,UACAL,SACI,AAACG,WACE7N,GAAG,CAAC,CAAC+M,UAAYC,IAAAA,6BAAoB,EAACD,SAAS,OAC/C5L,IAAI,CAAC,OACR6L,IAAAA,6BAAoB,EAACa,YAAsB,OAEhDtJ,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,YAAY;gBAEvBiJ,mBAAmBA,iBAChBjJ,OAAO,CACNwJ,UACAL,SACI,AAACG,WAAwB7N,GAAG,CAACgO,oBAAoB7M,IAAI,CAAC,OACtD6M,mBAAmBH,aAExBtJ,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,YAAY;YACzB;YAEA,IAAI,CAACgJ,aAAa,CAACC,kBAAkB;gBACnC;YACF;YAEA,IAAIzI,MAAMkJ,MAAM,IAAI,EAACxC,2BAAAA,QAASjG,QAAQ,CAACT,MAAMkJ,MAAM,IAAG;gBACpD,MAAM,IAAI3D,MACR,CAAC,gDAAgD,EAAEH,KAAK,aAAa,EAAEpF,MAAMkJ,MAAM,CAAC,qBAAqB,EAAEzC,gBAAgB;YAE/H;YACA,MAAM0C,YAAYnJ,MAAMkJ,MAAM,IAAIvC,iBAAiB;YAEnDE,kBAAkBlK,IAAI,CAAC;gBACrBR,MAAM,GAAGgN,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KACrCA,aAAaX,cAAc,MAAM,KAAKA,WACtC;gBACFL,SAAS,GAAGgB,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KACxCA,aAAaV,qBAAqB,MAAM,KAAKA,kBAC7C;gBACFL,qBAAqBpL;YACvB;QACF;IACF;IAEA,MAAMoM,OAAO,IAAI1P;IAEjB,OAAO;QACL2P,cAAcC,IAAAA,gCAAsB,EAAC9C,kBAAkBrB,QAAQ;QAC/D0B,mBAAmBA,kBAAkBhN,MAAM,CAAC,CAACiE;YAC3C,IAAIsL,KAAKrP,GAAG,CAAC+D,MAAM3B,IAAI,GAAG,OAAO;YAEjC,8BAA8B;YAC9BiN,KAAKhI,GAAG,CAACtD,MAAM3B,IAAI;YACnB,OAAO;QACT;IACF;AACF;AAMO,eAAezF,oBAAoB,EACxC6S,GAAG,EACHnE,IAAI,EACJoE,OAAO,EACPC,SAAS,EACTC,cAAc,EACdjD,cAAc,EACdkD,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZrJ,iBAAiB,EACjB3C,OAAO,EAqBR;IACC,IACEwL,SAAS3H,IAAI,CAAC,CAACoI;YAAaA;eAAAA,EAAAA,mBAAAA,SAASC,MAAM,qBAAfD,iBAAiBE,aAAa,MAAK;UAC/DJ,qBAAqB,UACrB;QACA,MAAM,IAAI3E,MACR;IAEJ;IAEA4E,aAAaI,UAAU;IAEvB,IAAIC;IACJ,IAAIX,cAAc;QAChBW,kBAAkBC,IAAAA,8BAAc,EAC9B,MAAM,MAAM,CAACC,IAAAA,gDAAuB,EAACnB,KAAKM,eAAec,IAAI,CAC3D,CAACC,MAAQA,IAAIC,OAAO,IAAID;IAG9B;IAEA,MAAME,mBAAmB,IAAIC,kCAAgB,CAAC;QAC5C5R,IAAI6R,qBAAM;QACVC,KAAK;QACLxB;QACAyB,aAAatB;QACbuB,eAAehP,aAAI,CAACC,IAAI,CAACoN,SAAS;QAClCS;QACAD;QACAoB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS,CAAC;gBACVtJ,QAAQ,CAAC;gBACTuJ,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;YACX,CAAA;QACAhB;QACAT;QACA0B,aAAaC,QAAcC,cAAc;IAC3C;IAEA,MAAMC,YAAY,IAAIlS;IAEtB,MAAMmS,kBAAkB,IAAInS;IAC5B,KAAK,MAAMsO,WAAW2B,SAAU;QAC9B,IAAI3B,QAAQ8D,KAAK,EAAE;gBAGb9D;YAFJ4D,UAAUxK,GAAG,CAAC4G,QAAQ8D,KAAK;YAE3B,IAAI9D,EAAAA,kBAAAA,QAAQqC,MAAM,qBAAdrC,gBAAgBsC,aAAa,MAAK,OAAO;gBAC3CuB,gBAAgBzK,GAAG,CAAC4G,QAAQ8D,KAAK;YACnC;QACF;IACF;IAEA,MAAMC,cAAc,IAAIC,yBAAW;IAEnC,MAAMC,QAAQC,IAAAA,0BAAe,EAAC;QAC5B9G;QACA,sEAAsE;QACtE,QAAQ;QACRgD,qBAAqB;QACrB+D,YAAY;YACVrB;YACAhB;YACAsC,yBAAyB;YACzBC,cAAc;YACdC,cAAc;gBACZ7C;gBACAC;YACF;YACA6C,WAAWR,YAAYS,OAAO,CAACD,SAAS;YACxCE,SAASV,YAAYS,OAAO,CAACC,OAAO;YACpCC,kBAAkBX,YAAYS,OAAO,CAACG,WAAW;YACjDxO;QACF;IACF;IAEA,MAAMyO,cAAc,MAAMzC,aAAa0C,gBAAgB,CAACC,GAAG,CACzDb,OACA;QACE,eAAec,iBACbC,gBAA0B,EAAE,EAC5B1K,MAAM,CAAC;YAEP,yDAAyD;YACzD,IAAIA,QAAQqH,SAAS5J,MAAM,EAAE,OAAOiN;YAEpC,MAAMC,UAAUtD,QAAQ,CAACrH,IAAI;YAE7B,IACE,OAAO2K,QAAQC,oBAAoB,KAAK,cACxC5K,MAAMqH,SAAS5J,MAAM,EACrB;gBACA,OAAOgN,iBAAiBC,eAAe1K,MAAM;YAC/C;YAEA,MAAMiG,SAAmB,EAAE;YAE3B,IAAI0E,QAAQC,oBAAoB,EAAE;oBAIrBD;gBAHX,oEAAoE;gBACpE,0EAA0E;gBAC1E,oCAAoC;gBACpC,IAAI,SAAOA,kBAAAA,QAAQ5C,MAAM,qBAAd4C,gBAAgBE,UAAU,MAAK,aAAa;oBACrDlB,MAAMkB,UAAU,GAAGF,QAAQ5C,MAAM,CAAC8C,UAAU;gBAC9C;gBAEA,IAAIH,cAAcjN,MAAM,GAAG,GAAG;oBAC5B,KAAK,MAAMqN,gBAAgBJ,cAAe;wBACxC,MAAMlF,SAAS,MAAMmF,QAAQC,oBAAoB,CAAC;4BAChD3E,QAAQ6E;wBACV;wBAEA,KAAK,MAAMjN,QAAQ2H,OAAQ;4BACzBS,OAAO5L,IAAI,CAAC;gCAAE,GAAGyQ,YAAY;gCAAE,GAAGjN,IAAI;4BAAC;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAM2H,SAAS,MAAMmF,QAAQC,oBAAoB,CAAC;wBAAE3E,QAAQ,CAAC;oBAAE;oBAE/DA,OAAO5L,IAAI,IAAImL;gBACjB;YACF;YAEA,IAAIxF,MAAMqH,SAAS5J,MAAM,EAAE;gBACzB,OAAOgN,iBAAiBxE,QAAQjG,MAAM;YACxC;YAEA,OAAOiG;QACT;QAEA,OAAOwE;IACT;IAGF,IAAIM,4CAA4C;IAChD,KAAK,MAAMrF,WAAW2B,SAAU;YAM5B3B;QALF,sEAAsE;QACtE,8BAA8B;QAC9B,IACEA,QAAQ8D,KAAK,IACb9D,QAAQsF,gBAAgB,IACxBtF,EAAAA,mBAAAA,QAAQqC,MAAM,qBAAdrC,iBAAgBsC,aAAa,MAAK,OAClC;YACA,KAAK,MAAM/B,UAAUqE,YAAa;gBAChC,IAAI5E,QAAQ8D,KAAK,IAAIvD,QAAQ;gBAE7B,MAAMgF,WAAWvF,QAAQwF,QAAQ,GAC7BrR,aAAI,CAACoR,QAAQ,CAAChE,KAAKvB,QAAQwF,QAAQ,IACnCxQ;gBAEJ,MAAM,IAAIuI,MACR,CAAC,SAAS,EAAEgI,SAAS,gDAAgD,EAAEvF,QAAQ8D,KAAK,CAAC,6CAA6C,CAAC;YAEvI;QACF;QAEA,IACE9D,QAAQsF,gBAAgB,IACxB,OAAOtF,QAAQkF,oBAAoB,KAAK,YACxC;YACAG,4CAA4C;QAC9C,OAAO,IAAI,OAAOrF,QAAQkF,oBAAoB,KAAK,YAAY;YAC7DG,4CAA4C;QAC9C;IACF;IAEA,6EAA6E;IAC7E,kEAAkE;IAClE,MAAMI,wBACJ7B,UAAUvS,IAAI,KAAK,KAClBuT,YAAY7M,MAAM,GAAG,KACpB6M,YAAYc,KAAK,CAAC,CAACnF;QACjB,KAAK,MAAMrN,OAAO0Q,UAAW;YAC3B,IAAI1Q,OAAOqN,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEJ,wEAAwE;IACxE,mEAAmE;IACnE,mCAAmC;IACnC,MAAM+B,gBAAgBX,SAAS+D,KAAK,CAClC,CAAC1F;YAAYA;eAAAA,EAAAA,kBAAAA,QAAQqC,MAAM,qBAAdrC,gBAAgBsC,aAAa,MAAK;;IAGjD,MAAMqD,6BACJF,yBAAyBG,QAAQC,GAAG,CAACC,QAAQ,KAAK;IAEpD,MAAMzE,eAAeiB,gBACjBqD,6BACE7M,oBACEiN,sBAAY,CAACC,SAAS,GACtBD,sBAAY,CAACE,sBAAsB,GACrCjR,YACF+Q,sBAAY,CAACG,SAAS;IAE1B,IAAIpG,SAAmC;QACrCuB;QACAxC,mBAAmBwG,4CACf,EAAE,GACFrQ;IACN;IAEA,IAAIyQ,yBAAyBpE,cAAc;QACzCvB,SAAS,MAAMnR,iBAAiB;YAC9B6P,mBAAmB;gBACjBrB,UAAUgJ,IAAAA,yCAA+B,EAAC9E;gBAC1C7B,OAAOoF,YAAY3R,GAAG,CAAC,CAACsN,SAAY,CAAA;wBAAEA;oBAAO,CAAA;YAC/C;YACAnD;YACAqB;YACAG,QAAQ;QACV;IACF;IAEA,sEAAsE;IACtE,uCAAuC;IACvC,IAAI9F,mBAAmB;QACrBgH,OAAOjB,iBAAiB,KAAK,EAAE;QAC/BiB,OAAOjB,iBAAiB,CAACuH,OAAO,CAAC;YAC/BjS,MAAMiJ;YACN+C,SAAS/C;YACTgD,qBAAqBiG,IAAAA,4BAAY,EAACjJ;QACpC;IACF;IAEA,MAAM2G,YAAYuC,YAAY;IAE9B,OAAOxG;AACT;AAiBO,eAAehQ,aAAa,EACjCyR,GAAG,EACHnE,IAAI,EACJoE,OAAO,EACP/C,cAAc,EACd8H,gBAAgB,EAChBC,gBAAgB,EAChB9H,OAAO,EACPC,aAAa,EACb8H,QAAQ,EACRC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRnF,SAAS,EACTC,cAAc,EACdmF,eAAe,EACfjF,cAAc,EACdI,kBAAkB,EAClBE,gBAAgB,EAChBL,YAAY,EACZiF,aAAa,EACbhF,iBAAiB,EACjBiF,SAAS,EACT5Q,OAAO,EA2BR;IACC,MAAM6Q,IAAAA,8CAAsB,EAAC;QAC3BnF;QACAiF;QACAtF;QACAD;QACAE;QACAyB,aAAatB;QACbqF,oBAAoBjF;IACtB;IAEA,MAAMkF,mBAAmBC,IAAAA,YAAK,EAAC,wBAAwBV;IACvD,OAAOS,iBACJE,YAAY,CAAC;QACZC,QAAQ,yCAAyCC,SAAS,CACxDf;QAEFgB,IAAAA,+CAA4B,EAAC;YAC3Bf;QACF;QAEA,IAAIgB;QACJ,IAAI3I;QACJ,IAAI4I;QACJ,IAAIC,YAA8B,CAAC;QACnC,IAAIC,oBAA6B;QACjC,MAAMC,oBAAoB5R,IAAAA,4BAAa,EAAC0Q;QAExC,IAAIkB,mBAAmB;YACrB,MAAM3R,UAAU,MAAM4R,IAAAA,0BAAiB,EAAC;gBACtCrI,OAAOmH,SAAShU,KAAK,CAACM,GAAG,CAAC,CAAClC,OAAiBoD,aAAI,CAACC,IAAI,CAACoN,SAASzQ;gBAC/D+W,mBAAmB;oBACjB,GAAGnB,QAAQ;oBACXoB,MAAM,AAACpB,CAAAA,SAASoB,IAAI,IAAI,EAAE,AAAD,EAAG9U,GAAG,CAAC,CAAC+U,UAA2B,CAAA;4BAC1D,GAAGA,OAAO;4BACVxC,UAAUrR,aAAI,CAACC,IAAI,CAACoN,SAASwG,QAAQxC,QAAQ;wBAC/C,CAAA;gBACF;gBACAyC,MAAMtB,SAASsB,IAAI;gBACnBC,UAAU;gBACV1G;YACF;YACA,MAAMoB,MAAM,AACV,CAAA,MAAM3M,QAAQuO,OAAO,CAAC2D,QAAQ,CAAC,CAAC,WAAW,EAAExB,SAASsB,IAAI,EAAE,CAAC,AAAD,EAC5D9F,YAAY;YAEd,qCAAqC;YACrC,MAAM7L,gBAAgB,CAAC;YAEvBqR,oBAAoBS,IAAAA,kCAAiB,EAACxF;YACtC4E,mBAAmB;gBACjBa,WAAWzF,IAAIC,OAAO;gBACtByF,UAAU1F,IAAI0F,QAAQ;gBACtBC,KAAK3F,IAAI2F,GAAG;gBACZC,aAAa5F,IAAI4F,WAAW;gBAC5BpL;gBACA+E,cAAcS;gBACd6F,YAAY7F,IAAIP,MAAM,IAAI,CAAC;gBAC3B/L;gBACAoS,uBAAuB,CAAC;gBACxBC,oBAAoB/F,IAAI+F,kBAAkB;gBAC1CpK,gBAAgBqE,IAAIrE,cAAc;gBAClCqK,gBAAgBhG,IAAIgG,cAAc;YACpC;QACF,OAAO;YACLpB,mBAAmB,MAAMqB,IAAAA,8BAAc,EAAC;gBACtCrH;gBACApE,MAAMyJ,mBAAmBzJ;gBACzB0L,WAAWlC,aAAa;gBACxBmC,OAAO;YACT;QACF;QACA,MAAMC,OAAOxB,iBAAiBa,SAAS;QACvC,IAAI7J;QAEJ,MAAMgK,cAA2BhB,iBAAiBgB,WAAW;QAE7D,IAAI1P,oBAA6B;QAEjC,IAAI8N,aAAa,OAAO;YACtB,MAAMzE,eAA8BqF,iBAAiBrF,YAAY;YAEjEwF,oBAAoBS,IAAAA,kCAAiB,EAACZ,iBAAiBrF,YAAY;YAEnE,IAAIR;YACJ,IAAI;gBACFA,WAAW,MAAMsH,IAAAA,4BAAe,EAACzB;YACnC,EAAE,OAAO0B,KAAK;gBACZ,MAAM,IAAI3L,MAAM,CAAC,oCAAoC,EAAEH,MAAM,EAAE;oBAC7D+L,OAAOD;gBACT;YACF;YAEAxB,YAAYnX,gBAAgB,MAAM0Y,IAAAA,4BAAe,EAACzB;YAElD,IAAIE,UAAU0B,OAAO,KAAK,kBAAkBxB,mBAAmB;gBAC7DyB,KAAIC,IAAI,CACN,CAAC,MAAM,EAAElM,KAAK,gKAAgK,CAAC;YAEnL;YAEA,uEAAuE;YACvE,wEAAwE;YACxE,uBAAuB;YACvBtE,oBACE0P,YAAYe,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,QAAQ,IAClD,CAACC,IAAAA,8CAA0B,EAACvM,SAC5BwM,IAAAA,2BAAsB,EAAC7C,WAAWW;YAEpC,uEAAuE;YACvE,mBAAmB;YACnB,yDAAyD;YACzD,IAAIA,UAAU0B,OAAO,KAAK,mBAAmB,CAACtQ,mBAAmB;gBAC/D4O,UAAUmC,UAAU,GAAG;YACzB;YAEA,IAAIC,IAAAA,yBAAc,EAAC1M,OAAO;;gBACtB,CAAA,EAAEiE,cAAcoG,qBAAqB,EAAE5I,iBAAiB,EAAE,GAC1D,MAAMnQ,oBAAoB;oBACxB6S;oBACAnE;oBACAqE;oBACAC;oBACAjD;oBACAkD;oBACAH;oBACAO,gBAAgB,CAAC;oBACjBH;oBACAI;oBACAH;oBACAC;oBACAK;oBACAD;oBACApJ;oBACA3C;gBACF,EAAC;YACL;QACF,OAAO;YACL,IAAI,CAAC6S,QAAQ,CAACe,IAAAA,2BAAkB,EAACf,SAAS,OAAOA,SAAS,UAAU;gBAClE,MAAM,IAAIzL,MAAM;YAClB;QACF;QAEA,MAAMyM,qBAAqB,CAAC,EAAChB,wBAAAA,KAAMiB,eAAe;QAClD,MAAMC,iBAAiB,CAAC,CAAC1C,iBAAiBoB,cAAc;QACxD,MAAMuB,iBAAiB,CAAC,CAAC3C,iBAAiBjJ,cAAc;QACxD,MAAM6L,iBAAiB,CAAC,CAAC5C,iBAAiBmB,kBAAkB;QAE5D,uEAAuE;QACvE,iBAAiB;QACjB,IAAIqB,sBAAsBE,gBAAgB;YACxC,MAAM,IAAI3M,MAAM8M,yCAA8B;QAChD;QAEA,IAAIL,sBAAsBI,gBAAgB;YACxC,MAAM,IAAI7M,MAAM+M,+CAAoC;QACtD;QAEA,IAAIJ,kBAAkBE,gBAAgB;YACpC,MAAM,IAAI7M,MAAMgN,oCAAyB;QAC3C;QAEA,MAAMC,gBAAgBV,IAAAA,yBAAc,EAAC1M;QACrC,oEAAoE;QACpE,IAAI8M,kBAAkBC,kBAAkB,CAACK,eAAe;YACtD,MAAM,IAAIjN,MACR,CAAC,yDAAyD,EAAEH,KAAK,EAAE,CAAC,GAClE,CAAC,4DAA4D,CAAC;QAEpE;QAEA,IAAI8M,kBAAkBM,iBAAiB,CAACL,gBAAgB;YACtD,MAAM,IAAI5M,MACR,CAAC,qEAAqE,EAAEH,KAAK,EAAE,CAAC,GAC9E,CAAC,0EAA0E,CAAC;QAElF;QAEA,IAAI,AAAC8M,kBAAkBC,kBAAmB3L,mBAAmB;;YACzD,CAAA,EAAE6C,cAAcoG,qBAAqB,EAAE5I,iBAAiB,EAAE,GAC1D,MAAMlQ,iBAAiB;gBACrByO;gBACAsB;gBACAC;gBACAF;gBACAD;gBACAD,gBAAgBiJ,iBAAiBjJ,cAAc;YACjD,EAAC;QACL;QAEA,MAAMkM,sBAAsB,AAACC,WAAmBC,qBAAqB;QACrE,MAAMtI,SAAqBsF,oBACvB,CAAC,IACDH,iBAAiBiB,UAAU;QAE/B,IAAIvP,WAAW;QACf,IAAI,CAACgR,kBAAkB,CAACF,sBAAsB,CAACI,gBAAgB;YAC7DlR,WAAW;QACb;QAEA,8DAA8D;QAC9D,6BAA6B;QAC7B,IAAIJ,mBAAmB;YACrBI,WAAW;QACb;QAEA,OAAO;YACLA;YACAJ;YACAlF,aAAayO,OAAOuI,GAAG,KAAK;YAC5BC,WAAWxI,OAAOuI,GAAG,KAAK;YAC1BnD;YACA5I;YACAqL;YACAE;YACAK;YACA/C;QACF;IACF,GACCoD,KAAK,CAAC,CAAC5B;QACN,IAAIA,IAAI6B,OAAO,KAAK,0BAA0B;YAC5C,MAAM7B;QACR;QACAxY,QAAQsa,KAAK,CAAC9B;QACd,MAAM,IAAI3L,MAAM,CAAC,gCAAgC,EAAEH,MAAM;IAC3D;AACJ;AAoBO,SAAS7M,gBACdoR,QAAsC;IAEtC,MAAMU,SAA2B,CAAC;IAElC,KAAK,MAAMrC,WAAW2B,SAAU;QAC9B,MAAM,EACJyH,OAAO,EACPjE,UAAU,EACV8F,eAAe,EACfpB,UAAU,EACVqB,gBAAgB,EAChBjV,OAAO,EACPkV,WAAW,EACZ,GAAGnL,QAAQqC,MAAM,IAAI,CAAC;QAEvB,uDAAuD;QACvD,6DAA6D;QAE7D,IAAI,OAAO4I,oBAAoB,aAAa;YAC1C5I,OAAO4I,eAAe,GAAGA;QAC3B;QAEA,IAAI,OAAO7B,YAAY,aAAa;YAClC/G,OAAO+G,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAOjE,eAAe,aAAa;YACrC9C,OAAO8C,UAAU,GAAGA;QACtB;QAEA,IAAI,OAAO0E,eAAe,aAAa;YACrCxH,OAAOwH,UAAU,GAAGA;QACtB;QAEA,0EAA0E;QAC1E,sBAAsB;QACtB,IACE,OAAOA,eAAe,YACrB,CAAA,OAAOxH,OAAOwH,UAAU,KAAK,YAAYA,aAAaxH,OAAOwH,UAAU,AAAD,GACvE;YACAxH,OAAOwH,UAAU,GAAGA;QACtB;QAEA,wEAAwE;QACxE,oEAAoE;QACpE,IAAI,OAAOqB,qBAAqB,aAAa;YAC3C7I,OAAO6I,gBAAgB,GAAGA;QAC5B;QAEA,IAAI,OAAOjV,YAAY,aAAa;YAClCoM,OAAOpM,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAOkV,gBAAgB,aAAa;YACtC9I,OAAO8I,WAAW,GAAGA;QACvB;IACF;IAEA,OAAO9I;AACT;AAEO,eAAe9S,yBAAyB,EAC7C6N,IAAI,EACJoE,OAAO,EACP+E,gBAAgB,EAChB6E,WAAW,EAMZ;IACC/D,QAAQ,yCAAyCC,SAAS,CAACf;IAE3D,MAAM8E,aAAa,MAAMxC,IAAAA,8BAAc,EAAC;QACtCrH;QACApE,MAAMA;QACN0L,WAAW;QACXC,OAAO;IACT;IACA,IAAInG,MAAMyI,WAAWlJ,YAAY;IAEjC,IAAIiJ,aAAa;QACfxI,MAAM,AAAC,MAAMA,IAAI0I,IAAI,IAAK1I,IAAIC,OAAO,IAAID;IAC3C,OAAO;QACLA,MAAMA,IAAIC,OAAO,IAAID;IACvB;IACAA,MAAM,MAAMA;IACZ,OAAOA,IAAIqH,eAAe,KAAKrH,IAAI2I,mBAAmB;AACxD;AAEO,eAAerc,uBAAuB,EAC3CkO,IAAI,EACJoE,OAAO,EACP+E,gBAAgB,EAKjB;IACCc,QAAQ,yCAAyCC,SAAS,CAACf;IAC3D,MAAM8E,aAAa,MAAMxC,IAAAA,8BAAc,EAAC;QACtCrH;QACApE,MAAMA;QACN0L,WAAW;QACXC,OAAO;IACT;IAEA,OAAOnW,OAAOqB,IAAI,CAACoX,WAAWlJ,YAAY,EAAEtQ,MAAM,CAAC,CAACqB;QAClD,OAAO,OAAOmY,WAAWlJ,YAAY,CAACjP,IAAI,KAAK;IACjD;AACF;AAEO,SAASlE,uBACdwc,aAAuB,EACvBC,QAAqB,EACrBC,2BAAkD;IAElD,MAAMC,mBAAmB,IAAIlY;IAQ7B,MAAMmY,kBAAkB;WAAIH;KAAS,CAAC5Z,MAAM,CAAC,CAACuL,OAAS0M,IAAAA,yBAAc,EAAC1M;IACtE,MAAMyO,2BAEF,CAAC;IAELH,4BAA4BxT,OAAO,CAAC,CAACsH,OAAOsM;QAC1CD,wBAAwB,CAACC,UAAU,KAAK,CAAC;QACzCtM,MAAMtH,OAAO,CAAC,CAAC6T;YACb,MAAMC,cAAcD,QAAQE,WAAW;YACvCJ,wBAAwB,CAACC,UAAU,CAACE,YAAY,GAAGD;QACrD;IACF;IAEAL,4BAA4BxT,OAAO,CAAC,CAACsH,OAAOsM;QAC1CtM,MAAMtH,OAAO,CAAC,CAAC6T;YACb,MAAMG,YAAYH,QAAQE,WAAW;YACrC,IAAIE,kBAAkBX,cAAcY,IAAI,CACtC,CAAChP,OAASA,KAAK6O,WAAW,OAAOC;YAGnC,IAAIC,iBAAiB;gBACnBR,iBAAiBvY,GAAG,CAAC8Y,WAAW;oBAC9B;wBAAE/X,MAAM4X;wBAAS3O,MAAM0O;oBAAU;oBACjC;wBAAE3X,MAAMgY;wBAAiB/O,MAAM+O;oBAAgB;iBAChD;YACH,OAAO;gBACL,IAAIE;gBAEJF,kBAAkBP,gBAAgBQ,IAAI,CAAC,CAAChP;oBACtC,IAAIA,SAAS0O,WAAW,OAAO;oBAE/BO,kBACEX,4BAA4BpY,GAAG,CAAC8J,SAAS,OACrCpI,YACA6W,wBAAwB,CAACzO,KAAK,CAAC8O,UAAU;oBAC/C,OAAOG;gBACT;gBAEA,IAAIF,mBAAmBE,iBAAiB;oBACtCV,iBAAiBvY,GAAG,CAAC8Y,WAAW;wBAC9B;4BAAE/X,MAAM4X;4BAAS3O,MAAM0O;wBAAU;wBACjC;4BAAE3X,MAAMkY;4BAAiBjP,MAAM+O;wBAAgB;qBAChD;gBACH;YACF;QACF;IACF;IAEA,IAAIR,iBAAiBta,IAAI,GAAG,GAAG;QAC7B,IAAIib,yBAAyB;QAE7BX,iBAAiBzT,OAAO,CAAC,CAACqU;YACxBA,UAAUrU,OAAO,CAAC,CAACsU,UAAUlS;gBAC3B,MAAMmS,YAAYD,SAASpP,IAAI,KAAKoP,SAASrY,IAAI;gBAEjD,IAAImG,MAAM,GAAG;oBACXgS,0BAA0B;gBAC5B;gBAEAA,0BAA0B,CAAC,OAAO,EAAEE,SAASrY,IAAI,CAAC,CAAC,EACjDsY,YAAY,CAAC,aAAa,EAAED,SAASpP,IAAI,CAAC,EAAE,CAAC,GAAG,KAChD;YACJ;YACAkP,0BAA0B;QAC5B;QAEAjD,KAAI2B,KAAK,CACP,qFACE,mFACAsB;QAEJ1G,QAAQ8G,IAAI,CAAC;IACf;AACF;AAEO,eAAe3d,gBACpBwS,GAAW,EACXC,OAAe,EACfmL,QAA2B,EAC3BC,WAA0C,EAC1CC,WAAmB,EACnBC,YAAwB,EACxBtW,kBAAsC,EACtCuW,sBAA+B,EAC/BC,WAAwB;IAExB,MAAMC,aAAa9Y,aAAI,CAACC,IAAI,CAACoN,SAAS;IACtC,IAAI0L,aAAa;IACjB,MAAMC,aAAa;QACjB,GAAGL,YAAY;QACftL,SAAS,CAAC,EAAE,EAAErN,aAAI,CAACoR,QAAQ,CAAChE,KAAKC,UAAU;IAC7C;IACA,IAAI;QACF,MAAM4L,kBAAkBjZ,aAAI,CAACC,IAAI,CAACoN,SAAS;QAC3C,MAAM6L,cAAcC,KAAKC,KAAK,CAAC,MAAMpc,YAAE,CAACqc,QAAQ,CAACJ,iBAAiB;QAClEF,aAAaG,YAAYlR,IAAI,KAAK;IACpC,EAAE,OAAM,CAAC;IACT,MAAMsR,cAAc,IAAI/b;IACxB,MAAMP,YAAE,CAACuc,EAAE,CAACT,YAAY;QAAEU,WAAW;QAAMC,OAAO;IAAK;IAEvD,eAAeC,iBAAiBC,aAAqB;QACnD,MAAMC,YAAYT,KAAKC,KAAK,CAAC,MAAMpc,YAAE,CAACqc,QAAQ,CAACM,eAAe;QAG9D,MAAME,WAAW,IAAIC,eAAI,CAAC,IAAI;YAAEC,UAAUH,UAAUpb,KAAK,CAACoF,MAAM;QAAC;QACjE,MAAMoW,eAAeha,aAAI,CAACia,OAAO,CAACN;QAElC,MAAM/Z,QAAQC,GAAG,CACf+Z,UAAUpb,KAAK,CAACM,GAAG,CAAC,OAAOob;YACzB,MAAML,SAASM,OAAO;YAEtB,MAAMC,iBAAiBpa,aAAI,CAACC,IAAI,CAAC+Z,cAAcE;YAC/C,MAAMG,iBAAiBra,aAAI,CAACC,IAAI,CAC9B6Y,YACA9Y,aAAI,CAACoR,QAAQ,CAACsH,aAAa0B;YAG7B,IAAI,CAACd,YAAY1b,GAAG,CAACyc,iBAAiB;gBACpCf,YAAYrU,GAAG,CAACoV;gBAEhB,MAAMrd,YAAE,CAACsd,KAAK,CAACta,aAAI,CAACia,OAAO,CAACI,iBAAiB;oBAAEb,WAAW;gBAAK;gBAC/D,MAAMe,UAAU,MAAMvd,YAAE,CAACwd,QAAQ,CAACJ,gBAAgBzD,KAAK,CAAC,IAAM;gBAE9D,IAAI4D,SAAS;oBACX,IAAI;wBACF,MAAMvd,YAAE,CAACud,OAAO,CAACA,SAASF;oBAC5B,EAAE,OAAOhZ,GAAQ;wBACf,IAAIA,EAAEoZ,IAAI,KAAK,UAAU;4BACvB,MAAMpZ;wBACR;oBACF;gBACF,OAAO;oBACL,MAAMrE,YAAE,CAAC0d,QAAQ,CAACN,gBAAgBC;gBACpC;YACF;YAEA,MAAMR,SAASc,OAAO;QACxB;IAEJ;IAEA,eAAeC,mBAAmB3R,IAA4B;YAa1DA,YACAA;QAbF,eAAe4R,WAAWje,IAAY;YACpC,MAAMke,eAAe9a,aAAI,CAACC,IAAI,CAACoN,SAASzQ;YACxC,MAAMyd,iBAAiBra,aAAI,CAACC,IAAI,CAC9B6Y,YACA9Y,aAAI,CAACoR,QAAQ,CAACsH,aAAarL,UAC3BzQ;YAEF,MAAMI,YAAE,CAACsd,KAAK,CAACta,aAAI,CAACia,OAAO,CAACI,iBAAiB;gBAAEb,WAAW;YAAK;YAC/D,MAAMxc,YAAE,CAAC0d,QAAQ,CAACI,cAAcT;QAClC;QACA,MAAMza,QAAQC,GAAG,CAAC;YAChBoJ,KAAKzK,KAAK,CAACM,GAAG,CAAC+b;aACf5R,aAAAA,KAAK2K,IAAI,qBAAT3K,WAAWnK,GAAG,CAAC,CAAClC,OAASie,WAAWje,KAAKyU,QAAQ;aACjDpI,eAAAA,KAAK8R,MAAM,qBAAX9R,aAAanK,GAAG,CAAC,CAAClC,OAASie,WAAWje,KAAKyU,QAAQ;SACpD;IACH;IAEA,MAAM2J,uBAAuC,EAAE;IAE/C,KAAK,MAAM7T,cAAc1I,OAAOwc,MAAM,CAAC5Y,mBAAmB8E,UAAU,EAAG;QACrE,IAAIzL,qBAAqByL,WAAW2M,IAAI,GAAG;YACzCkH,qBAAqBxa,IAAI,CAACoa,mBAAmBzT;QAC/C;IACF;IAEA,KAAK,MAAM8B,QAAQxK,OAAOwc,MAAM,CAAC5Y,mBAAmB6Y,SAAS,EAAG;QAC9DF,qBAAqBxa,IAAI,CAACoa,mBAAmB3R;IAC/C;IAEA,MAAMrJ,QAAQC,GAAG,CAACmb;IAElB,KAAK,MAAM/R,QAAQuP,SAAU;QAC3B,IAAInW,mBAAmB6Y,SAAS,CAACtO,cAAc,CAAC3D,OAAO;YACrD;QACF;QACA,MAAMtH,QAAQwZ,IAAAA,oCAAiB,EAAClS;QAEhC,IAAI4P,YAAYjb,GAAG,CAAC+D,QAAQ;YAC1B;QACF;QAEA,MAAMyZ,WAAWpb,aAAI,CAACC,IAAI,CACxBoN,SACA,UACA,SACA,GAAG8N,IAAAA,oCAAiB,EAAClS,MAAM,GAAG,CAAC;QAEjC,MAAMoS,gBAAgB,GAAGD,SAAS,SAAS,CAAC;QAC5C,MAAM1B,iBAAiB2B,eAAe1E,KAAK,CAAC,CAAC5B;YAC3C,IAAIA,IAAI0F,IAAI,KAAK,YAAaxR,SAAS,UAAUA,SAAS,QAAS;gBACjEiM,KAAIC,IAAI,CAAC,CAAC,gCAAgC,EAAEiG,UAAU,EAAErG;YAC1D;QACF;IACF;IAEA,IAAI0D,aAAa;QACf,KAAK,MAAMxP,QAAQwP,YAAa;YAC9B,IAAIpW,mBAAmB6Y,SAAS,CAACtO,cAAc,CAAC3D,OAAO;gBACrD;YACF;YACA,MAAMmS,WAAWpb,aAAI,CAACC,IAAI,CAACoN,SAAS,UAAU,OAAO,GAAGpE,KAAK,GAAG,CAAC;YACjE,MAAMoS,gBAAgB,GAAGD,SAAS,SAAS,CAAC;YAC5C,MAAM1B,iBAAiB2B,eAAe1E,KAAK,CAAC,CAAC5B;gBAC3CG,KAAIC,IAAI,CAAC,CAAC,gCAAgC,EAAEiG,UAAU,EAAErG;YAC1D;QACF;IACF;IAEA,IAAI6D,wBAAwB;QAC1B,MAAMc,iBACJ1Z,aAAI,CAACC,IAAI,CAACoN,SAAS,UAAU;IAEjC;IAEA,MAAMqM,iBAAiB1Z,aAAI,CAACC,IAAI,CAACoN,SAAS;IAC1C,MAAMiO,mBAAmBtb,aAAI,CAACC,IAAI,CAChC6Y,YACA9Y,aAAI,CAACoR,QAAQ,CAACsH,aAAatL,MAC3B;IAEF,MAAMpQ,YAAE,CAACsd,KAAK,CAACta,aAAI,CAACia,OAAO,CAACqB,mBAAmB;QAAE9B,WAAW;IAAK;IAEjE,MAAMxc,YAAE,CAACue,SAAS,CAChBD,kBACA,GACEvC,aACI,CAAC;;;;;;AAMX,CAAC,GACS,CAAC,4BAA4B,CAAC,CACnC;;;;;;;;;;;mBAWc,EAAEI,KAAKqC,SAAS,CAACxC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;GA0B7C,CAAC;AAEJ;AAEO,SAASpd,eAAeqN,IAAY;IACzC,OAAOxM,cAAcgf,IAAI,CAACxS;AAC5B;AAEO,SAAS5N,yBAAyB4N,IAAY;IACnD,OAAO,8DAA8DwS,IAAI,CACvExS;AAEJ;AAEO,SAAS3N,kBAAkB2N,IAAY;IAC5C,OAAOA,SAAS,UAAUA,SAAS;AACrC;AAEO,SAASxN,iBAAiBmB,IAAY;IAC3C,OACEA,SAAS,CAAC,CAAC,EAAEmE,8BAAmB,EAAE,IAAInE,SAAS,CAAC,KAAK,EAAEmE,8BAAmB,EAAE;AAEhF;AAEO,SAASxF,0BAA0BqB,IAAY;IACpD,OACEA,SAAS,CAAC,CAAC,EAAEoE,wCAA6B,EAAE,IAC5CpE,SAAS,CAAC,KAAK,EAAEoE,wCAA6B,EAAE;AAEpD;AAEO,SAAS/F,wCACdygB,MAAc,EACdC,UAAoB;IAEpB,MAAMnd,QAAQ,EAAE;IAChB,KAAK,MAAMod,aAAaD,WAAY;QAClCnd,MAAMgC,IAAI,CACRR,aAAI,CAACC,IAAI,CAACyb,QAAQ,GAAG1a,wCAA6B,CAAC,CAAC,EAAE4a,WAAW,GACjE5b,aAAI,CAACC,IAAI,CAACyb,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG1a,wCAA6B,CAAC,CAAC,EAAE4a,WAAW;IAE5E;IAEA,OAAOpd;AACT;AAEO,SAAStD,+BACdwgB,MAAc,EACdC,UAAoB;IAEpB,OAAOA,WAAW7c,GAAG,CAAC,CAAC8c,YACrB5b,aAAI,CAACC,IAAI,CAACyb,QAAQ,GAAG3a,8BAAmB,CAAC,CAAC,EAAE6a,WAAW;AAE3D;AAEO,MAAMthB,8BAA8B8O;IACzCyS,YACEC,eAAyB,EACzBC,OAAe,EACfC,aAAqB,CACrB;QACA,KAAK,CACH,CAAC,0CAA0C,CAAC,GAC1C,GAAGF,gBAAgBhd,GAAG,CAAC,CAAClC,OAAS,CAAC,KAAK,EAAEA,MAAM,EAAEqD,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/D,CAAC,0CAA0C,EAAED,aAAI,CAACC,IAAI,CACpDD,aAAI,CAACic,KAAK,CAACC,GAAG,EACdlc,aAAI,CAACoR,QAAQ,CAAC2K,SAAS/b,aAAI,CAACmc,OAAO,CAACH,eAAe,QACnD,cACA,WAAW,CAAC,GACd,CAAC,8DAA8D,CAAC;IAEtE;AACF;AAEO,SAAS7gB,qBACdiS,GAAW,EACXgP,aAAsB;IAEtB,IAAIC;IACJ,IAAI;QACF,MAAMC,qBAAqBC,qBAAY,CAACC,UAAU,CAAC;YACjDxc,MAAMoN;YACNsE,KAAK0K,gBAAgB,gBAAgB;QACvC;QACA,8FAA8F;QAC9F,IAAIE,sBAAsBA,mBAAmB1Y,MAAM,GAAG,GAAG;YACvDyY,WAAWE,IAAAA,qBAAY,EAACD;QAC1B;IACF,EAAE,OAAM,CAAC;IAET,6CAA6C;IAC7C,IAAID,YAAYA,SAASzY,MAAM,GAAG,GAAG;QACnC,OAAOyY;IACT;IAEA,uCAAuC;IACvC,OAAOI,sCAA0B;AACnC;AAEO,SAASxgB,yBACdygB,KAA0C;IAE1C,OAAOC,QACLD,SAASE,yBAAc,CAACC,KAAK,CAACC,UAAU,CAACxY,QAAQ,CAACoY;AAEtD;AAEO,SAAS3gB,yBACd2gB,KAA0C;IAE1C,OAAOC,QACLD,SAASE,yBAAc,CAACC,KAAK,CAACE,UAAU,CAACzY,QAAQ,CAACoY;AAEtD;AAEO,SAAS1gB,sBACd0gB,KAA0C;IAE1C,OAAOA,UAAU,QAAQA,UAAU7b;AACrC;AAEO,SAAS/E,sBACd4gB,KAA0C;IAE1C,OAAOC,QAAQD,SAASE,yBAAc,CAACC,KAAK,CAACG,OAAO,CAAC1Y,QAAQ,CAACoY;AAChE;AAEO,SAAS7gB,uBACd6gB,KAA0C;IAE1C,OAAOC,QAAQD,SAASE,yBAAc,CAACC,KAAK,CAACI,QAAQ,CAAC3Y,QAAQ,CAACoY;AACjE;AAEO,SAASjiB,YAAY,EAC1ByiB,MAAM,EACNpV,OAAO,EAIR;IAIC,MAAMqV,OAGF,CAAC;IAEL,IAAID,WAAW,KAAK;QAClBC,KAAKD,MAAM,GAAGA;IAChB;IAEA,IAAIpV,WAAWrJ,OAAOqB,IAAI,CAACgI,SAASlE,MAAM,EAAE;QAC1CuZ,KAAKrV,OAAO,GAAG,CAAC;QAEhB,4CAA4C;QAC5C,iCAAiC;QACjC,IAAK,MAAM/I,OAAO+I,QAAS;YACzB,qEAAqE;YACrE,sEAAsE;YACtE,IAAI/I,QAAQ,2BAA2B;YAEvC,IAAI6J,QAAQd,OAAO,CAAC/I,IAAI;YAExB,IAAIkM,MAAMC,OAAO,CAACtC,QAAQ;gBACxB,IAAI7J,QAAQ,cAAc;oBACxB6J,QAAQA,MAAM3I,IAAI,CAAC;gBACrB,OAAO;oBACL2I,QAAQA,KAAK,CAACA,MAAMhF,MAAM,GAAG,EAAE;gBACjC;YACF;YAEA,IAAI,OAAOgF,UAAU,UAAU;gBAC7BuU,KAAKrV,OAAO,CAAC/I,IAAI,GAAG6J;YACtB;QACF;IACF;IAEA,OAAOuU;AACT"}