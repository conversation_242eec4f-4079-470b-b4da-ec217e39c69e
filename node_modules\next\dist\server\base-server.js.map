{"version": 3, "sources": ["../../src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport {\n  type FallbackRouteParams,\n  getFallbackRouteParams,\n} from './request/fallback-params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport {\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ServerComponentsHmrCache,\n  type ResponseCacheBase,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n  CachedRouteKind,\n  type CachedRedirectValue,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PreviewData } from '../types'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from './route-modules/app-page/module'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { AppRouteRouteHandlerContext } from './route-modules/app-route/module'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport { getRedirectStatus } from '../lib/redirect-status'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport {\n  formatRevalidate,\n  type Revalidate,\n  type ExpireTime,\n} from './lib/revalidate'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport { getTracer, isBubbledError, SpanKind } from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  normalizeNextQueryParam,\n  toNodeOutgoingHttpHeaders,\n} from './web/utils'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  MATCHED_PATH_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from './web/spec-extension/adapters/next-request'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { decodePathParams } from './lib/router-utils/decode-path-params'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n  isPagesRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from './lib/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { ENCODED_TAGS } from './stream-utils/encodedTags'\nimport { NextRequestHint } from './web/adapter'\nimport { getRevalidateReason } from './instrumentation/utils'\nimport { RouteKind } from './route-kind'\nimport type { RouteModule } from './route-modules/route-module'\nimport { FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { toResponseCacheEntry } from './response-cache/utils'\nimport { scheduleOnNextTick } from '../lib/scheduler'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\nexport class NoFallbackError extends Error {}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  type: 'html' | 'json' | 'rsc'\n  body: RenderResult\n  revalidate?: Revalidate | undefined\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      revalidate: Revalidate | undefined\n      expireTime: ExpireTime | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n    requestProtocol: 'http' | 'https'\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for dynamic IO\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      customServer = true,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir =\n      process.env.NEXT_RUNTIME === 'edge' ? dir : require('path').resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? this.nextConfig.distDir\n        : require('path').join(this.dir, this.nextConfig.distDir)\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n    }\n\n    this.renderOpts = {\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      strictNextHead: this.nextConfig.experimental.strictNextHead ?? true,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      buildId: this.buildId,\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      customServer: customServer === true ? true : undefined,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      // @ts-expect-error internal field not publicly exposed\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        dynamicIO: this.nextConfig.experimental.dynamicIO ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  protected reloadMatchers() {\n    return this.matchers.reload()\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          getRequestMeta(req, 'middlewareInvoke')\n        ) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        parsedUrl.query.__nextLocale = localePathResult.detectedLocale\n        parsedUrl.query.__nextDefaultLocale = defaultLocale\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          delete parsedUrl.query.__nextInferredLocaleFromDefault\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          parsedUrl.query.__nextLocale = defaultLocale\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      parsedUrl.query.__nextDataReq = '1'\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // Validate that if i18n isn't configured or the passed parameters are not\n      // valid it should be removed from the query.\n      if (!this.i18nProvider?.validateQuery(parsedUrl.query)) {\n        delete parsedUrl.query.__nextLocale\n        delete parsedUrl.query.__nextDefaultLocale\n        delete parsedUrl.query.__nextInferredLocaleFromDefault\n      }\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      parsedUrl.query.__nextDefaultLocale = defaultLocale\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            req.headers[MATCHED_PATH_HEADER] as string,\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            parsedUrl.query.__nextDataReq = '1'\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            parsedUrl.query.__nextLocale = localeAnalysisResult.detectedLocale\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              parsedUrl.query.__nextInferredLocaleFromDefault = '1'\n            } else {\n              delete parsedUrl.query.__nextInferredLocaleFromDefault\n            }\n          }\n\n          // TODO: check if this is needed any more?\n          matchedPath = denormalizePagePath(matchedPath)\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n              // The page is dynamic if the params are defined.\n              pageIsDynamic = typeof match.params !== 'undefined'\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParams = utils.handleRewrites(req, parsedUrl)\n          const rewriteParamKeys = Object.keys(rewriteParams)\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n          const routeParamKeys = new Set<string>()\n\n          for (const key of Object.keys(parsedUrl.query)) {\n            const value = parsedUrl.query[key]\n\n            normalizeNextQueryParam(key, (normalizedKey) => {\n              if (!parsedUrl) return // typeguard\n\n              parsedUrl.query[normalizedKey] = value\n              routeParamKeys.add(normalizedKey)\n              delete parsedUrl.query[key]\n            })\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            let paramsResult = utils.normalizeDynamicRouteParams(\n              parsedUrl.query\n            )\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult =\n                  utils.normalizeDynamicRouteParams(matcherParams)\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            if (\n              req.headers['x-now-route-matches'] &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const opts: Record<string, string> = {}\n              const routeParams = utils.getParamsFromRouteMatches(\n                req,\n                opts,\n                parsedUrl.query.__nextLocale || ''\n              )\n\n              // If this returns a locale, it means that the locale was detected\n              // from the pathname.\n              if (opts.locale) {\n                parsedUrl.query.__nextLocale = opts.locale\n\n                // As the locale was parsed from the pathname, we should mark\n                // that the locale was not inferred as the default.\n                delete parsedUrl.query.__nextInferredLocaleFromDefault\n              }\n              paramsResult = utils.normalizeDynamicRouteParams(\n                routeParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // handle the actual dynamic route name being requested\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams &&\n              !utils.normalizeDynamicRouteParams({ ...params }, true)\n                .hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // Mark that the default route matches were set on the request\n              // during routing.\n              addRequestMeta(req, 'didSetDefaultRouteMatches', true)\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeVercelUrl(req, true, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          for (const key of routeParamKeys) {\n            delete parsedUrl.query[key]\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !parsedUrl.query.__nextLocale) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          parsedUrl.query.__nextLocale = pathnameInfo.locale\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          parsedUrl.query.__nextLocale = defaultLocale\n          parsedUrl.query.__nextInferredLocaleFromDefault = '1'\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        let protocol: 'http:' | 'https:' = 'https:'\n\n        try {\n          const parsedFullUrl = new URL(\n            getRequestMeta(req, 'initURL') || '/',\n            'http://n'\n          )\n          protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n        } catch {}\n\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n          requestProtocol: protocol.substring(0, protocol.length - 1) as\n            | 'http'\n            | 'https',\n        })\n\n        const _globalThis: typeof globalThis & {\n          __nextCacheHandlers?: Record<\n            string,\n            import('./lib/cache-handlers/types').CacheHandler\n          >\n        } = globalThis\n\n        if (_globalThis.__nextCacheHandlers) {\n          const expiredTags: string[] =\n            (req.headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER] as string)?.split(\n              ','\n            ) || []\n\n          for (const handler of Object.values(\n            _globalThis.__nextCacheHandlers\n          )) {\n            if (typeof handler.receiveExpiredTags === 'function') {\n              await handler.receiveExpiredTags(...expiredTags)\n            }\n          }\n        }\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath =\n        !useMatchedPathHeader &&\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          parsedUrl.query.__nextLocale = invokePathnameInfo.locale\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales || []\n        )\n\n        if (normalizeResult.detectedLocale) {\n          parsedUrl.query.__nextLocale = normalizeResult.detectedLocale\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          if (!key.startsWith('__next') && !key.startsWith('_next')) {\n            delete parsedUrl.query[key]\n          }\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        getRequestMeta(req, 'middlewareInvoke')\n      ) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.renderOpts.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    if (this.preparedPromise === null) {\n      // Get instrumentation module\n      this.instrumentation = await this.loadInstrumentationModule()\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const isBotRequest = isBot(partialContext.req.headers['user-agent'] || '')\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: !isBotRequest,\n        isBot: !!isBotRequest,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body, type } = payload\n    let { revalidate } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        revalidate = undefined\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        type,\n        generateEtags,\n        poweredByHeader,\n        revalidate,\n        expireTime: this.nextConfig.expireTime,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.renderOpts.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !query.__nextDataReq &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.setHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.setHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    let hasGetStaticPaths = !!components.getStaticPaths\n    const isServerAction = getIsServerAction(req)\n    const hasGetInitialProps = !!components.Component?.getInitialProps\n    let isSSG = !!components.getStaticProps\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let fallbackMode: FallbackMode | undefined\n    let hasFallback = false\n\n    const isDynamic = isDynamicRoute(components.page)\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (isAppPath && isDynamic) {\n      const pathsResult = await this.getStaticPaths({\n        pathname,\n        page: components.page,\n        isAppPath,\n        requestHeaders: req.headers,\n      })\n\n      staticPaths = pathsResult.staticPaths\n      fallbackMode = pathsResult.fallbackMode\n      hasFallback = typeof fallbackMode !== 'undefined'\n\n      if (this.nextConfig.output === 'export') {\n        const page = components.page\n        if (!staticPaths) {\n          throw new Error(\n            `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n\n        const resolvedWithoutSlash = removeTrailingSlash(resolvedUrlPathname)\n        if (!staticPaths.includes(resolvedWithoutSlash)) {\n          throw new Error(\n            `Page \"${page}\" is missing param \"${resolvedWithoutSlash}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n      }\n\n      if (hasFallback) {\n        hasGetStaticPaths = true\n      }\n    }\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        query.__nextDataReq ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    /**\n     * If true, this indicates that the request being made is for an app\n     * prefetch request.\n     */\n    const isPrefetchRSCRequest =\n      getRequestMeta(req, 'isPrefetchRSCRequest') ?? false\n\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    delete query.__nextDataReq\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${query.__nextLocale ? `/${query.__nextLocale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery =\n      hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    const isDebugStaticShell: boolean =\n      hasDebugStaticShellQuery && isRoutePPREnabled\n\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses =\n      isDebugStaticShell && this.renderOpts.dev === true\n\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest =\n      isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader =\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()]\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      await this.renderError(null, req, res, pathname)\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        type: 'html',\n        // TODO: Static pages should be serialized as RenderResult\n        body: RenderResult.fromStatic(components.Component),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const isBotRequest = isBot(req.headers['user-agent'] || '')\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n      opts.isBot = isBotRequest\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    const defaultLocale = isSSG\n      ? this.nextConfig.i18n?.defaultLocale\n      : query.__nextDefaultLocale\n\n    const locale = query.__nextLocale\n    const locales = this.nextConfig.i18n?.locales\n\n    let previewData: PreviewData\n    let isPreviewMode = false\n\n    if (hasServerProps || isSSG || isAppPath) {\n      // For the edge runtime, we don't support preview mode in SSG.\n      if (process.env.NEXT_RUNTIME !== 'edge') {\n        const { tryGetPreviewData } =\n          require('./api-utils/node/try-get-preview-data') as typeof import('./api-utils/node/try-get-preview-data')\n        previewData = tryGetPreviewData(\n          req,\n          res,\n          this.renderOpts.previewProps,\n          !!this.nextConfig.experimental.multiZoneDraftMode\n        )\n        isPreviewMode = previewData !== false\n      }\n    }\n\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (\n      isAppPath &&\n      !opts.dev &&\n      !isPreviewMode &&\n      isSSG &&\n      isRSCRequest &&\n      !isDynamicRSCRequest &&\n      (!isEdgeRuntime(opts.runtime) ||\n        (this.serverOptions as any).webServerConfig)\n    ) {\n      stripFlightHeaders(req.headers)\n    }\n\n    let isOnDemandRevalidate = false\n    let revalidateOnlyGenerated = false\n\n    if (isSSG) {\n      ;({ isOnDemandRevalidate, revalidateOnlyGenerated } =\n        checkIsOnDemandRevalidate(req, this.renderOpts.previewProps))\n    }\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    const handleRedirect = (pageData: any) => {\n      const redirect = {\n        destination: pageData.pageProps.__N_REDIRECT,\n        statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n        basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n      }\n      const statusCode = getRedirectStatus(redirect)\n      const { basePath } = this.nextConfig\n\n      if (\n        basePath &&\n        redirect.basePath !== false &&\n        redirect.destination.startsWith('/')\n      ) {\n        redirect.destination = `${basePath}${redirect.destination}`\n      }\n\n      if (redirect.destination.startsWith('/')) {\n        redirect.destination = normalizeRepeatedSlashes(redirect.destination)\n      }\n\n      res\n        .redirect(redirect.destination, statusCode)\n        .body(redirect.destination)\n        .send()\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    let ssgCacheKey: string | null = null\n    if (\n      !isPreviewMode &&\n      isSSG &&\n      !opts.supportsDynamicResponse &&\n      !isServerAction &&\n      !minimalPostponed &&\n      !isDynamicRSCRequest\n    ) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${\n        (pathname === '/' || resolvedUrlPathname === '/') && locale\n          ? ''\n          : resolvedUrlPathname\n      }${query.amp ? '.amp' : ''}`\n    }\n\n    if ((is404Page || is500Page) && isSSG) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${\n        query.amp ? '.amp' : ''\n      }`\n    }\n\n    if (ssgCacheKey) {\n      ssgCacheKey = decodePathParams(ssgCacheKey)\n\n      // ensure /index and / is normalized to one key\n      ssgCacheKey =\n        ssgCacheKey === '/index' && pathname === '/' ? '/' : ssgCacheKey\n    }\n    let protocol: 'http:' | 'https:' = 'https:'\n\n    try {\n      const parsedFullUrl = new URL(\n        getRequestMeta(req, 'initURL') || '/',\n        'http://n'\n      )\n      protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n    } catch {}\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      (globalThis as any).__incrementalCache ||\n      (await this.getIncrementalCache({\n        requestHeaders: Object.assign({}, req.headers),\n        requestProtocol: protocol.substring(0, protocol.length - 1) as\n          | 'http'\n          | 'https',\n      }))\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    type RendererContext = {\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }\n    type Renderer = (\n      context: RendererContext\n    ) => Promise<ResponseCacheEntry | null>\n\n    const doRender: Renderer = async ({ postponed, fallbackRouteParams }) => {\n      // In development, we always want to generate dynamic HTML.\n      let supportsDynamicResponse: boolean =\n        // If we're in development, we always support dynamic HTML, unless it's\n        // a data request, in which case we only produce static HTML.\n        (!isNextDataRequest && opts.dev === true) ||\n        // If this is not SSG or does not have static paths, then it supports\n        // dynamic HTML.\n        (!isSSG && !hasGetStaticPaths) ||\n        // If this request has provided postponed data, it supports dynamic\n        // HTML.\n        typeof postponed === 'string' ||\n        // If this is a dynamic RSC request, then this render supports dynamic\n        // HTML (it's dynamic).\n        isDynamicRSCRequest\n\n      const origQuery = parseUrl(req.url || '', true).query\n\n      // clear any dynamic route params so they aren't in\n      // the resolvedUrl\n      if (opts.params) {\n        Object.keys(opts.params).forEach((key) => {\n          delete origQuery[key]\n        })\n      }\n      const hadTrailingSlash =\n        urlPathname !== '/' && this.nextConfig.trailingSlash\n\n      const resolvedUrl = formatUrl({\n        pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,\n        // make sure to only add query values from original URL\n        query: origQuery,\n      })\n      const renderOpts: LoadedRenderOpts = {\n        ...components,\n        ...opts,\n        ...(isAppPath\n          ? {\n              incrementalCache,\n              // This is a revalidation request if the request is for a static\n              // page and it is not being resumed from a postponed render and\n              // it is not a dynamic RSC request then it is a revalidation\n              // request.\n              isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n              serverActions: this.nextConfig.experimental.serverActions,\n            }\n          : {}),\n        isNextDataRequest,\n        resolvedUrl,\n        locale,\n        locales,\n        defaultLocale,\n        multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n        // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on\n        // asPath\n        resolvedAsPath:\n          hasServerProps || hasGetInitialProps\n            ? formatUrl({\n                // we use the original URL pathname less the _next/data prefix if\n                // present\n                pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,\n                query: origQuery,\n              })\n            : resolvedUrl,\n        experimental: {\n          ...opts.experimental,\n          isRoutePPREnabled,\n        },\n        supportsDynamicResponse,\n        isOnDemandRevalidate,\n        isDraftMode: isPreviewMode,\n        isServerAction,\n        postponed,\n        waitUntil: this.getWaitUntil(),\n        onClose: res.onClose.bind(res),\n        onAfterTaskError: undefined,\n        // only available in dev\n        setAppIsrStatus: (this as any).setAppIsrStatus,\n      }\n\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        supportsDynamicResponse = false\n        renderOpts.nextExport = true\n        renderOpts.supportsDynamicResponse = false\n        renderOpts.isStaticGeneration = true\n        renderOpts.isRevalidate = true\n        renderOpts.isDebugStaticShell = isDebugStaticShell\n        renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses\n      }\n\n      // Legacy render methods will return a render result that needs to be\n      // served by the server.\n      let result: RenderResult\n\n      if (routeModule) {\n        if (isAppRouteRouteModule(routeModule)) {\n          if (\n            // The type check here ensures that `req` is correctly typed, and the\n            // environment variable check provides dead code elimination.\n            process.env.NEXT_RUNTIME === 'edge' ||\n            !isNodeNextRequest(req) ||\n            !isNodeNextResponse(res)\n          ) {\n            throw new Error(\n              'Invariant: App Route Route Modules cannot be used in the edge runtime'\n            )\n          }\n\n          const context: AppRouteRouteHandlerContext = {\n            params: opts.params,\n            prerenderManifest,\n            renderOpts: {\n              experimental: {\n                dynamicIO: renderOpts.experimental.dynamicIO,\n                authInterrupts: renderOpts.experimental.authInterrupts,\n              },\n              supportsDynamicResponse,\n              incrementalCache,\n              cacheLifeProfiles: this.nextConfig.experimental?.cacheLife,\n              isRevalidate: isSSG,\n              waitUntil: this.getWaitUntil(),\n              onClose: res.onClose.bind(res),\n              onAfterTaskError: undefined,\n              onInstrumentationRequestError:\n                this.renderOpts.onInstrumentationRequestError,\n              buildId: this.renderOpts.buildId,\n            },\n          }\n\n          try {\n            const request = NextRequestAdapter.fromNodeNextRequest(\n              req,\n              signalFromNodeResponse(res.originalResponse)\n            )\n\n            const response = await routeModule.handle(request, context)\n\n            ;(req as any).fetchMetrics = (\n              context.renderOpts as any\n            ).fetchMetrics\n\n            const cacheTags = context.renderOpts.collectedTags\n\n            // If the request is for a static response, we can cache it so long\n            // as it's not edge.\n            if (isSSG) {\n              const blob = await response.blob()\n\n              // Copy the headers from the response.\n              const headers = toNodeOutgoingHttpHeaders(response.headers)\n\n              if (cacheTags) {\n                headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n              }\n\n              if (!headers['content-type'] && blob.type) {\n                headers['content-type'] = blob.type\n              }\n\n              const revalidate =\n                typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n                context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n                  ? false\n                  : context.renderOpts.collectedRevalidate\n\n              // Create the cache entry for the response.\n              const cacheEntry: ResponseCacheEntry = {\n                value: {\n                  kind: CachedRouteKind.APP_ROUTE,\n                  status: response.status,\n                  body: Buffer.from(await blob.arrayBuffer()),\n                  headers,\n                },\n                revalidate,\n                isFallback: false,\n              }\n\n              return cacheEntry\n            }\n\n            // Send the response now that we have copied it into the cache.\n            await sendResponse(\n              req,\n              res,\n              response,\n              context.renderOpts.pendingWaitUntil\n            )\n            return null\n          } catch (err) {\n            await this.instrumentationOnRequestError(err, req, {\n              routerKind: 'App Router',\n              routePath: pathname,\n              routeType: 'route',\n              revalidateReason: getRevalidateReason(renderOpts),\n            })\n\n            // If this is during static generation, throw the error again.\n            if (isSSG) throw err\n\n            Log.error(err)\n\n            // Otherwise, send a 500 response.\n            await sendResponse(req, res, new Response(null, { status: 500 }))\n\n            return null\n          }\n        } else if (\n          isPagesRouteModule(routeModule) ||\n          isAppPageRouteModule(routeModule)\n        ) {\n          // An OPTIONS request to a page handler is invalid.\n          if (req.method === 'OPTIONS' && !is404Page) {\n            await sendResponse(req, res, new Response(null, { status: 400 }))\n            return null\n          }\n\n          if (isPagesRouteModule(routeModule)) {\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend\n            // the object here but only updating its `clientReferenceManifest` and\n            // `nextFontManifest` properties.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n            renderOpts.clientReferenceManifest =\n              components.clientReferenceManifest\n\n            const request = isNodeNextRequest(req) ? req.originalRequest : req\n            const response = isNodeNextResponse(res)\n              ? res.originalResponse\n              : res\n\n            // Call the built-in render method on the module.\n            try {\n              result = await routeModule.render(\n                // TODO: fix this type\n                // @ts-expect-error - preexisting accepted this\n                request,\n                response,\n                {\n                  page: pathname,\n                  params: opts.params,\n                  query,\n                  renderOpts,\n                }\n              )\n            } catch (err) {\n              await this.instrumentationOnRequestError(err, req, {\n                routerKind: 'Pages Router',\n                routePath: pathname,\n                routeType: 'render',\n                revalidateReason: getRevalidateReason({\n                  isRevalidate: isSSG,\n                  isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n                }),\n              })\n              throw err\n            }\n          } else {\n            const module = components.routeModule as AppPageRouteModule\n\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n            // object here but only updating its `nextFontManifest` field.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n\n            const context: AppPageRouteHandlerContext = {\n              page: is404Page ? '/404' : pathname,\n              params: opts.params,\n              query,\n              fallbackRouteParams,\n              renderOpts,\n              serverComponentsHmrCache: this.getServerComponentsHmrCache(),\n            }\n\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't s prefetch or a server action,\n            // we should seed the resume data cache.\n            if (\n              this.nextConfig.experimental.dynamicIO &&\n              this.renderOpts.dev &&\n              !isPrefetchRSCRequest &&\n              !isServerAction\n            ) {\n              const warmup = await module.warmup(req, res, context)\n\n              // If the warmup is successful, we should use the resume data\n              // cache from the warmup.\n              if (warmup.metadata.devRenderResumeDataCache) {\n                renderOpts.devRenderResumeDataCache =\n                  warmup.metadata.devRenderResumeDataCache\n              }\n            }\n\n            // Call the built-in render method on the module.\n            result = await module.render(req, res, context)\n          }\n        } else {\n          throw new Error('Invariant: Unknown route module type')\n        }\n      } else {\n        // If we didn't match a page, we should fallback to using the legacy\n        // render method.\n        result = await this.renderHTML(req, res, pathname, query, renderOpts)\n      }\n\n      const { metadata } = result\n\n      const {\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isAppPath &&\n        isSSG &&\n        metadata.revalidate === 0 &&\n        !this.renderOpts.dev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${urlPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      // Based on the metadata, we can determine what kind of cache result we\n      // should return.\n\n      // Handle `isNotFound`.\n      if ('isNotFound' in metadata && metadata.isNotFound) {\n        return {\n          value: null,\n          revalidate: metadata.revalidate,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isRedirect`.\n      if (metadata.isRedirect) {\n        return {\n          value: {\n            kind: CachedRouteKind.REDIRECT,\n            props: metadata.pageData ?? metadata.flightData,\n          } satisfies CachedRedirectValue,\n          revalidate: metadata.revalidate,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isNull`.\n      if (result.isNull) {\n        return null\n      }\n\n      // We now have a valid HTML result that we can return to the user.\n      if (isAppPath) {\n        return {\n          value: {\n            kind: CachedRouteKind.APP_PAGE,\n            html: result,\n            headers,\n            rscData: metadata.flightData,\n            postponed: metadata.postponed,\n            status: res.statusCode,\n            segmentData: metadata.segmentData,\n          } satisfies CachedAppPageValue,\n          revalidate: metadata.revalidate,\n          isFallback: !!fallbackRouteParams,\n        } satisfies ResponseCacheEntry\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.PAGES,\n          html: result,\n          pageData: metadata.pageData ?? metadata.flightData,\n          headers,\n          status: isAppPath ? res.statusCode : undefined,\n        } satisfies CachedPageValue,\n        revalidate: metadata.revalidate,\n        isFallback: query.__nextFallback === 'true',\n      }\n    }\n\n    let responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n    }): Promise<ResponseCacheEntry | null> => {\n      const isProduction = !this.renderOpts.dev\n      const didRespond = hasResolved || res.sent\n\n      // If we haven't found the static paths for the route, then do it now.\n      if (!staticPaths && isDynamic) {\n        if (hasGetStaticPaths) {\n          const pathsResult = await this.getStaticPaths({\n            pathname,\n            requestHeaders: req.headers,\n            isAppPath,\n            page: components.page,\n          })\n\n          staticPaths = pathsResult.staticPaths\n          fallbackMode = pathsResult.fallbackMode\n        } else {\n          staticPaths = undefined\n          fallbackMode = FallbackMode.NOT_FOUND\n        }\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (\n        fallbackMode === FallbackMode.PRERENDER &&\n        isBot(req.headers['user-agent'] || '')\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !this.minimalMode\n      ) {\n        await this.render404(req, res)\n        return null\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // We use `ssgCacheKey` here as it is normalized to match the encoding\n      // from getStaticPaths along with including the locale.\n      //\n      // We use the `resolvedUrlPathname` for the development case when this\n      // is an app path since it doesn't include locale information.\n      //\n      // We decode the `resolvedUrlPathname` to correctly match the app path\n      // with prerendered paths.\n      let staticPathKey = ssgCacheKey\n      if (!staticPathKey && opts.dev && isAppPath) {\n        staticPathKey = decodePathParams(resolvedUrlPathname)\n      }\n      if (staticPathKey && query.amp) {\n        staticPathKey = staticPathKey.replace(/\\.amp$/, '')\n      }\n\n      const isPageIncludedInStaticPaths =\n        staticPathKey && staticPaths?.includes(staticPathKey)\n\n      // When experimental compile is used, no pages have been prerendered,\n      // so they should all be blocking.\n\n      // @ts-expect-error internal field\n      if (this.nextConfig.experimental.isExperimentalCompile) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // When we did not respond from cache, we need to choose to block on\n      // rendering or return a skeleton.\n      //\n      // - Data requests always block.\n      // - Blocking mode fallback always blocks.\n      // - Preview mode toggles all pages to be resolved in a blocking manner.\n      // - Non-dynamic pages should block (though this is an impossible\n      //   case in production).\n      // - Dynamic pages should return their skeleton if not defined in\n      //   getStaticPaths, then finish the data request on the client-side.\n      //\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        !this.minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isPreviewMode &&\n        isDynamic &&\n        (isProduction || !staticPaths || !isPageIncludedInStaticPaths)\n      ) {\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || (staticPaths && staticPaths?.length > 0)) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        // If this is a pages router page.\n        if (isPagesRouteModule(components.routeModule) && !isNextDataRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? (locale ? `/${locale}${pathname}` : pathname) : null,\n            // This is the response generator for the fallback shell.\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              // For the pages router, fallbacks cannot be revalidated or\n              // generated in production. In the case of a missing fallback,\n              // we return null, but if it's being revalidated, we just return\n              // the previous fallback cache entry. This preserves the previous\n              // behavior.\n              if (isProduction) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n\n              // For the pages router, fallbacks can only be generated on\n              // demand in development, so if we're not in production, and we\n              // aren't a app path, then just add the __nextFallback query\n              // and render.\n              query.__nextFallback = 'true'\n\n              // We pass `undefined` and `null` as it doesn't apply to the pages\n              // router.\n              return doRender({\n                postponed: undefined,\n                fallbackRouteParams: null,\n              })\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n        // If this is a app router page, PPR is enabled, and PFPR is also\n        // enabled, then we should use the fallback renderer.\n        else if (\n          isRoutePPREnabled &&\n          isAppPageRouteModule(components.routeModule) &&\n          !isRSCRequest\n        ) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? pathname : null,\n            // This is the response generator for the fallback shell.\n            async () =>\n              doRender({\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                fallbackRouteParams:\n                  // If we're in production of we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(pathname)\n                    : null,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n\n        // If the fallback response was set to null, then we should return null.\n        if (fallbackResponse === null) return null\n\n        // Otherwise, if we did get a fallback response, we should return it.\n        if (fallbackResponse) {\n          // Remove the revalidate from the response to prevent it from being\n          // used in the surrounding cache.\n          delete fallbackResponse.revalidate\n\n          return fallbackResponse\n        }\n      }\n\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          revalidate: 1,\n          isFallback: false,\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        isDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'didSetDefaultRouteMatches') ||\n          isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      const result = await doRender({\n        postponed,\n        fallbackRouteParams,\n      })\n      if (!result) return null\n\n      return {\n        ...result,\n        revalidate: result.revalidate,\n      }\n    }\n\n    const cacheEntry = await this.responseCache.get(\n      ssgCacheKey,\n      responseGenerator,\n      {\n        routeKind:\n          // If the route module is not defined, we can assume it's a page being\n          // rendered and thus check isAppPath.\n          routeModule?.definition.kind ??\n          (isAppPath ? RouteKind.APP_PAGE : RouteKind.PAGES),\n        incrementalCache,\n        isOnDemandRevalidate,\n        isPrefetch: req.headers.purpose === 'prefetch',\n        isRoutePPREnabled,\n      }\n    )\n\n    if (isPrefetchRSCRequest && typeof segmentPrefetchHeader === 'string') {\n      // This is a prefetch request issued by the client Segment Cache. These\n      // should never reach the application layer (lambda). We should either\n      // respond from the cache (HIT) or respond with 204 No Content (MISS).\n      if (\n        cacheEntry !== null &&\n        // This is always true at runtime but is needed to refine the type\n        // of cacheEntry.value to CachedAppPageValue, because the outer\n        // ResponseCacheEntry is not a discriminated union.\n        cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n        cacheEntry.value.segmentData\n      ) {\n        const matchedSegment = cacheEntry.value.segmentData.get(\n          segmentPrefetchHeader\n        )\n        if (matchedSegment !== undefined) {\n          // Cache hit\n          return {\n            type: 'rsc',\n            body: RenderResult.fromStatic(matchedSegment),\n            // TODO: Eventually this should use revalidate time of the\n            // individual segment, not the whole page.\n            revalidate: cacheEntry.revalidate,\n          }\n        }\n      }\n\n      // Cache miss. Either a cache entry for this route has not been generated,\n      // or there's no match for the requested segment. Regardless, respond with\n      // a 204 No Content. We don't bother to respond with 404 in cases where\n      // the segment does not exist, because these requests are only issued by\n      // the client cache.\n      // TODO: If this is a request for the route tree (the special /_tree\n      // segment), we should *always* respond with a tree, even if PPR\n      // is disabled.\n      res.statusCode = 204\n      if (isRoutePPREnabled) {\n        // Set a header to indicate that PPR is enabled for this route. This\n        // lets the client distinguish between a regular cache miss and a cache\n        // miss due to PPR being disabled.\n        // NOTE: Theoretically, when PPR is enabled, there should *never* be\n        // a cache miss because we should generate a fallback route. So this\n        // is mostly defensive.\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n      return {\n        type: 'rsc',\n        body: RenderResult.fromStatic(''),\n        revalidate: cacheEntry?.revalidate,\n      }\n    }\n\n    if (isPreviewMode) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    if (!cacheEntry) {\n      if (ssgCacheKey && !(isOnDemandRevalidate && revalidateOnlyGenerated)) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n      return null\n    }\n\n    // If we're not in minimal mode and the cache entry that was returned was a\n    // app page fallback, then we need to kick off the dynamic shell generation.\n    if (\n      ssgCacheKey &&\n      !this.minimalMode &&\n      isRoutePPREnabled &&\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      cacheEntry.isFallback &&\n      !isOnDemandRevalidate &&\n      // When we're debugging the fallback shell, we don't want to regenerate\n      // the route shell.\n      !isDebugFallbackShell &&\n      process.env.DISABLE_ROUTE_SHELL_GENERATION !== 'true'\n    ) {\n      scheduleOnNextTick(async () => {\n        try {\n          await this.responseCache.get(\n            ssgCacheKey,\n            () =>\n              doRender({\n                // We're an on-demand request, so we don't need to pass in the\n                // fallbackRouteParams.\n                fallbackRouteParams: null,\n                postponed: undefined,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isOnDemandRevalidate: true,\n              isPrefetch: false,\n              isRoutePPREnabled: true,\n            }\n          )\n        } catch (err) {\n          console.error('Error occurred while rendering dynamic shell', err)\n        }\n      })\n    }\n\n    const didPostpone =\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      typeof cacheEntry.value.postponed === 'string'\n\n    if (\n      isSSG &&\n      // We don't want to send a cache header for requests that contain dynamic\n      // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n      // request, then we should set the cache header.\n      !isDynamicRSCRequest &&\n      (!didPostpone || isPrefetchRSCRequest)\n    ) {\n      if (!this.minimalMode) {\n        // set x-nextjs-cache header to match the header\n        // we set for the image-optimizer\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n      // Set a header used by the client router to signal the response is static\n      // and should respect the `static` cache staleTime value.\n      res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n    }\n\n    const { value: cachedData } = cacheEntry\n\n    // If the cache value is an image, we should error early.\n    if (cachedData?.kind === CachedRouteKind.IMAGE) {\n      throw new Error('invariant SSG should not return an image cache value')\n    }\n\n    // Coerce the revalidate parameter from the render.\n    let revalidate: Revalidate | undefined\n\n    // If this is a resume request in minimal mode it is streamed with dynamic\n    // content and should not be cached.\n    if (minimalPostponed) {\n      revalidate = 0\n    }\n\n    // If this is in minimal mode and this is a flight request that isn't a\n    // prefetch request while PPR is enabled, it cannot be cached as it contains\n    // dynamic content.\n    else if (\n      this.minimalMode &&\n      isRSCRequest &&\n      !isPrefetchRSCRequest &&\n      isRoutePPREnabled\n    ) {\n      revalidate = 0\n    } else if (!this.renderOpts.dev || (hasServerProps && !isNextDataRequest)) {\n      // If this is a preview mode request, we shouldn't cache it\n      if (isPreviewMode) {\n        revalidate = 0\n      }\n\n      // If this isn't SSG, then we should set change the header only if it is\n      // not set already.\n      else if (!isSSG) {\n        if (!res.getHeader('Cache-Control')) {\n          revalidate = 0\n        }\n      }\n\n      // If we are rendering the 404 page we derive the cache-control\n      // revalidate period from the value that trigged the not found\n      // to be rendered. So if `getStaticProps` returns\n      // { notFound: true, revalidate 60 } the revalidate period should\n      // be 60 but if a static asset 404s directly it should have a revalidate\n      // period of 0 so that it doesn't get cached unexpectedly by a CDN\n      else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n        revalidate =\n          typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate\n      } else if (is500Page) {\n        revalidate = 0\n      }\n\n      // If the cache entry has a revalidate value that's a number, use it.\n      else if (typeof cacheEntry.revalidate === 'number') {\n        if (cacheEntry.revalidate < 1) {\n          throw new Error(\n            `Invalid revalidate configuration provided: ${cacheEntry.revalidate} < 1`\n          )\n        }\n\n        revalidate = cacheEntry.revalidate\n      }\n      // Otherwise if the revalidate value is false, then we should use the cache\n      // time of one year.\n      else if (cacheEntry.revalidate === false) {\n        revalidate = CACHE_ONE_YEAR\n      }\n    }\n\n    cacheEntry.revalidate = revalidate\n\n    // If there's a callback for `onCacheEntry`, call it with the cache entry\n    // and the revalidate options.\n    const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n    if (onCacheEntry) {\n      const finished = await onCacheEntry(\n        {\n          ...cacheEntry,\n          // TODO: remove this when upstream doesn't\n          // always expect this value to be \"PAGE\"\n          value: {\n            ...cacheEntry.value,\n            kind:\n              cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n                ? 'PAGE'\n                : cacheEntry.value?.kind,\n          },\n        },\n        {\n          url: getRequestMeta(req, 'initURL'),\n        }\n      )\n      if (finished) {\n        // TODO: maybe we have to end the request?\n        return null\n      }\n    }\n\n    if (!cachedData) {\n      // add revalidate metadata before rendering 404 page\n      // so that we can use this as source of truth for the\n      // cache-control header instead of what the 404 page returns\n      // for the revalidate value\n      addRequestMeta(req, 'notFoundRevalidate', cacheEntry.revalidate)\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        typeof cacheEntry.revalidate !== 'undefined' &&\n        !res.getHeader('Cache-Control')\n      ) {\n        res.setHeader(\n          'Cache-Control',\n          formatRevalidate({\n            revalidate: cacheEntry.revalidate,\n            expireTime: this.nextConfig.expireTime,\n          })\n        )\n      }\n      if (isNextDataRequest) {\n        res.statusCode = 404\n        res.body('{\"notFound\":true}').send()\n        return null\n      }\n\n      if (this.renderOpts.dev) {\n        query.__nextNotFoundSrcPage = pathname\n      }\n      await this.render404(req, res, { pathname, query }, false)\n      return null\n    } else if (cachedData.kind === CachedRouteKind.REDIRECT) {\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        typeof cacheEntry.revalidate !== 'undefined' &&\n        !res.getHeader('Cache-Control')\n      ) {\n        res.setHeader(\n          'Cache-Control',\n          formatRevalidate({\n            revalidate: cacheEntry.revalidate,\n            expireTime: this.nextConfig.expireTime,\n          })\n        )\n      }\n\n      if (isNextDataRequest) {\n        return {\n          type: 'json',\n          body: RenderResult.fromStatic(\n            // @TODO: Handle flight data.\n            JSON.stringify(cachedData.props)\n          ),\n          revalidate: cacheEntry.revalidate,\n        }\n      } else {\n        await handleRedirect(cachedData.props)\n        return null\n      }\n    } else if (cachedData.kind === CachedRouteKind.APP_ROUTE) {\n      const headers = fromNodeOutgoingHttpHeaders(cachedData.headers)\n\n      if (!(this.minimalMode && isSSG)) {\n        headers.delete(NEXT_CACHE_TAGS_HEADER)\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        typeof cacheEntry.revalidate !== 'undefined' &&\n        !res.getHeader('Cache-Control') &&\n        !headers.get('Cache-Control')\n      ) {\n        headers.set(\n          'Cache-Control',\n          formatRevalidate({\n            revalidate: cacheEntry.revalidate,\n            expireTime: this.nextConfig.expireTime,\n          })\n        )\n      }\n\n      await sendResponse(\n        req,\n        res,\n        new Response(cachedData.body, {\n          headers,\n          status: cachedData.status || 200,\n        })\n      )\n      return null\n    } else if (cachedData.kind === CachedRouteKind.APP_PAGE) {\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!this.minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      if (\n        this.minimalMode &&\n        isSSG &&\n        cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      ) {\n        res.setHeader(\n          NEXT_CACHE_TAGS_HEADER,\n          cachedData.headers[NEXT_CACHE_TAGS_HEADER] as string\n        )\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isPreviewMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return {\n            type: 'rsc',\n            body: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            revalidate: isDynamicRSCRequest ? 0 : cacheEntry.revalidate,\n          }\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(cachedData.rscData),\n          revalidate: cacheEntry.revalidate,\n        }\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || this.minimalMode) {\n        return {\n          type: 'html',\n          body,\n          revalidate: cacheEntry.revalidate,\n        }\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return { type: 'html', body, revalidate: 0 }\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        postponed: cachedData.postponed,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return {\n        type: 'html',\n        body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        revalidate: 0,\n      }\n    } else if (isNextDataRequest) {\n      return {\n        type: 'json',\n        body: RenderResult.fromStatic(JSON.stringify(cachedData.pageData)),\n        revalidate: cacheEntry.revalidate,\n      }\n    } else {\n      return {\n        type: 'html',\n        body: cachedData.html,\n        revalidate: cacheEntry.revalidate,\n      }\n    }\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): MiddlewareRoutingItem | undefined\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback = !!query._nextBubbleNoFallback\n    delete query[NEXT_RSC_UNION_QUERY]\n    delete query._nextBubbleNoFallback\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromQuery(pathname, query),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        ctx.query.__nextCustomErrorRender = '1'\n        await this.renderErrorToResponse(ctx, err)\n        delete ctx.query.__nextCustomErrorRender\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (\n          (this.minimalMode && process.env.NEXT_RUNTIME !== 'edge') ||\n          this.renderOpts.dev\n        ) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    if (\n      this.getMiddleware() &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${query.__nextLocale ? `/${query.__nextLocale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('content-type', 'application/json')\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic(''),\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !ctx.query.__nextCustomErrorRender &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            type: 'html',\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic('Internal Server Error'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    if (this.nextConfig.i18n) {\n      query.__nextLocale ||= this.nextConfig.i18n.defaultLocale\n      query.__nextDefaultLocale ||= this.nextConfig.i18n.defaultLocale\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["NoFallbackError", "WrappedBuildError", "Server", "Error", "constructor", "innerError", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "prefetchRSC", "match", "normalize", "headers", "RSC_HEADER", "toLowerCase", "NEXT_ROUTER_PREFETCH_HEADER", "addRequestMeta", "rsc", "stripFlightHeaders", "url", "parsed", "parseUrl", "formatUrl", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "matchNextDataPathname", "path", "buildId", "process", "env", "NEXT_RUNTIME", "getRequestMeta", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "getRouteFromAssetPath", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "query", "__next<PERSON><PERSON><PERSON>", "__nextDefaultLocale", "__nextInferredLocaleFromDefault", "__nextDataReq", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "execOnce", "Log", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "customServer", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "formatHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "I18NProvider", "localeNormalizer", "LocaleRouteNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "checkIsAppPPREnabled", "ppr", "RSCPathnameNormalizer", "PrefetchRSCPathnameNormalizer", "NextDataPathnameNormalizer", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "strictNextHead", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "expireTime", "clientTraceMetadata", "dynamicIO", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "setConfig", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "reloadMatchers", "manifest<PERSON><PERSON>der", "ServerManifestLoader", "name", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "DefaultRouteMatcherManager", "PagesRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "NextRequestHint", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "getTracer", "withPropagatedContext", "trace", "BaseServerSpan", "spanName", "kind", "SpanKind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "patchSetHeaderWithCookieSupport", "isNodeNextResponse", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "normalizeRepeatedSlashes", "redirect", "body", "send", "URLSearchParams", "isNodeNextRequest", "xForwardedProto", "isHttps", "socket", "encrypted", "toString", "remoteAddress", "validate<PERSON><PERSON>y", "attachRequestMeta", "getHostname", "parseUrlUtil", "replace", "pathnameInfo", "getNextPathnameInfo", "removePathPrefix", "useMatchedPathHeader", "MATCHED_PATH_HEADER", "<PERSON><PERSON><PERSON>", "URL", "urlPathname", "NEXT_RESUME_HEADER", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "localeAnalysisResult", "inferredFromDefault", "denormalizePagePath", "srcPathname", "pageIsDynamic", "isDynamicRoute", "definition", "utils", "getUtils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "pathnameBeforeRewrite", "rewriteParams", "handleRewrites", "rewriteParamKeys", "didRewrite", "routeParamKeys", "Set", "key", "value", "normalizeNextQueryParam", "normalizedKey", "add", "paramsResult", "normalizeDynamicRouteParams", "hasValidParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "opts", "routeParams", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeVercelUrl", "defaultRouteRegex", "groups", "DecodeError", "NormalizeError", "renderError", "Boolean", "webServerConfig", "protocol", "parsedFullUrl", "incrementalCache", "getIncrementalCache", "requestHeaders", "requestProtocol", "_globalThis", "__next<PERSON>ache<PERSON>and<PERSON>", "expiredTags", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "handler", "values", "receiveExpiredTags", "resetRequestCache", "__incrementalCache", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "normalizeLocalePath", "startsWith", "result", "response", "Response", "bubble", "run", "code", "isBubbledError", "getProperError", "getRequestHandlerWithMetadata", "meta", "getRequestHandler", "setRequestMeta", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "normalizeAppPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "isBotRequest", "isBot", "payload", "originalStatus", "type", "revalidate", "sent", "<PERSON><PERSON><PERSON><PERSON>", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "getBuiltinRequestContext", "waitUntil", "getInternalWaitUntil", "hasPage", "isBlockedPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "parseFallbackField", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "isInterceptionRouteAppPath", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "addedNextUrlToVary", "NEXT_URL", "components", "prerenderManifest", "cacheEntry", "UNDERSCORE_NOT_FOUND_ROUTE", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "hasGetStaticPaths", "isServerAction", "getIsServerAction", "hasGetInitialProps", "Component", "getInitialProps", "isSSG", "getStaticProps", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "isDynamic", "pathsResult", "resolvedWithoutSlash", "removeTrailingSlash", "includes", "routes", "toRoute", "isNextDataRequest", "isPrefetchRSCRequest", "routeModule", "couldSupportPPR", "isAppPageRouteModule", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "isDynamicRSCRequest", "segmentPrefetchHeader", "STATIC_STATUS_PAGES", "parseInt", "slice", "RenderResult", "fromStatic", "isSupportedDocument", "Document", "NEXT_BUILTIN_DOCUMENT", "previewData", "isPreviewMode", "tryGetPreviewData", "multiZoneDraftMode", "isEdgeRuntime", "runtime", "isOnDemandRevalidate", "revalidateOnlyGenerated", "checkIsOnDemandRevalidate", "handleRedirect", "pageData", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "getRedirectStatus", "ssgCacheKey", "decodePathParams", "doR<PERSON>", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hadTrailingSlash", "resolvedUrl", "isRevalidate", "serverActions", "resolvedAsPath", "isDraftMode", "onClose", "onAfterTaskError", "setAppIsrStatus", "nextExport", "isStaticGeneration", "isAppRouteRouteModule", "context", "request", "NextRequestAdapter", "fromNodeNextRequest", "signalFromNodeResponse", "handle", "fetchMetrics", "cacheTags", "collectedTags", "blob", "toNodeOutgoingHttpHeaders", "NEXT_CACHE_TAGS_HEADER", "collectedRevalidate", "INFINITE_CACHE", "CachedRouteKind", "APP_ROUTE", "status", "from", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON>", "sendResponse", "pendingWaitUntil", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "isPagesRouteModule", "clientReferenceManifest", "module", "warmup", "metadata", "devRenderResumeDataCache", "renderHTML", "fetchTags", "staticBailoutInfo", "description", "stack", "message", "indexOf", "isNotFound", "isRedirect", "REDIRECT", "props", "flightData", "isNull", "APP_PAGE", "html", "rscData", "segmentData", "PAGES", "__<PERSON><PERSON><PERSON><PERSON>", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "FallbackMode", "NOT_FOUND", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "static<PERSON><PERSON><PERSON><PERSON>", "isPageIncludedInStaticPaths", "fallbackResponse", "previousFallbackCacheEntry", "toResponseCacheEntry", "routeKind", "RouteKind", "getFallbackRouteParams", "isPrefetch", "purpose", "matchedSegment", "NEXT_DID_POSTPONE_HEADER", "DISABLE_ROUTE_SHELL_GENERATION", "scheduleOnNextTick", "didPostpone", "isMiss", "NEXT_IS_PRERENDER_HEADER", "cachedData", "IMAGE", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "CACHE_ONE_YEAR", "onCacheEntry", "formatRevalidate", "__nextNotFoundSrcPage", "JSON", "stringify", "fromNodeOutgoingHttpHeaders", "delete", "set", "Array", "isArray", "v", "append<PERSON><PERSON>er", "chain", "ReadableStream", "start", "controller", "enqueue", "ENCODED_TAGS", "CLOSED", "BODY_AND_HTML", "transformer", "TransformStream", "readable", "pipeTo", "writable", "catch", "abort", "e", "stripLocale", "splitPath", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "_nextBubbleNoFallback", "NEXT_RSC_UNION_QUERY", "fromQuery", "matchAll", "invokeOutput", "MissingStaticPage", "initUrl", "rewroteUrl", "renderErrorToResponse", "__nextCustomErrorRender", "isWrappedError", "isError", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "is404", "using404Page", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "statusPage", "NODE_ENV", "removeRequestMeta", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": ";;;;;;;;;;;;;;;;IAmTaA,eAAe;eAAfA;;IAIAC,iBAAiB;eAAjBA;;IAoBb,OAirHC;eAjrH6BC;;;gCApUvB;+BAsBA;uBAOA;qBA2BgD;gCACxB;gCACG;+BACJ;2BAQvB;wBACwB;0BACW;uCAChB;4BAKnB;wBAEuB;uBACR;qEACG;qCACW;qCACA;6DACf;6BACI;iEACe;6BAMjC;kCAC0B;0BACA;6BACL;0BACa;qCACL;kCAU7B;uCAK+B;4CACK;6CACC;8CACC;8CACA;2CACH;sCACL;wBACe;4BACrB;8BACF;8BACA;wBAKtB;4BAQA;qCAC6B;6BAI7B;uCAC+B;8EACJ;kCACD;qBACK;oCACH;wBAK5B;6BACuC;0BACH;yCACT;oCACS;yBACnB;yBAE8B;gCACN;qBACX;uCAI9B;6BACsB;yBACG;wBACI;2BACV;0BAEuB;wBACZ;2BACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmI5B,MAAMF,wBAAwBG;AAAO;AAIrC,MAAMF,0BAA0BE;IAGrCC,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAae,MAAeH;IAkGlBI,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACxD,AAACC,WAAmBC,0BAA0B,GAC9CC;IACN;IAoBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YAsCrB,uBAiEE,mCAQL;aAyDXC,mBAAgE,CACtEC,KACAC,MACAC;gBAII,+BAWO;YAbX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,KAAI,gCAAA,IAAI,CAACC,WAAW,CAACC,WAAW,qBAA5B,8BAA8BC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC3DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACC,WAAW,CAACE,SAAS,CACzDL,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIQ,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCV,IAAIQ,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,GAAG;gBACzDE,IAAAA,2BAAc,EAACZ,KAAK,gBAAgB;gBACpCY,IAAAA,2BAAc,EAACZ,KAAK,wBAAwB;YAC9C,OAAO,KAAI,wBAAA,IAAI,CAACI,WAAW,CAACS,GAAG,qBAApB,sBAAsBP,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACS,GAAG,CAACN,SAAS,CACjDL,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIQ,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,GAAG;gBACxCE,IAAAA,2BAAc,EAACZ,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIQ,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;gBACvCM,IAAAA,sCAAkB,EAACd,IAAIQ,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIR,IAAIQ,OAAO,CAACC,4BAAU,CAACC,WAAW,GAAG,KAAK,KAAK;gBACxDE,IAAAA,2BAAc,EAACZ,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIQ,OAAO,CAACG,6CAA2B,CAACD,WAAW,GAAG,KAAK,KAAK;oBAClEE,IAAAA,2BAAc,EAACZ,KAAK,wBAAwB;gBAC9C;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIA,IAAIe,GAAG,EAAE;gBACX,MAAMC,SAASC,IAAAA,UAAQ,EAACjB,IAAIe,GAAG;gBAC/BC,OAAOb,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIe,GAAG,GAAGG,IAAAA,WAAS,EAACF;YACtB;YAEA,OAAO;QACT;aAEQG,wBACN,OAAOnB,KAAKoB,KAAKlB;YACf,MAAMmB,aAAa,IAAI,CAACC,aAAa;YACrC,MAAMC,SAASC,IAAAA,4CAAqB,EAACtB,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAACoB,UAAU,CAACA,OAAOE,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAIF,OAAOE,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IACEC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,2BAAc,EAAC9B,KAAK,qBACpB;oBACA,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAAC+B,SAAS,CAAC/B,KAAKoB,KAAKlB;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1BqB,OAAOE,IAAI,CAACO,KAAK;YAEjB,MAAMC,YAAYV,OAAOE,IAAI,CAACF,OAAOE,IAAI,CAACS,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAAC/B,KAAKoB,KAAKlB;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAEoB,OAAOE,IAAI,CAACW,IAAI,CAAC,MAAM;YAC1CjC,WAAWkC,IAAAA,8BAAqB,EAAClC,UAAU;YAE3C,iDAAiD;YACjD,IAAIkB,YAAY;gBACd,IAAI,IAAI,CAAC7B,UAAU,CAAC8C,aAAa,IAAI,CAACnC,SAASgC,QAAQ,CAAC,MAAM;oBAC5DhC,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAAC8C,aAAa,IAC9BnC,SAAS+B,MAAM,GAAG,KAClB/B,SAASgC,QAAQ,CAAC,MAClB;oBACAhC,WAAWA,SAASoC,SAAS,CAAC,GAAGpC,SAAS+B,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACM,YAAY,EAAE;oBAEJxC;gBADjB,gDAAgD;gBAChD,MAAMyC,WAAWzC,wBAAAA,oBAAAA,IAAKQ,OAAO,CAACkC,IAAI,qBAAjB1C,kBAAmB2C,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACjC,WAAW;gBAEhE,MAAMkC,eAAe,IAAI,CAACJ,YAAY,CAACK,kBAAkB,CAACJ;gBAC1D,MAAMK,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,KAAI,IAAI,CAACN,YAAY,CAACO,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACR,YAAY,CAACS,OAAO,CAAC9C;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAI6C,iBAAiBE,cAAc,EAAE;oBACnC/C,WAAW6C,iBAAiB7C,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChED,UAAUiD,KAAK,CAACC,YAAY,GAAGJ,iBAAiBE,cAAc;gBAC9DhD,UAAUiD,KAAK,CAACE,mBAAmB,GAAGP;gBAEtC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpC,OAAOhD,UAAUiD,KAAK,CAACG,+BAA+B;gBACxD;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAACN,iBAAiBE,cAAc,IAAI,CAAC7B,YAAY;oBACnDnB,UAAUiD,KAAK,CAACC,YAAY,GAAGN;oBAC/B,MAAM,IAAI,CAACf,SAAS,CAAC/B,KAAKoB,KAAKlB;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrBD,UAAUiD,KAAK,CAACI,aAAa,GAAG;YAEhC,OAAO;QACT;aAEQC,yBAGN,IAAM;aAEAC,8BAGN,IAAM;aAEAC,kCAGN,IAAM;QAmuBV;;;;;;GAMC,QACOnD,YAAY,CAACJ;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAACuD,IAAI,EAAE;gBACzBvD,YAAYwD,IAAI,CAAC,IAAI,CAACxD,WAAW,CAACuD,IAAI;YACxC;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAACvD,WAAW,CAACC,WAAW,EAAE;gBAChCD,YAAYwD,IAAI,CAAC,IAAI,CAACxD,WAAW,CAACC,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACD,WAAW,CAACS,GAAG,EAAE;gBACxBT,YAAYwD,IAAI,CAAC,IAAI,CAACxD,WAAW,CAACS,GAAG;YACvC;YAEA,KAAK,MAAMgD,cAAczD,YAAa;gBACpC,IAAI,CAACyD,WAAWvD,KAAK,CAACH,WAAW;gBAEjC,OAAO0D,WAAWtD,SAAS,CAACJ,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQ2D,6BAGJ,OAAO9D,KAAKoB,KAAKL;YACnB,IAAIgD,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAACxD,KAAKoB,KAAKL;YAC3D,IAAIgD,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAAC5C,qBAAqB,CAACnB,KAAKoB,KAAKL;gBACtD,IAAIgD,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAgCUG,WAAoB;aACpBC,kBAAwC;aA8rE1CC,uBAAuBC,IAAAA,eAAQ,EAAC;YACtCC,KAAIC,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QA9zGE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnBC,eAAe,IAAI,EACnBpC,QAAQ,EACRqC,IAAI,EACJC,qBAAqB,EACtB,GAAGjF;QAEJ,IAAI,CAACiF,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGlF;QAErB,IAAI,CAAC0E,GAAG,GACN7C,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS2C,MAAMS,QAAQ,QAAQC,OAAO,CAACV;QAEtE,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACU,aAAa,CAAC;YAAER;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACnF,UAAU,GAAGkF;QAClB,IAAI,CAACjC,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAAC2C,aAAa,GAAGC,IAAAA,8BAAc,EAAC,IAAI,CAAC5C,QAAQ;QACnD;QACA,IAAI,CAACqC,IAAI,GAAGA;QACZ,IAAI,CAACQ,OAAO,GACV3D,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,IAAI,CAACrC,UAAU,CAAC8F,OAAO,GACvBL,QAAQ,QAAQ7C,IAAI,CAAC,IAAI,CAACoC,GAAG,EAAE,IAAI,CAAChF,UAAU,CAAC8F,OAAO;QAC5D,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACb,eAAe,IAAI,CAACc,eAAe;QAExD,IAAI,CAAClD,YAAY,GAAG,EAAA,wBAAA,IAAI,CAAChD,UAAU,CAACmG,IAAI,qBAApB,sBAAsBC,OAAO,IAC7C,IAAIC,0BAAY,CAAC,IAAI,CAACrG,UAAU,CAACmG,IAAI,IACrC9F;QAEJ,yEAAyE;QACzE,IAAI,CAACiG,gBAAgB,GAAG,IAAI,CAACtD,YAAY,GACrC,IAAIuD,4CAAqB,CAAC,IAAI,CAACvD,YAAY,IAC3C3C;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJmG,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC3G,UAAU;QAEnB,IAAI,CAACkC,OAAO,GAAG,IAAI,CAAC0E,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBzB,eAAe,CAAC,CAACjD,QAAQC,GAAG,CAAC0E,yBAAyB;QAExD,IAAI,CAACtC,kBAAkB,GAAG,IAAI,CAACuC,qBAAqB,CAAC5B;QAErD,IAAI,CAAC6B,eAAe,GAClB,IAAI,CAACxC,kBAAkB,CAACyC,GAAG,IAC3BC,IAAAA,yBAAoB,EAAC,IAAI,CAAClH,UAAU,CAACC,YAAY,CAACkH,GAAG;QAEvD,IAAI,CAACvG,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCS,KACE,IAAI,CAACmD,kBAAkB,CAACyC,GAAG,IAAI,IAAI,CAAC7B,WAAW,GAC3C,IAAIgC,0BAAqB,KACzB/G;YACNQ,aACE,IAAI,CAACmG,eAAe,IAAI,IAAI,CAAC5B,WAAW,GACpC,IAAIiC,0CAA6B,KACjChH;YACN8D,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,IAAI6C,oCAA0B,CAAC,IAAI,CAACpF,OAAO,IAC3C7B;QACN;QAEA,IAAI,CAACkH,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAEhD,IAAIrF,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvCF,QAAQC,GAAG,CAACqF,kBAAkB,GAAG,IAAI,CAACzH,UAAU,CAAC0H,YAAY,IAAI;QACnE;QAEA,IAAI,CAACC,UAAU,GAAG;YAChBC,yBAAyB;YACzB9E,eAAe,IAAI,CAAC9C,UAAU,CAAC8C,aAAa;YAC5C4E,cAAc,IAAI,CAAC1H,UAAU,CAAC0H,YAAY;YAC1CG,gBAAgB,IAAI,CAAC7H,UAAU,CAACC,YAAY,CAAC4H,cAAc,IAAI;YAC/DC,iBAAiB,IAAI,CAAC9H,UAAU,CAAC8H,eAAe;YAChDC,eAAe,IAAI,CAAC/H,UAAU,CAACgI,GAAG,CAACD,aAAa,IAAI;YACpD7F,SAAS,IAAI,CAACA,OAAO;YACrByE;YACAsB,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjD9C,cAAcA,iBAAiB,OAAO,OAAOhF;YAC7C+H,kBAAkB,GAAE,oCAAA,IAAI,CAACpI,UAAU,CAACC,YAAY,CAAC+H,GAAG,qBAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAACtI,UAAU,CAACsI,QAAQ;YAClCC,QAAQ,IAAI,CAACvI,UAAU,CAACuI,MAAM;YAC9BC,aAAa,IAAI,CAACxI,UAAU,CAACC,YAAY,CAACuI,WAAW;YACrDC,kBAAkB,IAAI,CAACzI,UAAU,CAAC0I,MAAM;YACxCC,mBAAmB,IAAI,CAAC3I,UAAU,CAACC,YAAY,CAAC0I,iBAAiB;YACjEC,yBACE,IAAI,CAAC5I,UAAU,CAACC,YAAY,CAAC2I,uBAAuB;YACtDC,aAAa,GAAE,yBAAA,IAAI,CAAC7I,UAAU,CAACmG,IAAI,qBAApB,uBAAsB2C,OAAO;YAC5ChD,SAAS,IAAI,CAACA,OAAO;YACrBiD,kBAAkB,IAAI,CAACvE,kBAAkB,CAACyC,GAAG;YAC7C+B,mBAAmB,IAAI,CAAChJ,UAAU,CAACC,YAAY,CAACgJ,SAAS;YACzDC,gBAAgB,IAAI,CAAClJ,UAAU,CAACC,YAAY,CAACkJ,KAAK;YAClDC,aAAa,IAAI,CAACpJ,UAAU,CAACoJ,WAAW,GACpC,IAAI,CAACpJ,UAAU,CAACoJ,WAAW,GAC3B/I;YACJgJ,oBAAoB,IAAI,CAACrJ,UAAU,CAACC,YAAY,CAACoJ,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAC/C,qBAAqB/D,MAAM,GAAG,IACtC+D,sBACApG;YAEN,uDAAuD;YACvDoJ,uBAAuB,IAAI,CAACzJ,UAAU,CAACC,YAAY,CAACwJ,qBAAqB;YACzExJ,cAAc;gBACZyJ,YAAY,IAAI,CAAC1J,UAAU,CAAC0J,UAAU;gBACtCC,qBAAqB,IAAI,CAAC3J,UAAU,CAACC,YAAY,CAAC0J,mBAAmB;gBACrEC,WAAW,IAAI,CAAC5J,UAAU,CAACC,YAAY,CAAC2J,SAAS,IAAI;gBACrDC,WAAW,IAAI,CAAC7J,UAAU,CAACC,YAAY,CAAC4J,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAAC9J,UAAU,CAACC,YAAY,CAAC6J,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAAClK,UAAU,CAACkK,qBAAqB;QAC9D;QAEA,4DAA4D;QAC5DC,IAAAA,gCAAS,EAAC;YACR3D;YACAC;QACF;QAEA,IAAI,CAAC2D,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAACrE;QACpB,IAAI,CAACsE,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAE9F;QAAI;IACnD;IAEU+F,iBAAiB;QACzB,OAAO,IAAI,CAACN,QAAQ,CAACE,MAAM;IAC7B;IAuKUD,mBAAwC;QAChD,yEAAyE;QACzE,MAAMM,iBAAiB,IAAIC,0CAAoB,CAAC,CAACC;YAC/C,OAAQA;gBACN,KAAKC,yBAAc;oBACjB,OAAO,IAAI,CAACjB,gBAAgB,MAAM;gBACpC,KAAKkB,6BAAkB;oBACrB,OAAO,IAAI,CAAChB,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,IAAIY,sDAA0B;QAEpE,8BAA8B;QAC9BZ,SAASxG,IAAI,CACX,IAAIqH,oDAAyB,CAC3B,IAAI,CAAC3F,OAAO,EACZqF,gBACA,IAAI,CAACnI,YAAY;QAIrB,uCAAuC;QACvC4H,SAASxG,IAAI,CACX,IAAIsH,0DAA4B,CAC9B,IAAI,CAAC5F,OAAO,EACZqF,gBACA,IAAI,CAACnI,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACwB,kBAAkB,CAACyC,GAAG,EAAE;YAC/B,gCAAgC;YAChC2D,SAASxG,IAAI,CACX,IAAIuH,wDAA2B,CAAC,IAAI,CAAC7F,OAAO,EAAEqF;YAEhDP,SAASxG,IAAI,CACX,IAAIwH,0DAA4B,CAAC,IAAI,CAAC9F,OAAO,EAAEqF;QAEnD;QAEA,OAAOP;IACT;IAEA,MAAgBZ,8BACd,GAAG6B,IAAqD,EACxD;QACA,MAAM,CAACC,KAAKtL,KAAKuL,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,OAAM,IAAI,CAACA,eAAe,CAACC,cAAc,oBAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,MAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACE7J,MAAMzB,IAAIe,GAAG,IAAI;oBACjB2K,QAAQ1L,IAAI0L,MAAM,IAAI;oBACtB,gEAAgE;oBAChElL,SACER,eAAe2L,wBAAe,GAC1B5C,OAAO6C,WAAW,CAAC5L,IAAIQ,OAAO,CAACqL,OAAO,MACtC7L,IAAIQ,OAAO;gBACnB,GACA+K;YAEJ,EAAE,OAAOO,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASX,GAAU,EAAQ;QAChC,IAAI,IAAI,CAAC7G,KAAK,EAAE;QAChBH,KAAI0H,KAAK,CAACV;IACZ;IAEA,MAAaY,cACXlM,GAAkB,EAClBoB,GAAmB,EACnBlB,SAAkC,EACnB;QACf,MAAM,IAAI,CAACiM,OAAO;QAClB,MAAMT,SAAS1L,IAAI0L,MAAM,CAACU,WAAW;QAErC,MAAMC,SAASC,IAAAA,iBAAS;QACxB,OAAOD,OAAOE,qBAAqB,CAACvM,IAAIQ,OAAO,EAAE;YAC/C,OAAO6L,OAAOG,KAAK,CACjBC,0BAAc,CAACP,aAAa,EAC5B;gBACEQ,UAAU,GAAGhB,OAAO,CAAC,EAAE1L,IAAIe,GAAG,EAAE;gBAChC4L,MAAMC,gBAAQ,CAACC,MAAM;gBACrBC,YAAY;oBACV,eAAepB;oBACf,eAAe1L,IAAIe,GAAG;gBACxB;YACF,GACA,OAAOgM,OACL,IAAI,CAACC,iBAAiB,CAAChN,KAAKoB,KAAKlB,WAAW+M,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,eAAepL,IAAAA,2BAAc,EAAC9B,KAAK,mBAAmB;oBAC5D+M,KAAKI,aAAa,CAAC;wBACjB,oBAAoB/L,IAAIgM,UAAU;wBAClC,YAAYF;oBACd;oBAEA,MAAMG,qBAAqBhB,OAAOiB,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvBd,0BAAc,CAACP,aAAa,EAC5B;wBACAH,QAAQxH,IAAI,CACV,CAAC,2BAA2B,EAAE8I,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAM3C,OAAOqC,eACT,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAE8B,OAAO,GACxB,GAAG9B,OAAO,CAAC,EAAE8B,OAAO;wBAExBT,KAAKI,aAAa,CAAC;4BACjB,cAAcK;4BACd,cAAcA;4BACd,kBAAkB3C;wBACpB;wBACAkC,KAAKU,UAAU,CAAC5C;oBAClB,OAAO;wBACLkC,KAAKU,UAAU,CACbP,eACI,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAE1L,IAAIe,GAAG,EAAE,GAC1B,GAAG2K,OAAO,CAAC,EAAE1L,IAAIe,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAciM,kBACZhN,GAAkB,EAClBoB,GAAmB,EACnBlB,SAAkC,EACnB;QACf,IAAI;gBAiDKwN,yBAS4BA,0BAI9B,oBAagB,qBAKY;YA/EjC,qCAAqC;YACrC,MAAM,IAAI,CAACtD,QAAQ,CAACuD,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;YAClDC,IAAAA,+CAA+B,EAC7B5N,KACA6N,IAAAA,2BAAkB,EAACzM,OAAOA,IAAI0M,gBAAgB,GAAG1M;YAGnD,MAAM2M,WAAW,AAAC/N,CAAAA,IAAIe,GAAG,IAAI,EAAC,EAAG4B,KAAK,CAAC,KAAK;YAC5C,MAAMqL,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,8BAAAA,WAAY1N,KAAK,CAAC,cAAc;gBAClC,MAAM2N,WAAWC,IAAAA,+BAAwB,EAAClO,IAAIe,GAAG;gBACjDK,IAAI+M,QAAQ,CAACF,UAAU,KAAKG,IAAI,CAACH,UAAUI,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACnO,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIe,GAAG,EAAE;oBACZ,MAAM,IAAI3B,MAAM;gBAClB;gBAEAc,YAAYe,IAAAA,UAAQ,EAACjB,IAAIe,GAAG,EAAG;YACjC;YAEA,IAAI,CAACb,UAAUC,QAAQ,EAAE;gBACvB,MAAM,IAAIf,MAAM;YAClB;YAEA,iFAAiF;YACjF,IAAI,OAAOc,UAAUiD,KAAK,KAAK,UAAU;gBACvCjD,UAAUiD,KAAK,GAAG4F,OAAO6C,WAAW,CAClC,IAAI0C,gBAAgBpO,UAAUiD,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAEuK,kBAAkB,IAAI,EAAE,GAAGa,IAAAA,0BAAiB,EAACvO,OAAOA,MAAM,CAAC;YACnE,MAAMwO,kBAAkBd,mCAAAA,gBAAiBlN,OAAO,CAAC,oBAAoB;YACrE,MAAMiO,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,EAAEd,oCAAAA,0BAAAA,gBAAiBgB,MAAM,qBAAxB,AAAChB,wBAAuCiB,SAAS;YAEvD3O,IAAIQ,OAAO,CAAC,mBAAmB,KAAKR,IAAIQ,OAAO,CAAC,OAAO,IAAI,IAAI,CAACiC,QAAQ;YACxEzC,IAAIQ,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAACsE,IAAI,GACzC,IAAI,CAACA,IAAI,CAAC8J,QAAQ,KAClBH,UACE,QACA;YACNzO,IAAIQ,OAAO,CAAC,oBAAoB,KAAKiO,UAAU,UAAU;YACzDzO,IAAIQ,OAAO,CAAC,kBAAkB,KAAKkN,oCAAAA,2BAAAA,gBAAiBgB,MAAM,qBAAvBhB,yBAAyBmB,aAAa;YAEzE,0EAA0E;YAC1E,6CAA6C;YAC7C,IAAI,GAAC,qBAAA,IAAI,CAACrM,YAAY,qBAAjB,mBAAmBsM,aAAa,CAAC5O,UAAUiD,KAAK,IAAG;gBACtD,OAAOjD,UAAUiD,KAAK,CAACC,YAAY;gBACnC,OAAOlD,UAAUiD,KAAK,CAACE,mBAAmB;gBAC1C,OAAOnD,UAAUiD,KAAK,CAACG,+BAA+B;YACxD;YAEA,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAACyL,iBAAiB,CAAC/O,KAAKE;YAE5B,IAAI6D,WAAW,MAAM,IAAI,CAAChE,gBAAgB,CAACC,KAAKoB,KAAKlB;YACrD,IAAI6D,UAAU;YAEd,MAAMnB,gBAAe,sBAAA,IAAI,CAACJ,YAAY,qBAAjB,oBAAmBK,kBAAkB,CACxDmM,IAAAA,wBAAW,EAAC9O,WAAWF,IAAIQ,OAAO;YAGpC,MAAMsC,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,OAAI,wBAAA,IAAI,CAACtD,UAAU,CAACmG,IAAI,qBAApB,sBAAsB7C,aAAa;YACpE5C,UAAUiD,KAAK,CAACE,mBAAmB,GAAGP;YAEtC,MAAM/B,MAAMkO,IAAAA,kBAAY,EAACjP,IAAIe,GAAG,CAACmO,OAAO,CAAC,QAAQ;YACjD,MAAMC,eAAeC,IAAAA,wCAAmB,EAACrO,IAAIZ,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3BgD,cAAc,IAAI,CAACA,YAAY;YACjC;YACAzB,IAAIZ,QAAQ,GAAGgP,aAAahP,QAAQ;YAEpC,IAAIgP,aAAarH,QAAQ,EAAE;gBACzB9H,IAAIe,GAAG,GAAGsO,IAAAA,kCAAgB,EAACrP,IAAIe,GAAG,EAAG,IAAI,CAACvB,UAAU,CAACsI,QAAQ;YAC/D;YAEA,MAAMwH,uBACJ,IAAI,CAAC1K,WAAW,IAAI,OAAO5E,IAAIQ,OAAO,CAAC+O,+BAAmB,CAAC,KAAK;YAElE,uCAAuC;YACvC,IAAID,sBAAsB;gBACxB,IAAI;wBAuBE,wBA2ByB,qBAkDjB;oBAnGZ,IAAI,IAAI,CAACtL,kBAAkB,CAACyC,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAIzG,IAAIe,GAAG,CAACT,KAAK,CAAC,mBAAmB;4BACnCN,IAAIe,GAAG,GAAGf,IAAIe,GAAG,CAACmO,OAAO,CAAC,YAAY;wBACxC;wBACAhP,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAUqP,WAAW,EAAE,GAAG,IAAIC,IAClCzP,IAAIQ,OAAO,CAAC+O,+BAAmB,CAAC,EAChC;oBAGF,IAAI,EAAEpP,UAAUuP,WAAW,EAAE,GAAG,IAAID,IAAIzP,IAAIe,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,KAAI,yBAAA,IAAI,CAACX,WAAW,CAACuD,IAAI,qBAArB,uBAAuBrD,KAAK,CAACoP,cAAc;wBAC7CxP,UAAUiD,KAAK,CAACI,aAAa,GAAG;oBAClC,OAGK,IACH,IAAI,CAACiD,eAAe,IACpB,IAAI,CAAC5B,WAAW,IAChB5E,IAAIQ,OAAO,CAACmP,8BAAkB,CAAC,KAAK,OACpC3P,IAAI0L,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAM0C,OAAsB,EAAE;wBAC9B,WAAW,MAAMwB,SAAS5P,IAAIoO,IAAI,CAAE;4BAClCA,KAAKxK,IAAI,CAACgM;wBACZ;wBACA,MAAMC,YAAYC,OAAOC,MAAM,CAAC3B,MAAMQ,QAAQ,CAAC;wBAE/ChO,IAAAA,2BAAc,EAACZ,KAAK,aAAa6P;oBACnC;oBAEAL,cAAc,IAAI,CAACjP,SAAS,CAACiP;oBAC7B,MAAMQ,oBAAoB,IAAI,CAACC,iBAAiB,CAACP;oBAEjD,8CAA8C;oBAC9C,MAAMQ,wBAAuB,sBAAA,IAAI,CAAC1N,YAAY,qBAAjB,oBAAmBS,OAAO,CAACuM,aAAa;wBACnE1M;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAIoN,sBAAsB;wBACxBhQ,UAAUiD,KAAK,CAACC,YAAY,GAAG8M,qBAAqBhN,cAAc;wBAElE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAIgN,qBAAqBC,mBAAmB,EAAE;4BAC5CjQ,UAAUiD,KAAK,CAACG,+BAA+B,GAAG;wBACpD,OAAO;4BACL,OAAOpD,UAAUiD,KAAK,CAACG,+BAA+B;wBACxD;oBACF;oBAEA,0CAA0C;oBAC1CkM,cAAcY,IAAAA,wCAAmB,EAACZ;oBAElC,IAAIa,cAAcb;oBAClB,IAAIc,gBAAgBC,IAAAA,sBAAc,EAACF;oBAEnC,IAAI,CAACC,eAAe;wBAClB,MAAMhQ,QAAQ,MAAM,IAAI,CAAC8J,QAAQ,CAAC9J,KAAK,CAAC+P,aAAa;4BACnD1K,MAAMuK;wBACR;wBAEA,6DAA6D;wBAC7D,IAAI5P,OAAO;4BACT+P,cAAc/P,MAAMkQ,UAAU,CAACrQ,QAAQ;4BACvC,iDAAiD;4BACjDmQ,gBAAgB,OAAOhQ,MAAMiB,MAAM,KAAK;wBAC1C;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAI2O,sBAAsB;wBACxBV,cAAcU,qBAAqB/P,QAAQ;oBAC7C;oBAEA,MAAMsQ,QAAQC,IAAAA,qBAAQ,EAAC;wBACrBJ;wBACAK,MAAMN;wBACN1K,MAAM,IAAI,CAACnG,UAAU,CAACmG,IAAI;wBAC1BmC,UAAU,IAAI,CAACtI,UAAU,CAACsI,QAAQ;wBAClC8I,UAAU,EAAA,0BAAA,IAAI,CAACC,iBAAiB,uBAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAACzR,UAAU,CAACC,YAAY,CAACyR,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIpO,iBAAiB,CAACqM,aAAagC,MAAM,EAAE;wBACzCjR,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAE2C,gBAAgB5C,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,MAAMiR,wBAAwBlR,UAAUC,QAAQ;oBAChD,MAAMkR,gBAAgBZ,MAAMa,cAAc,CAACtR,KAAKE;oBAChD,MAAMqR,mBAAmBxI,OAAOC,IAAI,CAACqI;oBACrC,MAAMG,aAAaJ,0BAA0BlR,UAAUC,QAAQ;oBAE/D,IAAIqR,cAActR,UAAUC,QAAQ,EAAE;wBACpCS,IAAAA,2BAAc,EAACZ,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBACA,MAAMsR,iBAAiB,IAAIC;oBAE3B,KAAK,MAAMC,OAAO5I,OAAOC,IAAI,CAAC9I,UAAUiD,KAAK,EAAG;wBAC9C,MAAMyO,QAAQ1R,UAAUiD,KAAK,CAACwO,IAAI;wBAElCE,IAAAA,+BAAuB,EAACF,KAAK,CAACG;4BAC5B,IAAI,CAAC5R,WAAW,QAAO,YAAY;4BAEnCA,UAAUiD,KAAK,CAAC2O,cAAc,GAAGF;4BACjCH,eAAeM,GAAG,CAACD;4BACnB,OAAO5R,UAAUiD,KAAK,CAACwO,IAAI;wBAC7B;oBACF;oBAEA,yDAAyD;oBACzD,IAAIrB,eAAe;wBACjB,IAAI/O,SAAiC,CAAC;wBAEtC,IAAIyQ,eAAevB,MAAMwB,2BAA2B,CAClD/R,UAAUiD,KAAK;wBAGjB,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAAC6O,aAAaE,cAAc,IAC5B,CAAC3B,IAAAA,sBAAc,EAACP,oBAChB;4BACA,IAAImC,gBAAgB1B,MAAM2B,mBAAmB,oBAAzB3B,MAAM2B,mBAAmB,MAAzB3B,OAA4BT;4BAEhD,IAAImC,eAAe;gCACjB1B,MAAMwB,2BAA2B,CAACE;gCAClCpJ,OAAOsJ,MAAM,CAACL,aAAazQ,MAAM,EAAE4Q;gCACnCH,aAAaE,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IACE,8DAA8D;wBAC9D1C,gBAAgB,YAChB,CAACwC,aAAaE,cAAc,IAC5B,CAAC3B,IAAAA,sBAAc,EAACf,cAChB;4BACA,IAAI2C,gBAAgB1B,MAAM2B,mBAAmB,oBAAzB3B,MAAM2B,mBAAmB,MAAzB3B,OAA4BjB;4BAEhD,IAAI2C,eAAe;gCACjB,MAAMG,kBACJ7B,MAAMwB,2BAA2B,CAACE;gCAEpC,IAAIG,gBAAgBJ,cAAc,EAAE;oCAClCnJ,OAAOsJ,MAAM,CAAC9Q,QAAQ4Q;oCACtBH,eAAeM;gCACjB;4BACF;wBACF;wBAEA,IAAIN,aAAaE,cAAc,EAAE;4BAC/B3Q,SAASyQ,aAAazQ,MAAM;wBAC9B;wBAEA,IACEvB,IAAIQ,OAAO,CAAC,sBAAsB,IAClC+P,IAAAA,sBAAc,EAACf,gBACf,CAACwC,aAAaE,cAAc,EAC5B;4BACA,MAAMK,OAA+B,CAAC;4BACtC,MAAMC,cAAc/B,MAAMgC,yBAAyB,CACjDzS,KACAuS,MACArS,UAAUiD,KAAK,CAACC,YAAY,IAAI;4BAGlC,kEAAkE;4BAClE,qBAAqB;4BACrB,IAAImP,KAAKpB,MAAM,EAAE;gCACfjR,UAAUiD,KAAK,CAACC,YAAY,GAAGmP,KAAKpB,MAAM;gCAE1C,6DAA6D;gCAC7D,mDAAmD;gCACnD,OAAOjR,UAAUiD,KAAK,CAACG,+BAA+B;4BACxD;4BACA0O,eAAevB,MAAMwB,2BAA2B,CAC9CO,aACA;4BAGF,IAAIR,aAAaE,cAAc,EAAE;gCAC/B3Q,SAASyQ,aAAazQ,MAAM;4BAC9B;wBACF;wBAEA,uDAAuD;wBACvD,IACEkP,MAAMiC,mBAAmB,IACzB1C,sBAAsBK,eACtB,CAAC2B,aAAaE,cAAc,IAC5B,CAACzB,MAAMwB,2BAA2B,CAAC;4BAAE,GAAG1Q,MAAM;wBAAC,GAAG,MAC/C2Q,cAAc,EACjB;4BACA3Q,SAASkP,MAAMiC,mBAAmB;4BAElC,8DAA8D;4BAC9D,kBAAkB;4BAClB9R,IAAAA,2BAAc,EAACZ,KAAK,6BAA6B;wBACnD;wBAEA,IAAIuB,QAAQ;4BACViO,cAAciB,MAAMkC,sBAAsB,CAACtC,aAAa9O;4BACxDvB,IAAIe,GAAG,GAAG0P,MAAMkC,sBAAsB,CAAC3S,IAAIe,GAAG,EAAGQ;wBACnD;oBACF;oBAEA,IAAI+O,iBAAiBkB,YAAY;4BAGdf;wBAFjBA,MAAMmC,kBAAkB,CAAC5S,KAAK,MAAM;+BAC/BuR;+BACAxI,OAAOC,IAAI,CAACyH,EAAAA,2BAAAA,MAAMoC,iBAAiB,qBAAvBpC,yBAAyBqC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACA,KAAK,MAAMnB,OAAOF,eAAgB;wBAChC,OAAOvR,UAAUiD,KAAK,CAACwO,IAAI;oBAC7B;oBACAzR,UAAUC,QAAQ,GAAGqP;oBACrBzO,IAAIZ,QAAQ,GAAGD,UAAUC,QAAQ;oBACjC4D,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAAC9D,KAAKoB,KAAKlB;oBAC3D,IAAI6D,UAAU;gBAChB,EAAE,OAAOuH,KAAK;oBACZ,IAAIA,eAAeyH,kBAAW,IAAIzH,eAAe0H,qBAAc,EAAE;wBAC/D5R,IAAIgM,UAAU,GAAG;wBACjB,OAAO,IAAI,CAAC6F,WAAW,CAAC,MAAMjT,KAAKoB,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAMkK;gBACR;YACF;YAEA1K,IAAAA,2BAAc,EAACZ,KAAK,kBAAkBkT,QAAQtQ;YAE9C,IAAIuM,aAAagC,MAAM,EAAE;gBACvBnR,IAAIe,GAAG,GAAGG,IAAAA,WAAS,EAACH;gBACpBH,IAAAA,2BAAc,EAACZ,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAAC4E,WAAW,IAAI,CAAC1E,UAAUiD,KAAK,CAACC,YAAY,EAAE;gBACtD,gEAAgE;gBAChE,IAAI+L,aAAagC,MAAM,EAAE;oBACvBjR,UAAUiD,KAAK,CAACC,YAAY,GAAG+L,aAAagC,MAAM;gBACpD,OAGK,IAAIrO,eAAe;oBACtB5C,UAAUiD,KAAK,CAACC,YAAY,GAAGN;oBAC/B5C,UAAUiD,KAAK,CAACG,+BAA+B,GAAG;gBACpD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAC,AAAC,IAAI,CAAC0B,aAAa,CAASmO,eAAe,IAC5C,CAACrR,IAAAA,2BAAc,EAAC9B,KAAK,qBACrB;gBACA,IAAIoT,WAA+B;gBAEnC,IAAI;oBACF,MAAMC,gBAAgB,IAAI5D,IACxB3N,IAAAA,2BAAc,EAAC9B,KAAK,cAAc,KAClC;oBAEFoT,WAAWC,cAAcD,QAAQ;gBACnC,EAAE,OAAM,CAAC;gBAET,MAAME,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgBzK,OAAOsJ,MAAM,CAAC,CAAC,GAAGrS,IAAIQ,OAAO;oBAC7CiT,iBAAiBL,SAAS7Q,SAAS,CAAC,GAAG6Q,SAASlR,MAAM,GAAG;gBAG3D;gBAEA,MAAMwR,cAKF/T;gBAEJ,IAAI+T,YAAYC,mBAAmB,EAAE;wBAEhC3T;oBADH,MAAM4T,cACJ,EAAC5T,kDAAAA,IAAIQ,OAAO,CAACqT,8CAAkC,CAAC,qBAAhD,AAAC7T,gDAA4D2C,KAAK,CAChE,SACG,EAAE;oBAET,KAAK,MAAMmR,WAAW/K,OAAOgL,MAAM,CACjCL,YAAYC,mBAAmB,EAC9B;wBACD,IAAI,OAAOG,QAAQE,kBAAkB,KAAK,YAAY;4BACpD,MAAMF,QAAQE,kBAAkB,IAAIJ;wBACtC;oBACF;gBACF;gBAEAN,iBAAiBW,iBAAiB;gBAClCrT,IAAAA,2BAAc,EAACZ,KAAK,oBAAoBsT;gBACtC3T,WAAmBuU,kBAAkB,GAAGZ;YAC5C;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAACxR,IAAAA,2BAAc,EAAC9B,KAAK,6BAA6B;gBACpDY,IAAAA,2BAAc,EACZZ,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAM4U,aAAarS,IAAAA,2BAAc,EAAC9B,KAAK;YACvC,MAAMoU,gBACJ,CAAC9E,wBACD3N,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BsS;YAEF,IAAIC,eAAe;oBAkCf;gBAjCF,MAAMC,eAAevS,IAAAA,2BAAc,EAAC9B,KAAK;gBACzC,IAAIqU,cAAc;oBAChB,MAAMC,cAAcxS,IAAAA,2BAAc,EAAC9B,KAAK;oBAExC,IAAIsU,aAAa;wBACfvL,OAAOsJ,MAAM,CAACnS,UAAUiD,KAAK,EAAEmR;oBACjC;oBAEAlT,IAAIgM,UAAU,GAAGiH;oBACjB,IAAI/I,MAAoBxJ,IAAAA,2BAAc,EAAC9B,KAAK,kBAAkB;oBAE9D,OAAO,IAAI,CAACiT,WAAW,CAAC3H,KAAKtL,KAAKoB,KAAK,WAAWlB,UAAUiD,KAAK;gBACnE;gBAEA,MAAMoR,oBAAoB,IAAI9E,IAAI0E,cAAc,KAAK;gBACrD,MAAMK,qBAAqBpF,IAAAA,wCAAmB,EAC5CmF,kBAAkBpU,QAAQ,EAC1B;oBACEX,YAAY,IAAI,CAACA,UAAU;oBAC3BiV,WAAW;gBACb;gBAGF,IAAID,mBAAmBrD,MAAM,EAAE;oBAC7BjR,UAAUiD,KAAK,CAACC,YAAY,GAAGoR,mBAAmBrD,MAAM;gBAC1D;gBAEA,IAAIjR,UAAUC,QAAQ,KAAKoU,kBAAkBpU,QAAQ,EAAE;oBACrDD,UAAUC,QAAQ,GAAGoU,kBAAkBpU,QAAQ;oBAC/CS,IAAAA,2BAAc,EAACZ,KAAK,cAAcwU,mBAAmBrU,QAAQ;gBAC/D;gBACA,MAAMuU,kBAAkBC,IAAAA,wCAAmB,EACzCtF,IAAAA,kCAAgB,EAACnP,UAAUC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAACsI,QAAQ,IAAI,KACjE,EAAA,yBAAA,IAAI,CAACtI,UAAU,CAACmG,IAAI,qBAApB,uBAAsBC,OAAO,KAAI,EAAE;gBAGrC,IAAI8O,gBAAgBxR,cAAc,EAAE;oBAClChD,UAAUiD,KAAK,CAACC,YAAY,GAAGsR,gBAAgBxR,cAAc;gBAC/D;gBACAhD,UAAUC,QAAQ,GAAGuU,gBAAgBvU,QAAQ;gBAE7C,KAAK,MAAMwR,OAAO5I,OAAOC,IAAI,CAAC9I,UAAUiD,KAAK,EAAG;oBAC9C,IAAI,CAACwO,IAAIiD,UAAU,CAAC,aAAa,CAACjD,IAAIiD,UAAU,CAAC,UAAU;wBACzD,OAAO1U,UAAUiD,KAAK,CAACwO,IAAI;oBAC7B;gBACF;gBACA,MAAM2C,cAAcxS,IAAAA,2BAAc,EAAC9B,KAAK;gBAExC,IAAIsU,aAAa;oBACfvL,OAAOsJ,MAAM,CAACnS,UAAUiD,KAAK,EAAEmR;gBACjC;gBAEAvQ,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAAC9D,KAAKoB,KAAKlB;gBAC3D,IAAI6D,UAAU;gBAEd,MAAM,IAAI,CAACN,2BAA2B,CAACzD,KAAKoB,KAAKlB;gBACjD;YACF;YAEA,IACEyB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BC,IAAAA,2BAAc,EAAC9B,KAAK,qBACpB;gBACA+D,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAAC9D,KAAKoB,KAAKlB;gBAC3D,IAAI6D,UAAU;gBAEdA,WAAW,MAAM,IAAI,CAACL,+BAA+B,CACnD1D,KACAoB,KACAlB;gBAEF,IAAI6D,UAAU;gBAEd,MAAMuH,MAAM,IAAIlM;gBACdkM,IAAYuJ,MAAM,GAAG;oBACrBC,UAAU,IAAIC,SAAS,MAAM;wBAC3BvU,SAAS;4BACP,qBAAqB;wBACvB;oBACF;gBACF;gBACE8K,IAAY0J,MAAM,GAAG;gBACvB,MAAM1J;YACR;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAACgE,wBAAwBH,aAAarH,QAAQ,EAAE;gBAClD5H,UAAUC,QAAQ,GAAGkP,IAAAA,kCAAgB,EACnCnP,UAAUC,QAAQ,EAClBgP,aAAarH,QAAQ;YAEzB;YAEA1G,IAAIgM,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAAC6H,GAAG,CAACjV,KAAKoB,KAAKlB;QAClC,EAAE,OAAOoL,KAAU;YACjB,IAAIA,eAAerM,iBAAiB;gBAClC,MAAMqM;YACR;YAEA,IACE,AAACA,OAAO,OAAOA,QAAQ,YAAYA,IAAI4J,IAAI,KAAK,qBAChD5J,eAAeyH,kBAAW,IAC1BzH,eAAe0H,qBAAc,EAC7B;gBACA5R,IAAIgM,UAAU,GAAG;gBACjB,OAAO,IAAI,CAAC6F,WAAW,CAAC,MAAMjT,KAAKoB,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAACwD,WAAW,IAChB,IAAI,CAACuC,UAAU,CAACxC,GAAG,IAClBwQ,IAAAA,sBAAc,EAAC7J,QAAQA,IAAI0J,MAAM,EAClC;gBACA,MAAM1J;YACR;YACA,IAAI,CAACW,QAAQ,CAACmJ,IAAAA,uBAAc,EAAC9J;YAC7BlK,IAAIgM,UAAU,GAAG;YACjBhM,IAAIgN,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAkDA;;GAEC,GACD,AAAOgH,8BACLC,IAAiB,EACkC;QACnD,MAAMxB,UAAU,IAAI,CAACyB,iBAAiB;QACtC,OAAO,CAACvV,KAAKoB,KAAKlB;YAChBsV,IAAAA,2BAAc,EAACxV,KAAKsV;YACpB,OAAOxB,QAAQ9T,KAAKoB,KAAKlB;QAC3B;IACF;IAEOqV,oBAGL;QACA,OAAO,IAAI,CAACrJ,aAAa,CAACzC,IAAI,CAAC,IAAI;IACrC;IAQOc,eAAekL,MAAe,EAAQ;QAC3C,IAAI,CAACtO,UAAU,CAACjB,WAAW,GAAGuP,SAASA,OAAOvG,OAAO,CAAC,OAAO,MAAM;IACrE;IAIA;;;GAGC,GACD,MAAa/C,UAAyB;QACpC,IAAI,IAAI,CAACjI,QAAQ,EAAE;QAEnB,IAAI,IAAI,CAACC,eAAe,KAAK,MAAM;YACjC,6BAA6B;YAC7B,IAAI,CAACqH,eAAe,GAAG,MAAM,IAAI,CAACkK,yBAAyB;YAC3D,IAAI,CAACvR,eAAe,GAAG,IAAI,CAACwR,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAAC1R,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgBwR,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3B5L,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDjB,OAAOC,IAAI,CAAC,IAAI,CAACc,gBAAgB,IAAI,CAAC,GAAGgM,OAAO,CAAC,CAACC;YAChD,MAAMC,iBAAiBC,IAAAA,0BAAgB,EAACF;YACxC,IAAI,CAAC/L,aAAa,CAACgM,eAAe,EAAE;gBAClChM,aAAa,CAACgM,eAAe,GAAG,EAAE;YACpC;YACAhM,aAAa,CAACgM,eAAe,CAACpS,IAAI,CAACmS;QACrC;QACA,OAAO/L;IACT;IAEA,MAAgBiL,IACdjV,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA6B,EACd;QACf,OAAOoM,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACwI,GAAG,EAAE,UAC3C,IAAI,CAACiB,OAAO,CAAClW,KAAKoB,KAAKlB;IAE3B;IAEA,MAAcgW,QACZlW,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA6B,EACd;QACf,MAAM,IAAI,CAACuD,2BAA2B,CAACzD,KAAKoB,KAAKlB;IACnD;IAEA,MAAciW,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAO/J,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC0J,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,eAAeC,IAAAA,YAAK,EAACH,eAAerW,GAAG,CAACQ,OAAO,CAAC,aAAa,IAAI;QACvE,MAAM+K,MAAqD;YACzD,GAAG8K,cAAc;YACjBlP,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB,CAACmP;gBAC1BC,OAAO,CAAC,CAACD;YACX;QACF;QACA,MAAME,UAAU,MAAML,GAAG7K;QACzB,IAAIkL,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAEzW,GAAG,EAAEoB,GAAG,EAAE,GAAGmK;QACrB,MAAMmL,iBAAiBtV,IAAIgM,UAAU;QACrC,MAAM,EAAEgB,IAAI,EAAEuI,IAAI,EAAE,GAAGF;QACvB,IAAI,EAAEG,UAAU,EAAE,GAAGH;QACrB,IAAI,CAACrV,IAAIyV,IAAI,EAAE;YACb,MAAM,EAAE1Q,aAAa,EAAEmB,eAAe,EAAE3C,GAAG,EAAE,GAAG,IAAI,CAACwC,UAAU;YAE/D,oDAAoD;YACpD,IAAIxC,KAAK;gBACPvD,IAAI0V,SAAS,CAAC,iBAAiB;gBAC/BF,aAAa/W;YACf;YAEA,MAAM,IAAI,CAACkX,gBAAgB,CAAC/W,KAAKoB,KAAK;gBACpCyT,QAAQzG;gBACRuI;gBACAxQ;gBACAmB;gBACAsP;gBACA1N,YAAY,IAAI,CAAC1J,UAAU,CAAC0J,UAAU;YACxC;YACA9H,IAAIgM,UAAU,GAAGsJ;QACnB;IACF;IAEA,MAAcM,cACZZ,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAM9K,MAAqD;YACzD,GAAG8K,cAAc;YACjBlP,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAMqP,UAAU,MAAML,GAAG7K;QACzB,IAAIkL,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQrI,IAAI,CAAC6I,iBAAiB;IACvC;IAEA,MAAaC,OACXlX,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBgD,QAA4B,CAAC,CAAC,EAC9BjD,SAAkC,EAClCiX,iBAAiB,KAAK,EACP;QACf,OAAO7K,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACyK,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAACpX,KAAKoB,KAAKjB,UAAUgD,OAAOjD,WAAWiX;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,wBAAwBC,IAAAA,+CAAwB;QACtD,IAAID,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBE,SAAS;QACxC;QAEA,IAAI,IAAI,CAAC5S,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAO/E;QACT;QAEA,OAAO,IAAI,CAAC4X,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAO5X;IACT;IAEA,MAAcuX,WACZpX,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBgD,QAA4B,CAAC,CAAC,EAC9BjD,SAAkC,EAClCiX,iBAAiB,KAAK,EACP;YAyBZnX;QAxBH,IAAI,CAACG,SAASyU,UAAU,CAAC,MAAM;YAC7B7I,QAAQxH,IAAI,CACV,CAAC,8BAA8B,EAAEpE,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAACgH,UAAU,CAACtC,YAAY,IAC5B1E,aAAa,YACb,CAAE,MAAM,IAAI,CAACuX,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxCvX,WAAW;QACb;QAEA,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAACgX,kBACD,CAAC,IAAI,CAACvS,WAAW,IACjB,CAACzB,MAAMI,aAAa,IACnBvD,CAAAA,EAAAA,WAAAA,IAAIe,GAAG,qBAAPf,SAASM,KAAK,CAAC,kBACb,IAAI,CAACmF,YAAY,IAAIzF,IAAIe,GAAG,CAAET,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAAC4L,aAAa,CAAClM,KAAKoB,KAAKlB;QACtC;QAEA,IAAIyX,IAAAA,qBAAa,EAACxX,WAAW;YAC3B,OAAO,IAAI,CAAC4B,SAAS,CAAC/B,KAAKoB,KAAKlB;QAClC;QAEA,OAAO,IAAI,CAACiW,IAAI,CAAC,CAAC5K,MAAQ,IAAI,CAACqM,gBAAgB,CAACrM,MAAM;YACpDvL;YACAoB;YACAjB;YACAgD;QACF;IACF;IAEA,MAAgB0U,eAAe,EAC7B1X,QAAQ,EAMT,EAGE;YAGC;QAFF,+DAA+D;QAC/D,MAAM2X,iBACJ,oDAAA,IAAI,CAACpQ,oBAAoB,GAAGqQ,aAAa,CAAC5X,SAAS,qBAAnD,kDAAqD6Q,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvCgH,aAAanY;YACboY,cAAcC,IAAAA,4BAAkB,EAACJ;QACnC;IACF;IAEA,MAAcK,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,OAAO/L,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAAC0L,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,OACEC,IAAAA,8CAA0B,EAACD,qBAC3B,IAAI,CAACtO,yBAAyB,CAACwO,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACJ;QACrB;IAEJ;IAEUK,cACR7Y,GAAkB,EAClBoB,GAAmB,EACnB0X,SAAkB,EAClBN,gBAAwB,EAClB;QACN,MAAMO,iBAAiB,GAAGtY,4BAAU,CAAC,EAAE,EAAEuY,+CAA6B,CAAC,EAAE,EAAErY,6CAA2B,CAAC,EAAE,EAAEsY,qDAAmC,EAAE;QAChJ,MAAM/L,eAAepL,IAAAA,2BAAc,EAAC9B,KAAK,mBAAmB;QAE5D,IAAIkZ,qBAAqB;QAEzB,IAAIJ,aAAa,IAAI,CAACP,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FpX,IAAI0V,SAAS,CAAC,QAAQ,GAAGiC,eAAe,EAAE,EAAEI,0BAAQ,EAAE;YACtDD,qBAAqB;QACvB,OAAO,IAAIJ,aAAa5L,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnG9L,IAAI0V,SAAS,CAAC,QAAQiC;QACxB;QAEA,IAAI,CAACG,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAOlZ,IAAIQ,OAAO,CAAC2Y,0BAAQ,CAAC;QAC9B;IACF;IAEA,MAAcb,mCACZ,EACEtY,GAAG,EACHoB,GAAG,EACHjB,QAAQ,EACRgH,YAAYoL,IAAI,EAC8B,EAChD,EAAE6G,UAAU,EAAEjW,KAAK,EAAwB,EACV;YAcJiW,uBA6JzBC,OA0GA,uBAIY,wBAq5BdC,mBAkCAA;QA/sCF,IAAInZ,aAAaoZ,qCAA0B,EAAE;YAC3CpZ,WAAW;QACb;QACA,MAAMqZ,kBAAkBrZ,aAAa;QACrC,MAAMsZ,YACJtZ,aAAa,UAAWqZ,mBAAmBpY,IAAIgM,UAAU,KAAK;QAChE,MAAMsM,YACJvZ,aAAa,UAAWqZ,mBAAmBpY,IAAIgM,UAAU,KAAK;QAChE,MAAM0L,YAAYM,WAAWN,SAAS,KAAK;QAE3C,MAAMa,iBAAiB,CAAC,CAACP,WAAWQ,kBAAkB;QACtD,IAAIC,oBAAoB,CAAC,CAACT,WAAWvB,cAAc;QACnD,MAAMiC,iBAAiBC,IAAAA,0CAAiB,EAAC/Z;QACzC,MAAMga,qBAAqB,CAAC,GAACZ,wBAAAA,WAAWa,SAAS,qBAApBb,sBAAsBc,eAAe;QAClE,IAAIC,QAAQ,CAAC,CAACf,WAAWgB,cAAc;QAEvC,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAI1K,cAAczO,IAAAA,UAAQ,EAACjB,IAAIe,GAAG,IAAI,IAAIZ,QAAQ,IAAI;QAEtD,IAAIka,sBAAsBvY,IAAAA,2BAAc,EAAC9B,KAAK,iBAAiB0P;QAE/D,IAAI,CAACmJ,aAAa,CAAC7Y,KAAKoB,KAAK0X,WAAWuB;QAExC,IAAIrC;QACJ,IAAIC;QACJ,IAAIqC,cAAc;QAElB,MAAMC,YAAYhK,IAAAA,sBAAc,EAAC6I,WAAWzI,IAAI;QAEhD,MAAM0I,oBAAoB,IAAI,CAAC3R,oBAAoB;QAEnD,IAAIoR,aAAayB,WAAW;YAC1B,MAAMC,cAAc,MAAM,IAAI,CAAC3C,cAAc,CAAC;gBAC5C1X;gBACAwQ,MAAMyI,WAAWzI,IAAI;gBACrBmI;gBACAtF,gBAAgBxT,IAAIQ,OAAO;YAC7B;YAEAwX,cAAcwC,YAAYxC,WAAW;YACrCC,eAAeuC,YAAYvC,YAAY;YACvCqC,cAAc,OAAOrC,iBAAiB;YAEtC,IAAI,IAAI,CAACzY,UAAU,CAAC0I,MAAM,KAAK,UAAU;gBACvC,MAAMyI,OAAOyI,WAAWzI,IAAI;gBAC5B,IAAI,CAACqH,aAAa;oBAChB,MAAM,IAAI5Y,MACR,CAAC,MAAM,EAAEuR,KAAK,wGAAwG,CAAC;gBAE3H;gBAEA,MAAM8J,uBAAuBC,IAAAA,wCAAmB,EAACL;gBACjD,IAAI,CAACrC,YAAY2C,QAAQ,CAACF,uBAAuB;oBAC/C,MAAM,IAAIrb,MACR,CAAC,MAAM,EAAEuR,KAAK,oBAAoB,EAAE8J,qBAAqB,8EAA8E,CAAC;gBAE5I;YACF;YAEA,IAAIH,aAAa;gBACfT,oBAAoB;YACtB;QACF;QAEA,IACES,gBACAtC,+BAAAA,YAAa2C,QAAQ,CAACN,yBACtB,mDAAmD;QACnD,+BAA+B;QAC/Bra,IAAIQ,OAAO,CAAC,sBAAsB,EAClC;YACA2Z,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAAChT,UAAU,CAACxC,GAAG,EAAE;YAC/BwV,UAAU,CAAC,CAACd,kBAAkBuB,MAAM,CAACC,IAAAA,gBAAO,EAAC1a,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAM2a,oBACJ,CAAC,CACC3X,CAAAA,MAAMI,aAAa,IAClBvD,IAAIQ,OAAO,CAAC,gBAAgB,IAC3B,AAAC,IAAI,CAACwE,aAAa,CAASmO,eAAe,KAE9CgH,CAAAA,SAASR,cAAa;QAEzB;;;KAGC,GACD,MAAMoB,uBACJjZ,IAAAA,2BAAc,EAAC9B,KAAK,2BAA2B;QAEjD,uFAAuF;QAEvF,MAAMkN,eAAepL,IAAAA,2BAAc,EAAC9B,KAAK,mBAAmB;QAE5D,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACma,SACDna,IAAIQ,OAAO,CAAC,wBAAwB,IACpC,CAAEiZ,CAAAA,aAAatZ,aAAa,SAAQ,GACpC;YACAiB,IAAI0V,SAAS,CAACvH,+BAAmB,EAAEpP;YACnCiB,IAAI0V,SAAS,CAAC,qBAAqB;YACnC1V,IAAI0V,SAAS,CACX,iBACA;YAEF1V,IAAIgN,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,OAAOlL,MAAMI,aAAa;QAE1B,uDAAuD;QACvD,iEAAiE;QACjE,IACE4W,SACA,IAAI,CAACvV,WAAW,IAChB5E,IAAIQ,OAAO,CAAC+O,+BAAmB,CAAC,IAChCvP,IAAIe,GAAG,CAAC6T,UAAU,CAAC,gBACnB;YACA5U,IAAIe,GAAG,GAAG,IAAI,CAACkP,iBAAiB,CAACjQ,IAAIe,GAAG;QAC1C;QAEA,IACE,CAAC,CAACf,IAAIQ,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACY,IAAIgM,UAAU,IAAIhM,IAAIgM,UAAU,KAAK,GAAE,GACzC;YACAhM,IAAI0V,SAAS,CACX,yBACA,GAAG3T,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAED,MAAMC,YAAY,EAAE,GAAG,KAAKjD,UAAU;QAEtE;QAEA,IAAI6a;QACJ,IAAI5B,WAAW4B,WAAW,EAAE;YAC1BA,cAAc5B,WAAW4B,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAACzU,eAAe,IACpB,OAAOwU,gBAAgB,eACvBE,IAAAA,4BAAoB,EAACF;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAMG,2BACJxZ,QAAQC,GAAG,CAACwZ,0CAA0C,KAAK,OAC3D,OAAOjY,MAAMkY,aAAa,KAAK,eAC/BJ;QAEF,sEAAsE;QACtE,6CAA6C;QAC7C,MAAMK,6BACJH,4BAA4BhY,MAAMkY,aAAa,KAAK;QAEtD,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAME,oBACJN,mBACC,CAAA,EACC5B,QAAAA,kBAAkBuB,MAAM,CAACza,SAAS,IAClCkZ,kBAAkBtB,aAAa,CAAC5X,SAAS,qBAF1C,AACCkZ,MAECmC,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BL,4BACE,CAAA,IAAI,CAAChU,UAAU,CAACxC,GAAG,KAAK,QACvB,IAAI,CAACI,qBAAqB,KAAK,IAAG,CAAE;QAE5C,MAAM0W,qBACJN,4BAA4BI;QAE9B,oEAAoE;QACpE,iEAAiE;QACjE,MAAMG,yBACJD,sBAAsB,IAAI,CAACtU,UAAU,CAACxC,GAAG,KAAK;QAEhD,MAAMgX,uBAAuBL,8BAA8BC;QAE3D,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMK,mBAAmBL,oBACrBzZ,IAAAA,2BAAc,EAAC9B,KAAK,eACpBH;QAEJ,0EAA0E;QAC1E,wEAAwE;QACxE,0DAA0D;QAC1D,MAAMgc,sBACJN,qBAAqBrO,gBAAgB,CAAC6N;QAExC,yEAAyE;QACzE,iEAAiE;QACjE,yEAAyE;QACzE,yEAAyE;QACzE,MAAMe,wBACJ9b,IAAIQ,OAAO,CAACyY,qDAAmC,CAACvY,WAAW,GAAG;QAEhE,gEAAgE;QAChE,IAAI+Y,aAAa,CAACqB,qBAAqB,CAAC5N,cAAc;YACpD9L,IAAIgM,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAI2O,8BAAmB,CAACpB,QAAQ,CAACxa,WAAW;YAC1CiB,IAAIgM,UAAU,GAAG4O,SAAS7b,SAAS8b,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,+CAA+C;QAC/C,CAACnC,kBACD,uCAAuC;QACvC,CAAC8B,oBACD,CAACnC,aACD,CAACC,aACDvZ,aAAa,aACbH,IAAI0L,MAAM,KAAK,UACf1L,IAAI0L,MAAM,KAAK,SACd,CAAA,OAAO0N,WAAWa,SAAS,KAAK,YAAYE,KAAI,GACjD;YACA/Y,IAAIgM,UAAU,GAAG;YACjBhM,IAAI0V,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtC,MAAM,IAAI,CAAC7D,WAAW,CAAC,MAAMjT,KAAKoB,KAAKjB;YACvC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAOiZ,WAAWa,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACLtD,MAAM;gBACN,0DAA0D;gBAC1DvI,MAAM8N,qBAAY,CAACC,UAAU,CAAC/C,WAAWa,SAAS;YACpD;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAAS9W,SAAS,CAACA,MAAMqE,GAAG,EAAE,OAAOrE,MAAMqE,GAAG;QAElD,IAAI+K,KAAKnL,uBAAuB,KAAK,MAAM;gBAGhCgS;YAFT,MAAM7C,eAAeC,IAAAA,YAAK,EAACxW,IAAIQ,OAAO,CAAC,aAAa,IAAI;YACxD,MAAM4b,sBACJ,SAAOhD,uBAAAA,WAAWiD,QAAQ,qBAAnBjD,qBAAqBc,eAAe,MAAK,cAChD,oFAAoF;YACpFoC,gCAAqB,IAAIlD,WAAWiD,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClD9J,KAAKnL,uBAAuB,GAC1B,CAAC+S,SAAS,CAAC5D,gBAAgB,CAACpT,MAAMqE,GAAG,IAAI4U;YAC3C7J,KAAKiE,KAAK,GAAGD;QACf;QAEA,2DAA2D;QAC3D,IAAI,CAACuE,qBAAqBhC,aAAavG,KAAK5N,GAAG,EAAE;YAC/C4N,KAAKnL,uBAAuB,GAAG;QACjC;QAEA,MAAMtE,gBAAgBqX,SAClB,wBAAA,IAAI,CAAC3a,UAAU,CAACmG,IAAI,qBAApB,sBAAsB7C,aAAa,GACnCK,MAAME,mBAAmB;QAE7B,MAAM8N,SAAShO,MAAMC,YAAY;QACjC,MAAMwC,WAAU,yBAAA,IAAI,CAACpG,UAAU,CAACmG,IAAI,qBAApB,uBAAsBC,OAAO;QAE7C,IAAI2W;QACJ,IAAIC,gBAAgB;QAEpB,IAAI7C,kBAAkBQ,SAASrB,WAAW;YACxC,8DAA8D;YAC9D,IAAInX,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,MAAM,EAAE4a,iBAAiB,EAAE,GACzBxX,QAAQ;gBACVsX,cAAcE,kBACZzc,KACAoB,KACA,IAAI,CAAC+F,UAAU,CAACM,YAAY,EAC5B,CAAC,CAAC,IAAI,CAACjI,UAAU,CAACC,YAAY,CAACid,kBAAkB;gBAEnDF,gBAAgBD,gBAAgB;YAClC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,gCAAgC;QAChC,IACEzD,aACA,CAACvG,KAAK5N,GAAG,IACT,CAAC6X,iBACDrC,SACAjN,gBACA,CAAC2O,uBACA,CAAA,CAACc,IAAAA,4BAAa,EAACpK,KAAKqK,OAAO,KAC1B,AAAC,IAAI,CAAC5X,aAAa,CAASmO,eAAe,AAAD,GAC5C;YACArS,IAAAA,sCAAkB,EAACd,IAAIQ,OAAO;QAChC;QAEA,IAAIqc,uBAAuB;QAC3B,IAAIC,0BAA0B;QAE9B,IAAI3C,OAAO;;YACP,CAAA,EAAE0C,oBAAoB,EAAEC,uBAAuB,EAAE,GACjDC,IAAAA,mCAAyB,EAAC/c,KAAK,IAAI,CAACmH,UAAU,CAACM,YAAY,CAAA;QAC/D;QAEA,IAAI0S,SAAS,IAAI,CAACvV,WAAW,IAAI5E,IAAIQ,OAAO,CAAC+O,+BAAmB,CAAC,EAAE;YACjE,uEAAuE;YACvE8K,sBAAsB3K;QACxB;QAEAA,cAAcgL,IAAAA,wCAAmB,EAAChL;QAClC2K,sBAAsBK,IAAAA,wCAAmB,EAACL;QAC1C,IAAI,IAAI,CAACvU,gBAAgB,EAAE;YACzBuU,sBAAsB,IAAI,CAACvU,gBAAgB,CAACvF,SAAS,CAAC8Z;QACxD;QAEA,MAAM2C,iBAAiB,CAACC;YACtB,MAAM9O,WAAW;gBACf+O,aAAaD,SAASE,SAAS,CAACC,YAAY;gBAC5ChQ,YAAY6P,SAASE,SAAS,CAACE,mBAAmB;gBAClDvV,UAAUmV,SAASE,SAAS,CAACG,sBAAsB;YACrD;YACA,MAAMlQ,aAAamQ,IAAAA,iCAAiB,EAACpP;YACrC,MAAM,EAAErG,QAAQ,EAAE,GAAG,IAAI,CAACtI,UAAU;YAEpC,IACEsI,YACAqG,SAASrG,QAAQ,KAAK,SACtBqG,SAAS+O,WAAW,CAACtI,UAAU,CAAC,MAChC;gBACAzG,SAAS+O,WAAW,GAAG,GAAGpV,WAAWqG,SAAS+O,WAAW,EAAE;YAC7D;YAEA,IAAI/O,SAAS+O,WAAW,CAACtI,UAAU,CAAC,MAAM;gBACxCzG,SAAS+O,WAAW,GAAGhP,IAAAA,+BAAwB,EAACC,SAAS+O,WAAW;YACtE;YAEA9b,IACG+M,QAAQ,CAACA,SAAS+O,WAAW,EAAE9P,YAC/BgB,IAAI,CAACD,SAAS+O,WAAW,EACzB7O,IAAI;QACT;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAIyM,mBAAmB;YACrBT,sBAAsB,IAAI,CAACpK,iBAAiB,CAACoK;YAC7C3K,cAAc,IAAI,CAACO,iBAAiB,CAACP;QACvC;QAEA,IAAI8N,cAA6B;QACjC,IACE,CAAChB,iBACDrC,SACA,CAAC5H,KAAKnL,uBAAuB,IAC7B,CAAC0S,kBACD,CAAC8B,oBACD,CAACC,qBACD;YACA2B,cAAc,GAAGrM,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACvC,AAAChR,CAAAA,aAAa,OAAOka,wBAAwB,GAAE,KAAMlJ,SACjD,KACAkJ,sBACHlX,MAAMqE,GAAG,GAAG,SAAS,IAAI;QAC9B;QAEA,IAAI,AAACiS,CAAAA,aAAaC,SAAQ,KAAMS,OAAO;YACrCqD,cAAc,GAAGrM,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKhR,WAC5CgD,MAAMqE,GAAG,GAAG,SAAS,IACrB;QACJ;QAEA,IAAIgW,aAAa;YACfA,cAAcC,IAAAA,kCAAgB,EAACD;YAE/B,+CAA+C;YAC/CA,cACEA,gBAAgB,YAAYrd,aAAa,MAAM,MAAMqd;QACzD;QACA,IAAIpK,WAA+B;QAEnC,IAAI;YACF,MAAMC,gBAAgB,IAAI5D,IACxB3N,IAAAA,2BAAc,EAAC9B,KAAK,cAAc,KAClC;YAEFoT,WAAWC,cAAcD,QAAQ;QACnC,EAAE,OAAM,CAAC;QAET,sDAAsD;QACtD,MAAME,mBACJ,AAAC3T,WAAmBuU,kBAAkB,IACrC,MAAM,IAAI,CAACX,mBAAmB,CAAC;YAC9BC,gBAAgBzK,OAAOsJ,MAAM,CAAC,CAAC,GAAGrS,IAAIQ,OAAO;YAC7CiT,iBAAiBL,SAAS7Q,SAAS,CAAC,GAAG6Q,SAASlR,MAAM,GAAG;QAG3D;QAEF,0EAA0E;QAC1EoR,iBAAiBW,iBAAiB;QAkBlC,MAAMyJ,WAAqB,OAAO,EAAE7N,SAAS,EAAE8N,mBAAmB,EAAE;YAClE,2DAA2D;YAC3D,IAAIvW,0BAGF,AAFA,uEAAuE;YACvE,6DAA6D;YAC5D,CAAC0T,qBAAqBvI,KAAK5N,GAAG,KAAK,QACpC,qEAAqE;YACrE,gBAAgB;YACf,CAACwV,SAAS,CAACN,qBACZ,mEAAmE;YACnE,QAAQ;YACR,OAAOhK,cAAc,YACrB,sEAAsE;YACtE,uBAAuB;YACvBgM;YAEF,MAAM+B,YAAY3c,IAAAA,UAAQ,EAACjB,IAAIe,GAAG,IAAI,IAAI,MAAMoC,KAAK;YAErD,mDAAmD;YACnD,kBAAkB;YAClB,IAAIoP,KAAKhR,MAAM,EAAE;gBACfwH,OAAOC,IAAI,CAACuJ,KAAKhR,MAAM,EAAEuU,OAAO,CAAC,CAACnE;oBAChC,OAAOiM,SAAS,CAACjM,IAAI;gBACvB;YACF;YACA,MAAMkM,mBACJnO,gBAAgB,OAAO,IAAI,CAAClQ,UAAU,CAAC8C,aAAa;YAEtD,MAAMwb,cAAc5c,IAAAA,WAAS,EAAC;gBAC5Bf,UAAU,GAAGka,sBAAsBwD,mBAAmB,MAAM,IAAI;gBAChE,uDAAuD;gBACvD1a,OAAOya;YACT;YACA,MAAMzW,aAA+B;gBACnC,GAAGiS,UAAU;gBACb,GAAG7G,IAAI;gBACP,GAAIuG,YACA;oBACExF;oBACA,gEAAgE;oBAChE,+DAA+D;oBAC/D,4DAA4D;oBAC5D,WAAW;oBACXyK,cAAc5D,SAAS,CAACtK,aAAa,CAACgM;oBACtCmC,eAAe,IAAI,CAACxe,UAAU,CAACC,YAAY,CAACue,aAAa;gBAC3D,IACA,CAAC,CAAC;gBACNlD;gBACAgD;gBACA3M;gBACAvL;gBACA9C;gBACA4Z,oBAAoB,IAAI,CAACld,UAAU,CAACC,YAAY,CAACid,kBAAkB;gBACnE,uFAAuF;gBACvF,8DAA8D;gBAC9D,SAAS;gBACTuB,gBACEtE,kBAAkBK,qBACd9Y,IAAAA,WAAS,EAAC;oBACR,iEAAiE;oBACjE,UAAU;oBACVf,UAAU,GAAGuP,cAAcmO,mBAAmB,MAAM,IAAI;oBACxD1a,OAAOya;gBACT,KACAE;gBACNre,cAAc;oBACZ,GAAG8S,KAAK9S,YAAY;oBACpB8b;gBACF;gBACAnU;gBACAyV;gBACAqB,aAAa1B;gBACb1C;gBACAjK;gBACA2H,WAAW,IAAI,CAACH,YAAY;gBAC5B8G,SAAS/c,IAAI+c,OAAO,CAAC1U,IAAI,CAACrI;gBAC1Bgd,kBAAkBve;gBAClB,wBAAwB;gBACxBwe,iBAAiB,AAAC,IAAI,CAASA,eAAe;YAChD;YAEA,IAAI5C,sBAAsBC,wBAAwB;gBAChDtU,0BAA0B;gBAC1BD,WAAWmX,UAAU,GAAG;gBACxBnX,WAAWC,uBAAuB,GAAG;gBACrCD,WAAWoX,kBAAkB,GAAG;gBAChCpX,WAAW4W,YAAY,GAAG;gBAC1B5W,WAAWsU,kBAAkB,GAAGA;gBAChCtU,WAAWuU,sBAAsB,GAAGA;YACtC;YAEA,qEAAqE;YACrE,wBAAwB;YACxB,IAAI7G;YAEJ,IAAImG,aAAa;gBACf,IAAIwD,IAAAA,6BAAqB,EAACxD,cAAc;wBAuBf;oBAtBvB,IACE,qEAAqE;oBACrE,6DAA6D;oBAC7DrZ,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC0M,IAAAA,0BAAiB,EAACvO,QACnB,CAAC6N,IAAAA,2BAAkB,EAACzM,MACpB;wBACA,MAAM,IAAIhC,MACR;oBAEJ;oBAEA,MAAMqf,UAAuC;wBAC3Cld,QAAQgR,KAAKhR,MAAM;wBACnB8X;wBACAlS,YAAY;4BACV1H,cAAc;gCACZ2J,WAAWjC,WAAW1H,YAAY,CAAC2J,SAAS;gCAC5CE,gBAAgBnC,WAAW1H,YAAY,CAAC6J,cAAc;4BACxD;4BACAlC;4BACAkM;4BACA9K,iBAAiB,GAAE,gCAAA,IAAI,CAAChJ,UAAU,CAACC,YAAY,qBAA5B,8BAA8BgJ,SAAS;4BAC1DsV,cAAc5D;4BACd3C,WAAW,IAAI,CAACH,YAAY;4BAC5B8G,SAAS/c,IAAI+c,OAAO,CAAC1U,IAAI,CAACrI;4BAC1Bgd,kBAAkBve;4BAClB0J,+BACE,IAAI,CAACpC,UAAU,CAACoC,6BAA6B;4BAC/C7H,SAAS,IAAI,CAACyF,UAAU,CAACzF,OAAO;wBAClC;oBACF;oBAEA,IAAI;wBACF,MAAMgd,UAAUC,+BAAkB,CAACC,mBAAmB,CACpD5e,KACA6e,IAAAA,mCAAsB,EAACzd,IAAI0M,gBAAgB;wBAG7C,MAAMgH,WAAW,MAAMkG,YAAY8D,MAAM,CAACJ,SAASD;wBAEjDze,IAAY+e,YAAY,GAAG,AAC3BN,QAAQtX,UAAU,CAClB4X,YAAY;wBAEd,MAAMC,YAAYP,QAAQtX,UAAU,CAAC8X,aAAa;wBAElD,mEAAmE;wBACnE,oBAAoB;wBACpB,IAAI9E,OAAO;4BACT,MAAM+E,OAAO,MAAMpK,SAASoK,IAAI;4BAEhC,sCAAsC;4BACtC,MAAM1e,UAAU2e,IAAAA,iCAAyB,EAACrK,SAAStU,OAAO;4BAE1D,IAAIwe,WAAW;gCACbxe,OAAO,CAAC4e,kCAAsB,CAAC,GAAGJ;4BACpC;4BAEA,IAAI,CAACxe,OAAO,CAAC,eAAe,IAAI0e,KAAKvI,IAAI,EAAE;gCACzCnW,OAAO,CAAC,eAAe,GAAG0e,KAAKvI,IAAI;4BACrC;4BAEA,MAAMC,aACJ,OAAO6H,QAAQtX,UAAU,CAACkY,mBAAmB,KAAK,eAClDZ,QAAQtX,UAAU,CAACkY,mBAAmB,IAAIC,0BAAc,GACpD,QACAb,QAAQtX,UAAU,CAACkY,mBAAmB;4BAE5C,2CAA2C;4BAC3C,MAAM/F,aAAiC;gCACrC1H,OAAO;oCACLjF,MAAM4S,8BAAe,CAACC,SAAS;oCAC/BC,QAAQ3K,SAAS2K,MAAM;oCACvBrR,MAAM0B,OAAO4P,IAAI,CAAC,MAAMR,KAAKS,WAAW;oCACxCnf;gCACF;gCACAoW;gCACAgJ,YAAY;4BACd;4BAEA,OAAOtG;wBACT;wBAEA,+DAA+D;wBAC/D,MAAMuG,IAAAA,0BAAY,EAChB7f,KACAoB,KACA0T,UACA2J,QAAQtX,UAAU,CAAC2Y,gBAAgB;wBAErC,OAAO;oBACT,EAAE,OAAOxU,KAAK;wBACZ,MAAM,IAAI,CAAC9B,6BAA6B,CAAC8B,KAAKtL,KAAK;4BACjD+f,YAAY;4BACZC,WAAW7f;4BACX8f,WAAW;4BACXC,kBAAkBC,IAAAA,2BAAmB,EAAChZ;wBACxC;wBAEA,8DAA8D;wBAC9D,IAAIgT,OAAO,MAAM7O;wBAEjBhH,KAAI0H,KAAK,CAACV;wBAEV,kCAAkC;wBAClC,MAAMuU,IAAAA,0BAAY,EAAC7f,KAAKoB,KAAK,IAAI2T,SAAS,MAAM;4BAAE0K,QAAQ;wBAAI;wBAE9D,OAAO;oBACT;gBACF,OAAO,IACLW,IAAAA,0BAAkB,EAACpF,gBACnBE,IAAAA,4BAAoB,EAACF,cACrB;oBACA,mDAAmD;oBACnD,IAAIhb,IAAI0L,MAAM,KAAK,aAAa,CAAC+N,WAAW;wBAC1C,MAAMoG,IAAAA,0BAAY,EAAC7f,KAAKoB,KAAK,IAAI2T,SAAS,MAAM;4BAAE0K,QAAQ;wBAAI;wBAC9D,OAAO;oBACT;oBAEA,IAAIW,IAAAA,0BAAkB,EAACpF,cAAc;wBACnC,wEAAwE;wBACxE,sEAAsE;wBACtE,iCAAiC;wBACjC,4HAA4H;wBAC5H7T,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBACnDI,WAAWkZ,uBAAuB,GAChCjH,WAAWiH,uBAAuB;wBAEpC,MAAM3B,UAAUnQ,IAAAA,0BAAiB,EAACvO,OAAOA,IAAI0N,eAAe,GAAG1N;wBAC/D,MAAM8U,WAAWjH,IAAAA,2BAAkB,EAACzM,OAChCA,IAAI0M,gBAAgB,GACpB1M;wBAEJ,iDAAiD;wBACjD,IAAI;4BACFyT,SAAS,MAAMmG,YAAY9D,MAAM,CAC/B,sBAAsB;4BACtB,+CAA+C;4BAC/CwH,SACA5J,UACA;gCACEnE,MAAMxQ;gCACNoB,QAAQgR,KAAKhR,MAAM;gCACnB4B;gCACAgE;4BACF;wBAEJ,EAAE,OAAOmE,KAAK;4BACZ,MAAM,IAAI,CAAC9B,6BAA6B,CAAC8B,KAAKtL,KAAK;gCACjD+f,YAAY;gCACZC,WAAW7f;gCACX8f,WAAW;gCACXC,kBAAkBC,IAAAA,2BAAmB,EAAC;oCACpCpC,cAAc5D;oCACd0C,sBAAsB1V,WAAW0V,oBAAoB;gCACvD;4BACF;4BACA,MAAMvR;wBACR;oBACF,OAAO;wBACL,MAAMgV,UAASlH,WAAW4B,WAAW;wBAErC,4EAA4E;wBAC5E,8DAA8D;wBAC9D,4HAA4H;wBAC5H7T,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBAEnD,MAAM0X,UAAsC;4BAC1C9N,MAAM8I,YAAY,SAAStZ;4BAC3BoB,QAAQgR,KAAKhR,MAAM;4BACnB4B;4BACAwa;4BACAxW;4BACAzH,0BAA0B,IAAI,CAACH,2BAA2B;wBAC5D;wBAEA,4DAA4D;wBAC5D,iEAAiE;wBACjE,wCAAwC;wBACxC,IACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAAC2J,SAAS,IACtC,IAAI,CAACjC,UAAU,CAACxC,GAAG,IACnB,CAACoW,wBACD,CAACjB,gBACD;4BACA,MAAMyG,SAAS,MAAMD,QAAOC,MAAM,CAACvgB,KAAKoB,KAAKqd;4BAE7C,6DAA6D;4BAC7D,yBAAyB;4BACzB,IAAI8B,OAAOC,QAAQ,CAACC,wBAAwB,EAAE;gCAC5CtZ,WAAWsZ,wBAAwB,GACjCF,OAAOC,QAAQ,CAACC,wBAAwB;4BAC5C;wBACF;wBAEA,iDAAiD;wBACjD5L,SAAS,MAAMyL,QAAOpJ,MAAM,CAAClX,KAAKoB,KAAKqd;oBACzC;gBACF,OAAO;oBACL,MAAM,IAAIrf,MAAM;gBAClB;YACF,OAAO;gBACL,oEAAoE;gBACpE,iBAAiB;gBACjByV,SAAS,MAAM,IAAI,CAAC6L,UAAU,CAAC1gB,KAAKoB,KAAKjB,UAAUgD,OAAOgE;YAC5D;YAEA,MAAM,EAAEqZ,QAAQ,EAAE,GAAG3L;YAErB,MAAM,EACJrU,UAAU,CAAC,CAAC,EACZ,oEAAoE;YACpEmgB,WAAW3B,SAAS,EACrB,GAAGwB;YAEJ,IAAIxB,WAAW;gBACbxe,OAAO,CAAC4e,kCAAsB,CAAC,GAAGJ;YACpC;YAEA,2DAA2D;;YACzDhf,IAAY+e,YAAY,GAAGyB,SAASzB,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACEjG,aACAqB,SACAqG,SAAS5J,UAAU,KAAK,KACxB,CAAC,IAAI,CAACzP,UAAU,CAACxC,GAAG,IACpB,CAAC4W,mBACD;gBACA,MAAMqF,oBAAoBJ,SAASI,iBAAiB;gBAEpD,MAAMtV,MAAM,IAAIlM,MACd,CAAC,+CAA+C,EAAEsQ,cAChDkR,CAAAA,qCAAAA,kBAAmBC,WAAW,IAC1B,CAAC,UAAU,EAAED,kBAAkBC,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC;gBAGlF,IAAID,qCAAAA,kBAAmBE,KAAK,EAAE;oBAC5B,MAAMA,QAAQF,kBAAkBE,KAAK;oBACrCxV,IAAIwV,KAAK,GAAGxV,IAAIyV,OAAO,GAAGD,MAAMve,SAAS,CAACue,MAAME,OAAO,CAAC;gBAC1D;gBAEA,MAAM1V;YACR;YAEA,uEAAuE;YACvE,iBAAiB;YAEjB,uBAAuB;YACvB,IAAI,gBAAgBkV,YAAYA,SAASS,UAAU,EAAE;gBACnD,OAAO;oBACLrP,OAAO;oBACPgF,YAAY4J,SAAS5J,UAAU;oBAC/BgJ,YAAY;gBACd;YACF;YAEA,uBAAuB;YACvB,IAAIY,SAASU,UAAU,EAAE;gBACvB,OAAO;oBACLtP,OAAO;wBACLjF,MAAM4S,8BAAe,CAAC4B,QAAQ;wBAC9BC,OAAOZ,SAASvD,QAAQ,IAAIuD,SAASa,UAAU;oBACjD;oBACAzK,YAAY4J,SAAS5J,UAAU;oBAC/BgJ,YAAY;gBACd;YACF;YAEA,mBAAmB;YACnB,IAAI/K,OAAOyM,MAAM,EAAE;gBACjB,OAAO;YACT;YAEA,kEAAkE;YAClE,IAAIxI,WAAW;gBACb,OAAO;oBACLlH,OAAO;wBACLjF,MAAM4S,8BAAe,CAACgC,QAAQ;wBAC9BC,MAAM3M;wBACNrU;wBACAihB,SAASjB,SAASa,UAAU;wBAC5BxR,WAAW2Q,SAAS3Q,SAAS;wBAC7B4P,QAAQre,IAAIgM,UAAU;wBACtBsU,aAAalB,SAASkB,WAAW;oBACnC;oBACA9K,YAAY4J,SAAS5J,UAAU;oBAC/BgJ,YAAY,CAAC,CAACjC;gBAChB;YACF;YAEA,OAAO;gBACL/L,OAAO;oBACLjF,MAAM4S,8BAAe,CAACoC,KAAK;oBAC3BH,MAAM3M;oBACNoI,UAAUuD,SAASvD,QAAQ,IAAIuD,SAASa,UAAU;oBAClD7gB;oBACAif,QAAQ3G,YAAY1X,IAAIgM,UAAU,GAAGvN;gBACvC;gBACA+W,YAAY4J,SAAS5J,UAAU;gBAC/BgJ,YAAYzc,MAAMye,cAAc,KAAK;YACvC;QACF;QAEA,IAAIC,oBAAuC,OAAO,EAChDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACf;YACC,MAAMC,eAAe,CAAC,IAAI,CAAC9a,UAAU,CAACxC,GAAG;YACzC,MAAMud,aAAaJ,eAAe1gB,IAAIyV,IAAI;YAE1C,sEAAsE;YACtE,IAAI,CAACmB,eAAeuC,WAAW;gBAC7B,IAAIV,mBAAmB;oBACrB,MAAMW,cAAc,MAAM,IAAI,CAAC3C,cAAc,CAAC;wBAC5C1X;wBACAqT,gBAAgBxT,IAAIQ,OAAO;wBAC3BsY;wBACAnI,MAAMyI,WAAWzI,IAAI;oBACvB;oBAEAqH,cAAcwC,YAAYxC,WAAW;oBACrCC,eAAeuC,YAAYvC,YAAY;gBACzC,OAAO;oBACLD,cAAcnY;oBACdoY,eAAekK,sBAAY,CAACC,SAAS;gBACvC;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,IACEnK,iBAAiBkK,sBAAY,CAACE,SAAS,IACvC7L,IAAAA,YAAK,EAACxW,IAAIQ,OAAO,CAAC,aAAa,IAAI,KACnC;gBACAyX,eAAekK,sBAAY,CAACG,sBAAsB;YACpD;YAEA,wDAAwD;YACxD,iCAAiC;YACjC,IACEzF,wBACAC,2BACA,CAACiF,sBACD,CAAC,IAAI,CAACnd,WAAW,EACjB;gBACA,MAAM,IAAI,CAAC7C,SAAS,CAAC/B,KAAKoB;gBAC1B,OAAO;YACT;YAEA,IAAI2gB,CAAAA,sCAAAA,mBAAoBQ,OAAO,MAAK,CAAC,GAAG;gBACtC1F,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACC5E,CAAAA,iBAAiBkK,sBAAY,CAACC,SAAS,IAAIL,kBAAiB,GAC7D;gBACA9J,eAAekK,sBAAY,CAACG,sBAAsB;YACpD;YAEA,sEAAsE;YACtE,uDAAuD;YACvD,EAAE;YACF,sEAAsE;YACtE,8DAA8D;YAC9D,EAAE;YACF,sEAAsE;YACtE,0BAA0B;YAC1B,IAAIE,gBAAgBhF;YACpB,IAAI,CAACgF,iBAAiBjQ,KAAK5N,GAAG,IAAImU,WAAW;gBAC3C0J,gBAAgB/E,IAAAA,kCAAgB,EAACpD;YACnC;YACA,IAAImI,iBAAiBrf,MAAMqE,GAAG,EAAE;gBAC9Bgb,gBAAgBA,cAActT,OAAO,CAAC,UAAU;YAClD;YAEA,MAAMuT,8BACJD,kBAAiBxK,+BAAAA,YAAa2C,QAAQ,CAAC6H;YAEzC,qEAAqE;YACrE,kCAAkC;YAElC,kCAAkC;YAClC,IAAI,IAAI,CAAChjB,UAAU,CAACC,YAAY,CAACwJ,qBAAqB,EAAE;gBACtDgP,eAAekK,sBAAY,CAACG,sBAAsB;YACpD;YAEA,oEAAoE;YACpE,kCAAkC;YAClC,EAAE;YACF,gCAAgC;YAChC,0CAA0C;YAC1C,wEAAwE;YACxE,iEAAiE;YACjE,yBAAyB;YACzB,iEAAiE;YACjE,qEAAqE;YACrE,EAAE;YACF,IACE3gB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC,IAAI,CAAC+C,WAAW,IACjBqT,iBAAiBkK,sBAAY,CAACG,sBAAsB,IACpDE,iBACA,CAACN,cACD,CAAC1F,iBACDjC,aACC0H,CAAAA,gBAAgB,CAACjK,eAAe,CAACyK,2BAA0B,GAC5D;gBACA,IAGE,AAFA,2DAA2D;gBAC3D,kBAAkB;gBACjBR,CAAAA,gBAAiBjK,eAAeA,CAAAA,+BAAAA,YAAa9V,MAAM,IAAG,CAAC,KACxD,2DAA2D;gBAC3D+V,iBAAiBkK,sBAAY,CAACC,SAAS,EACvC;oBACA,MAAM,IAAInjB;gBACZ;gBAEA,IAAIyjB;gBAEJ,kCAAkC;gBAClC,IAAItC,IAAAA,0BAAkB,EAAChH,WAAW4B,WAAW,KAAK,CAACF,mBAAmB;oBACpE,gEAAgE;oBAChE,oCAAoC;oBACpC4H,mBAAmB,MAAM,IAAI,CAAClY,aAAa,CAAC+C,GAAG,CAC7C0U,eAAgB9Q,SAAS,CAAC,CAAC,EAAEA,SAAShR,UAAU,GAAGA,WAAY,MAC/D,yDAAyD;oBACzD,OAAO,EACL4hB,oBAAoBY,6BAA6B,IAAI,EACtD;wBACC,2DAA2D;wBAC3D,8DAA8D;wBAC9D,gEAAgE;wBAChE,iEAAiE;wBACjE,YAAY;wBACZ,IAAIV,cAAc;4BAChB,OAAOW,IAAAA,4BAAoB,EAACD;wBAC9B;wBAEA,2DAA2D;wBAC3D,+DAA+D;wBAC/D,4DAA4D;wBAC5D,cAAc;wBACdxf,MAAMye,cAAc,GAAG;wBAEvB,kEAAkE;wBAClE,UAAU;wBACV,OAAOlE,SAAS;4BACd7N,WAAWhQ;4BACX8d,qBAAqB;wBACvB;oBACF,GACA;wBACEkF,WAAWC,oBAAS,CAACnB,KAAK;wBAC1BrO;wBACAiI;wBACAqE,YAAY;oBACd;gBAEJ,OAGK,IACHrE,qBACAL,IAAAA,4BAAoB,EAAC9B,WAAW4B,WAAW,KAC3C,CAAC9N,cACD;oBACA,gEAAgE;oBAChE,oCAAoC;oBACpCwV,mBAAmB,MAAM,IAAI,CAAClY,aAAa,CAAC+C,GAAG,CAC7C0U,eAAe9hB,WAAW,MAC1B,yDAAyD;oBACzD,UACEud,SAAS;4BACP,4DAA4D;4BAC5D,QAAQ;4BACR7N,WAAWhQ;4BACX8d,qBACE,yDAAyD;4BACzD,wDAAwD;4BACxD,YAAY;4BACZsE,gBAAgBtG,uBACZoH,IAAAA,sCAAsB,EAAC5iB,YACvB;wBACR,IACF;wBACE0iB,WAAWC,oBAAS,CAACvB,QAAQ;wBAC7BjO;wBACAiI;wBACAqE,YAAY;oBACd;gBAEJ;gBAEA,wEAAwE;gBACxE,IAAI8C,qBAAqB,MAAM,OAAO;gBAEtC,qEAAqE;gBACrE,IAAIA,kBAAkB;oBACpB,mEAAmE;oBACnE,iCAAiC;oBACjC,OAAOA,iBAAiB9L,UAAU;oBAElC,OAAO8L;gBACT;YACF;YAEA,wEAAwE;YACxE,oEAAoE;YACpE,MAAM7S,YACJ,CAACgN,wBAAwB,CAACmF,kBAAkBpG,mBACxCA,mBACA/b;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACE,AAAC4b,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAO7L,cAAc,aACrB;gBACA,OAAO;oBACL+G,YAAY;oBACZgJ,YAAY;oBACZhO,OAAO;wBACLjF,MAAM4S,8BAAe,CAACoC,KAAK;wBAC3BH,MAAMtF,qBAAY,CAACC,UAAU,CAAC;wBAC9Bc,UAAU,CAAC;wBACXzc,SAASX;wBACT4f,QAAQ5f;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAM8d,sBACJpD,aACAgB,qBACCzZ,CAAAA,IAAAA,2BAAc,EAAC9B,KAAK,gCACnB2b,oBAAmB,IACjBoH,IAAAA,sCAAsB,EAAC5iB,YACvB;YAEN,sBAAsB;YACtB,MAAM0U,SAAS,MAAM6I,SAAS;gBAC5B7N;gBACA8N;YACF;YACA,IAAI,CAAC9I,QAAQ,OAAO;YAEpB,OAAO;gBACL,GAAGA,MAAM;gBACT+B,YAAY/B,OAAO+B,UAAU;YAC/B;QACF;QAEA,MAAM0C,aAAa,MAAM,IAAI,CAAC9O,aAAa,CAAC+C,GAAG,CAC7CiQ,aACAqE,mBACA;YACEgB,WACE,sEAAsE;YACtE,qCAAqC;YACrC7H,CAAAA,+BAAAA,YAAaxK,UAAU,CAAC7D,IAAI,KAC3BmM,CAAAA,YAAYgK,oBAAS,CAACvB,QAAQ,GAAGuB,oBAAS,CAACnB,KAAK,AAAD;YAClDrO;YACAuJ;YACAmG,YAAYhjB,IAAIQ,OAAO,CAACyiB,OAAO,KAAK;YACpC1H;QACF;QAGF,IAAIR,wBAAwB,OAAOe,0BAA0B,UAAU;gBAMnE,kEAAkE;YAClE,+DAA+D;YAC/D,mDAAmD;YACnDxC;YARF,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YACtE,IACEA,eAAe,QAIfA,EAAAA,qBAAAA,WAAW1H,KAAK,qBAAhB0H,mBAAkB3M,IAAI,MAAK4S,8BAAe,CAACgC,QAAQ,IACnDjI,WAAW1H,KAAK,CAAC8P,WAAW,EAC5B;gBACA,MAAMwB,iBAAiB5J,WAAW1H,KAAK,CAAC8P,WAAW,CAACnU,GAAG,CACrDuO;gBAEF,IAAIoH,mBAAmBrjB,WAAW;oBAChC,YAAY;oBACZ,OAAO;wBACL8W,MAAM;wBACNvI,MAAM8N,qBAAY,CAACC,UAAU,CAAC+G;wBAC9B,0DAA0D;wBAC1D,0CAA0C;wBAC1CtM,YAAY0C,WAAW1C,UAAU;oBACnC;gBACF;YACF;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,uEAAuE;YACvE,wEAAwE;YACxE,oBAAoB;YACpB,oEAAoE;YACpE,gEAAgE;YAChE,eAAe;YACfxV,IAAIgM,UAAU,GAAG;YACjB,IAAImO,mBAAmB;gBACrB,oEAAoE;gBACpE,uEAAuE;gBACvE,kCAAkC;gBAClC,oEAAoE;gBACpE,oEAAoE;gBACpE,uBAAuB;gBACvBna,IAAI0V,SAAS,CAACqM,0CAAwB,EAAE;YAC1C;YACA,OAAO;gBACLxM,MAAM;gBACNvI,MAAM8N,qBAAY,CAACC,UAAU,CAAC;gBAC9BvF,UAAU,EAAE0C,8BAAAA,WAAY1C,UAAU;YACpC;QACF;QAEA,IAAI4F,eAAe;YACjBpb,IAAI0V,SAAS,CACX,iBACA;QAEJ;QAEA,IAAI,CAACwC,YAAY;YACf,IAAIkE,eAAe,CAAEX,CAAAA,wBAAwBC,uBAAsB,GAAI;gBACrE,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,IAAI1d,MAAM;YAClB;YACA,OAAO;QACT;QAEA,2EAA2E;QAC3E,4EAA4E;QAC5E,IACEoe,eACA,CAAC,IAAI,CAAC5Y,WAAW,IACjB2W,qBACAjC,EAAAA,oBAAAA,WAAW1H,KAAK,qBAAhB0H,kBAAkB3M,IAAI,MAAK4S,8BAAe,CAACgC,QAAQ,IACnDjI,WAAWsG,UAAU,IACrB,CAAC/C,wBACD,uEAAuE;QACvE,mBAAmB;QACnB,CAAClB,wBACDha,QAAQC,GAAG,CAACwhB,8BAA8B,KAAK,QAC/C;YACAC,IAAAA,6BAAkB,EAAC;gBACjB,IAAI;oBACF,MAAM,IAAI,CAAC7Y,aAAa,CAAC+C,GAAG,CAC1BiQ,aACA,IACEE,SAAS;4BACP,8DAA8D;4BAC9D,uBAAuB;4BACvBC,qBAAqB;4BACrB9N,WAAWhQ;wBACb,IACF;wBACEgjB,WAAWC,oBAAS,CAACvB,QAAQ;wBAC7BjO;wBACAuJ,sBAAsB;wBACtBmG,YAAY;wBACZzH,mBAAmB;oBACrB;gBAEJ,EAAE,OAAOjQ,KAAK;oBACZS,QAAQC,KAAK,CAAC,gDAAgDV;gBAChE;YACF;QACF;QAEA,MAAMgY,cACJhK,EAAAA,qBAAAA,WAAW1H,KAAK,qBAAhB0H,mBAAkB3M,IAAI,MAAK4S,8BAAe,CAACgC,QAAQ,IACnD,OAAOjI,WAAW1H,KAAK,CAAC/B,SAAS,KAAK;QAExC,IACEsK,SACA,yEAAyE;QACzE,kEAAkE;QAClE,gDAAgD;QAChD,CAAC0B,uBACA,CAAA,CAACyH,eAAevI,oBAAmB,GACpC;YACA,IAAI,CAAC,IAAI,CAACnW,WAAW,EAAE;gBACrB,gDAAgD;gBAChD,iCAAiC;gBACjCxD,IAAI0V,SAAS,CACX,kBACA+F,uBACI,gBACAvD,WAAWiK,MAAM,GACf,SACAjK,WAAWiJ,OAAO,GAChB,UACA;YAEZ;YACA,0EAA0E;YAC1E,yDAAyD;YACzDnhB,IAAI0V,SAAS,CAAC0M,0CAAwB,EAAE;QAC1C;QAEA,MAAM,EAAE5R,OAAO6R,UAAU,EAAE,GAAGnK;QAE9B,yDAAyD;QACzD,IAAImK,CAAAA,8BAAAA,WAAY9W,IAAI,MAAK4S,8BAAe,CAACmE,KAAK,EAAE;YAC9C,MAAM,IAAItkB,MAAM;QAClB;QAEA,mDAAmD;QACnD,IAAIwX;QAEJ,0EAA0E;QAC1E,oCAAoC;QACpC,IAAIgF,kBAAkB;YACpBhF,aAAa;QACf,OAKK,IACH,IAAI,CAAChS,WAAW,IAChBsI,gBACA,CAAC6N,wBACDQ,mBACA;YACA3E,aAAa;QACf,OAAO,IAAI,CAAC,IAAI,CAACzP,UAAU,CAACxC,GAAG,IAAKgV,kBAAkB,CAACmB,mBAAoB;YACzE,2DAA2D;YAC3D,IAAI0B,eAAe;gBACjB5F,aAAa;YACf,OAIK,IAAI,CAACuD,OAAO;gBACf,IAAI,CAAC/Y,IAAIuiB,SAAS,CAAC,kBAAkB;oBACnC/M,aAAa;gBACf;YACF,OAQK,IAAI6C,WAAW;gBAClB,MAAMmK,qBAAqB9hB,IAAAA,2BAAc,EAAC9B,KAAK;gBAC/C4W,aACE,OAAOgN,uBAAuB,cAAc,IAAIA;YACpD,OAAO,IAAIlK,WAAW;gBACpB9C,aAAa;YACf,OAGK,IAAI,OAAO0C,WAAW1C,UAAU,KAAK,UAAU;gBAClD,IAAI0C,WAAW1C,UAAU,GAAG,GAAG;oBAC7B,MAAM,IAAIxX,MACR,CAAC,2CAA2C,EAAEka,WAAW1C,UAAU,CAAC,IAAI,CAAC;gBAE7E;gBAEAA,aAAa0C,WAAW1C,UAAU;YACpC,OAGK,IAAI0C,WAAW1C,UAAU,KAAK,OAAO;gBACxCA,aAAaiN,0BAAc;YAC7B;QACF;QAEAvK,WAAW1C,UAAU,GAAGA;QAExB,yEAAyE;QACzE,8BAA8B;QAC9B,MAAMkN,eAAehiB,IAAAA,2BAAc,EAAC9B,KAAK;QACzC,IAAI8jB,cAAc;gBASRxK,oBAEIA;YAVZ,MAAMvV,WAAW,MAAM+f,aACrB;gBACE,GAAGxK,UAAU;gBACb,0CAA0C;gBAC1C,wCAAwC;gBACxC1H,OAAO;oBACL,GAAG0H,WAAW1H,KAAK;oBACnBjF,MACE2M,EAAAA,qBAAAA,WAAW1H,KAAK,qBAAhB0H,mBAAkB3M,IAAI,MAAK4S,8BAAe,CAACgC,QAAQ,GAC/C,UACAjI,qBAAAA,WAAW1H,KAAK,qBAAhB0H,mBAAkB3M,IAAI;gBAC9B;YACF,GACA;gBACE5L,KAAKe,IAAAA,2BAAc,EAAC9B,KAAK;YAC3B;YAEF,IAAI+D,UAAU;gBACZ,0CAA0C;gBAC1C,OAAO;YACT;QACF;QAEA,IAAI,CAAC0f,YAAY;YACf,oDAAoD;YACpD,qDAAqD;YACrD,4DAA4D;YAC5D,2BAA2B;YAC3B7iB,IAAAA,2BAAc,EAACZ,KAAK,sBAAsBsZ,WAAW1C,UAAU;YAE/D,2DAA2D;YAC3D,6DAA6D;YAC7D,IACE,OAAO0C,WAAW1C,UAAU,KAAK,eACjC,CAACxV,IAAIuiB,SAAS,CAAC,kBACf;gBACAviB,IAAI0V,SAAS,CACX,iBACAiN,IAAAA,4BAAgB,EAAC;oBACfnN,YAAY0C,WAAW1C,UAAU;oBACjC1N,YAAY,IAAI,CAAC1J,UAAU,CAAC0J,UAAU;gBACxC;YAEJ;YACA,IAAI4R,mBAAmB;gBACrB1Z,IAAIgM,UAAU,GAAG;gBACjBhM,IAAIgN,IAAI,CAAC,qBAAqBC,IAAI;gBAClC,OAAO;YACT;YAEA,IAAI,IAAI,CAAClH,UAAU,CAACxC,GAAG,EAAE;gBACvBxB,MAAM6gB,qBAAqB,GAAG7jB;YAChC;YACA,MAAM,IAAI,CAAC4B,SAAS,CAAC/B,KAAKoB,KAAK;gBAAEjB;gBAAUgD;YAAM,GAAG;YACpD,OAAO;QACT,OAAO,IAAIsgB,WAAW9W,IAAI,KAAK4S,8BAAe,CAAC4B,QAAQ,EAAE;YACvD,2DAA2D;YAC3D,6DAA6D;YAC7D,IACE,OAAO7H,WAAW1C,UAAU,KAAK,eACjC,CAACxV,IAAIuiB,SAAS,CAAC,kBACf;gBACAviB,IAAI0V,SAAS,CACX,iBACAiN,IAAAA,4BAAgB,EAAC;oBACfnN,YAAY0C,WAAW1C,UAAU;oBACjC1N,YAAY,IAAI,CAAC1J,UAAU,CAAC0J,UAAU;gBACxC;YAEJ;YAEA,IAAI4R,mBAAmB;gBACrB,OAAO;oBACLnE,MAAM;oBACNvI,MAAM8N,qBAAY,CAACC,UAAU,CAC3B,6BAA6B;oBAC7B8H,KAAKC,SAAS,CAACT,WAAWrC,KAAK;oBAEjCxK,YAAY0C,WAAW1C,UAAU;gBACnC;YACF,OAAO;gBACL,MAAMoG,eAAeyG,WAAWrC,KAAK;gBACrC,OAAO;YACT;QACF,OAAO,IAAIqC,WAAW9W,IAAI,KAAK4S,8BAAe,CAACC,SAAS,EAAE;YACxD,MAAMhf,UAAU2jB,IAAAA,mCAA2B,EAACV,WAAWjjB,OAAO;YAE9D,IAAI,CAAE,CAAA,IAAI,CAACoE,WAAW,IAAIuV,KAAI,GAAI;gBAChC3Z,QAAQ4jB,MAAM,CAAChF,kCAAsB;YACvC;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IACE,OAAO9F,WAAW1C,UAAU,KAAK,eACjC,CAACxV,IAAIuiB,SAAS,CAAC,oBACf,CAACnjB,QAAQ+M,GAAG,CAAC,kBACb;gBACA/M,QAAQ6jB,GAAG,CACT,iBACAN,IAAAA,4BAAgB,EAAC;oBACfnN,YAAY0C,WAAW1C,UAAU;oBACjC1N,YAAY,IAAI,CAAC1J,UAAU,CAAC0J,UAAU;gBACxC;YAEJ;YAEA,MAAM2W,IAAAA,0BAAY,EAChB7f,KACAoB,KACA,IAAI2T,SAAS0O,WAAWrV,IAAI,EAAE;gBAC5B5N;gBACAif,QAAQgE,WAAWhE,MAAM,IAAI;YAC/B;YAEF,OAAO;QACT,OAAO,IAAIgE,WAAW9W,IAAI,KAAK4S,8BAAe,CAACgC,QAAQ,EAAE;gBAmCrDkC;YAlCF,oEAAoE;YACpE,gBAAgB;YAChB,IAAIH,eAAe1H,kBAAkB;gBACnC,MAAM,IAAIxc,MACR;YAEJ;YAEA,IAAIqkB,WAAWjjB,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAGijB,WAAWjjB,OAAO;gBAAC;gBAExC,IAAI,CAAC,IAAI,CAACoE,WAAW,IAAI,CAACuV,OAAO;oBAC/B,OAAO3Z,OAAO,CAAC4e,kCAAsB,CAAC;gBACxC;gBAEA,KAAK,IAAI,CAACzN,KAAKC,MAAM,IAAI7I,OAAO8C,OAAO,CAACrL,SAAU;oBAChD,IAAI,OAAOoR,UAAU,aAAa;oBAElC,IAAI0S,MAAMC,OAAO,CAAC3S,QAAQ;wBACxB,KAAK,MAAM4S,KAAK5S,MAAO;4BACrBxQ,IAAIqjB,YAAY,CAAC9S,KAAK6S;wBACxB;oBACF,OAAO,IAAI,OAAO5S,UAAU,UAAU;wBACpCA,QAAQA,MAAMhD,QAAQ;wBACtBxN,IAAIqjB,YAAY,CAAC9S,KAAKC;oBACxB,OAAO;wBACLxQ,IAAIqjB,YAAY,CAAC9S,KAAKC;oBACxB;gBACF;YACF;YAEA,IACE,IAAI,CAAChN,WAAW,IAChBuV,WACAsJ,sBAAAA,WAAWjjB,OAAO,qBAAlBijB,mBAAoB,CAACrE,kCAAsB,CAAC,GAC5C;gBACAhe,IAAI0V,SAAS,CACXsI,kCAAsB,EACtBqE,WAAWjjB,OAAO,CAAC4e,kCAAsB,CAAC;YAE9C;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAIqE,WAAWhE,MAAM,IAAK,CAAA,CAACvS,gBAAgB,CAACqO,iBAAgB,GAAI;gBAC9Dna,IAAIgM,UAAU,GAAGqW,WAAWhE,MAAM;YACpC;YAEA,sCAAsC;YACtC,IAAI6D,aAAa;gBACfliB,IAAI0V,SAAS,CAACqM,0CAAwB,EAAE;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAIjW,gBAAgB,CAACsP,eAAe;gBAClC,8DAA8D;gBAC9D,IAAI,OAAOiH,WAAWhC,OAAO,KAAK,aAAa;oBAC7C,IAAIgC,WAAW5T,SAAS,EAAE;wBACxB,MAAM,IAAIzQ,MAAM;oBAClB;oBAEA,OAAO;wBACLuX,MAAM;wBACNvI,MAAMqV,WAAWjC,IAAI;wBACrB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/E5K,YAAYiF,sBAAsB,IAAIvC,WAAW1C,UAAU;oBAC7D;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO;oBACLD,MAAM;oBACNvI,MAAM8N,qBAAY,CAACC,UAAU,CAACsH,WAAWhC,OAAO;oBAChD7K,YAAY0C,WAAW1C,UAAU;gBACnC;YACF;YAEA,mCAAmC;YACnC,IAAIxI,OAAOqV,WAAWjC,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAAC8B,eAAe,IAAI,CAAC1e,WAAW,EAAE;gBACpC,OAAO;oBACL+R,MAAM;oBACNvI;oBACAwI,YAAY0C,WAAW1C,UAAU;gBACnC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAI6E,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnDtN,KAAKsW,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,CAACC,yBAAY,CAACC,MAAM,CAACC,aAAa;wBACpDJ,WAAWhP,KAAK;oBAClB;gBACF;gBAGF,OAAO;oBAAEc,MAAM;oBAAQvI;oBAAMwI,YAAY;gBAAE;YAC7C;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAMsO,cAAc,IAAIC;YACxB/W,KAAKsW,KAAK,CAACQ,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzE1H,SAAS;gBACP7N,WAAW4T,WAAW5T,SAAS;gBAC/B,sEAAsE;gBACtE,YAAY;gBACZ8N,qBAAqB;YACvB,GACG/H,IAAI,CAAC,OAAOf;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,IAAIzV,MAAM;gBAClB;gBAEA,IAAIyV,EAAAA,gBAAAA,OAAOjD,KAAK,qBAAZiD,cAAclI,IAAI,MAAK4S,8BAAe,CAACgC,QAAQ,EAAE;wBAEL1M;oBAD9C,MAAM,IAAIzV,MACR,CAAC,yCAAyC,GAAEyV,iBAAAA,OAAOjD,KAAK,qBAAZiD,eAAclI,IAAI,EAAE;gBAEpE;gBAEA,6CAA6C;gBAC7C,MAAMkI,OAAOjD,KAAK,CAAC4P,IAAI,CAAC6D,MAAM,CAACH,YAAYI,QAAQ;YACrD,GACCC,KAAK,CAAC,CAACja;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1D4Z,YAAYI,QAAQ,CAACE,KAAK,CAACla,KAAKia,KAAK,CAAC,CAACE;oBACrC1Z,QAAQC,KAAK,CAAC,8BAA8ByZ;gBAC9C;YACF;YAEF,OAAO;gBACL9O,MAAM;gBACNvI;gBACA,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrCwI,YAAY;YACd;QACF,OAAO,IAAIkE,mBAAmB;YAC5B,OAAO;gBACLnE,MAAM;gBACNvI,MAAM8N,qBAAY,CAACC,UAAU,CAAC8H,KAAKC,SAAS,CAACT,WAAWxG,QAAQ;gBAChErG,YAAY0C,WAAW1C,UAAU;YACnC;QACF,OAAO;YACL,OAAO;gBACLD,MAAM;gBACNvI,MAAMqV,WAAWjC,IAAI;gBACrB5K,YAAY0C,WAAW1C,UAAU;YACnC;QACF;IACF;IAEQ3G,kBAAkBxO,IAAY,EAAEikB,cAAc,IAAI,EAAE;QAC1D,IAAIjkB,KAAKkZ,QAAQ,CAAC,IAAI,CAACjZ,OAAO,GAAG;YAC/B,MAAMikB,YAAYlkB,KAAKc,SAAS,CAC9Bd,KAAKuf,OAAO,CAAC,IAAI,CAACtf,OAAO,IAAI,IAAI,CAACA,OAAO,CAACQ,MAAM;YAGlDT,OAAO2O,IAAAA,wCAAmB,EAACuV,UAAUzW,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAACpJ,gBAAgB,IAAI4f,aAAa;YACxC,OAAO,IAAI,CAAC5f,gBAAgB,CAACvF,SAAS,CAACkB;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChCmkB,oBAAoBpY,KAAa,EAAE;QAC3C,IAAI,IAAI,CAACxJ,kBAAkB,CAACyC,GAAG,EAAE;gBACP;YAAxB,MAAMof,mBAAkB,sBAAA,IAAI,CAAC7b,aAAa,qBAAlB,mBAAoB,CAACwD,MAAM;YAEnD,IAAI,CAACqY,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACdva,GAAkD,EAClDwa,gBAAyB,EACzB;YAiBgB;QAhBhB,MAAM,EAAE5iB,KAAK,EAAEhD,QAAQ,EAAE,GAAGoL;QAE5B,MAAMya,WAAW,IAAI,CAACJ,mBAAmB,CAACzlB;QAC1C,MAAM2Y,YAAYwL,MAAMC,OAAO,CAACyB;QAEhC,IAAIrV,OAAOxQ;QACX,IAAI2Y,WAAW;YACb,4EAA4E;YAC5EnI,OAAOqV,QAAQ,CAACA,SAAS9jB,MAAM,GAAG,EAAE;QACtC;QAEA,MAAM2S,SAAS,MAAM,IAAI,CAACoR,kBAAkB,CAAC;YAC3CtV;YACAxN;YACA5B,QAAQgK,IAAIpE,UAAU,CAAC5F,MAAM,IAAI,CAAC;YAClCuX;YACAoN,YAAY,CAAC,GAAC,oCAAA,IAAI,CAAC1mB,UAAU,CAACC,YAAY,CAAC0mB,GAAG,qBAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAIxR,QAAQ;YACVvI,IAAAA,iBAAS,IAAGga,oBAAoB,CAAC,cAAcnmB;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAACgY,8BAA8B,CAAC5M,KAAKsJ;YACxD,EAAE,OAAOvJ,KAAK;gBACZ,MAAMib,oBAAoBjb,eAAerM;gBAEzC,IAAI,CAACsnB,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAMza;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAcsM,iBACZrM,GAAkD,EACjB;QACjC,OAAOe,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAACmL,gBAAgB,EAC/B;YACElL,UAAU,CAAC,cAAc,CAAC;YAC1BI,YAAY;gBACV,cAAcvB,IAAIpL,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAACqmB,oBAAoB,CAACjb;QACnC;IAEJ;IAQA,MAAcib,qBACZjb,GAAkD,EACjB;YAQzB;QAPR,MAAM,EAAEnK,GAAG,EAAE+B,KAAK,EAAEhD,QAAQ,EAAE,GAAGoL;QACjC,IAAIoF,OAAOxQ;QACX,MAAM4lB,mBAAmB,CAAC,CAAC5iB,MAAMsjB,qBAAqB;QACtD,OAAOtjB,KAAK,CAACujB,sCAAoB,CAAC;QAClC,OAAOvjB,MAAMsjB,qBAAqB;QAElC,MAAM3mB,UAAwB;YAC5B6F,IAAI,GAAE,qBAAA,IAAI,CAACnD,YAAY,qBAAjB,mBAAmBmkB,SAAS,CAACxmB,UAAUgD;QAC/C;QAEA,IAAI;YACF,WAAW,MAAM7C,SAAS,IAAI,CAAC8J,QAAQ,CAACwc,QAAQ,CAACzmB,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAM+mB,eAAe/kB,IAAAA,2BAAc,EAACyJ,IAAIvL,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAAC4E,WAAW,IACjB,OAAOiiB,iBAAiB,YACxBtW,IAAAA,sBAAc,EAACsW,gBAAgB,OAC/BA,iBAAiBvmB,MAAMkQ,UAAU,CAACrQ,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAM0U,SAAS,MAAM,IAAI,CAACiR,mBAAmB,CAC3C;oBACE,GAAGva,GAAG;oBACNpL,UAAUG,MAAMkQ,UAAU,CAACrQ,QAAQ;oBACnCgH,YAAY;wBACV,GAAGoE,IAAIpE,UAAU;wBACjB5F,QAAQjB,MAAMiB,MAAM;oBACtB;gBACF,GACAwkB;gBAEF,IAAIlR,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAAC7P,aAAa,CAACmO,eAAe,EAAE;gBACtC,sDAAsD;gBACtD5H,IAAIpL,QAAQ,GAAG,IAAI,CAAC6E,aAAa,CAACmO,eAAe,CAACxC,IAAI;gBACtD,MAAMkE,SAAS,MAAM,IAAI,CAACiR,mBAAmB,CAACva,KAAKwa;gBACnD,IAAIlR,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAO7I,OAAO;YACd,MAAMV,MAAM8J,IAAAA,uBAAc,EAACpJ;YAE3B,IAAIA,iBAAiB8a,wBAAiB,EAAE;gBACtC/a,QAAQC,KAAK,CACX,yCACAiY,KAAKC,SAAS,CACZ;oBACEvT;oBACA5P,KAAKwK,IAAIvL,GAAG,CAACe,GAAG;oBAChByO,aAAajE,IAAIvL,GAAG,CAACQ,OAAO,CAAC+O,+BAAmB,CAAC;oBACjDwX,SAASjlB,IAAAA,2BAAc,EAACyJ,IAAIvL,GAAG,EAAE;oBACjCwR,YAAY,CAAC,CAAC1P,IAAAA,2BAAc,EAACyJ,IAAIvL,GAAG,EAAE;oBACtCgnB,YAAYllB,IAAAA,2BAAc,EAACyJ,IAAIvL,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAMsL;YACR;YAEA,IAAIA,eAAerM,mBAAmB8mB,kBAAkB;gBACtD,MAAMza;YACR;YACA,IAAIA,eAAeyH,kBAAW,IAAIzH,eAAe0H,qBAAc,EAAE;gBAC/D5R,IAAIgM,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAAC6Z,qBAAqB,CAAC1b,KAAKD;YAC/C;YAEAlK,IAAIgM,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAACsK,OAAO,CAAC,SAAS;gBAC9BnM,IAAIpI,KAAK,CAAC+jB,uBAAuB,GAAG;gBACpC,MAAM,IAAI,CAACD,qBAAqB,CAAC1b,KAAKD;gBACtC,OAAOC,IAAIpI,KAAK,CAAC+jB,uBAAuB;YAC1C;YAEA,MAAMC,iBAAiB7b,eAAepM;YAEtC,IAAI,CAACioB,gBAAgB;gBACnB,IACE,AAAC,IAAI,CAACviB,WAAW,IAAIjD,QAAQC,GAAG,CAACC,YAAY,KAAK,UAClD,IAAI,CAACsF,UAAU,CAACxC,GAAG,EACnB;oBACA,IAAIyiB,IAAAA,gBAAO,EAAC9b,MAAMA,IAAIqF,IAAI,GAAGA;oBAC7B,MAAMrF;gBACR;gBACA,IAAI,CAACW,QAAQ,CAACmJ,IAAAA,uBAAc,EAAC9J;YAC/B;YACA,MAAMwJ,WAAW,MAAM,IAAI,CAACmS,qBAAqB,CAC/C1b,KACA4b,iBAAiB,AAAC7b,IAA0BhM,UAAU,GAAGgM;YAE3D,OAAOwJ;QACT;QAEA,IACE,IAAI,CAACxT,aAAa,MAClB,CAAC,CAACiK,IAAIvL,GAAG,CAACQ,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACY,IAAIgM,UAAU,IAAIhM,IAAIgM,UAAU,KAAK,OAAOhM,IAAIgM,UAAU,KAAK,GAAE,GACnE;YACAhM,IAAI0V,SAAS,CACX,yBACA,GAAG3T,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAED,MAAMC,YAAY,EAAE,GAAG,KAAKjD,UAAU;YAEpEiB,IAAIgM,UAAU,GAAG;YACjBhM,IAAI0V,SAAS,CAAC,gBAAgB;YAC9B1V,IAAIgN,IAAI,CAAC;YACThN,IAAIiN,IAAI;YACR,OAAO;QACT;QAEAjN,IAAIgM,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC6Z,qBAAqB,CAAC1b,KAAK;IACzC;IAEA,MAAa8b,aACXrnB,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBgD,QAAwB,CAAC,CAAC,EACF;QACxB,OAAOmJ,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC4a,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAACtnB,KAAKoB,KAAKjB,UAAUgD;QACnD;IACF;IAEA,MAAcmkB,iBACZtnB,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBgD,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC6T,aAAa,CAAC,CAACzL,MAAQ,IAAI,CAACqM,gBAAgB,CAACrM,MAAM;YAC7DvL;YACAoB;YACAjB;YACAgD;QACF;IACF;IAEA,MAAa8P,YACX3H,GAAiB,EACjBtL,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBgD,QAA4B,CAAC,CAAC,EAC9BokB,aAAa,IAAI,EACF;QACf,OAAOjb,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACwG,WAAW,EAAE;YACnD,OAAO,IAAI,CAACuU,eAAe,CAAClc,KAAKtL,KAAKoB,KAAKjB,UAAUgD,OAAOokB;QAC9D;IACF;IAEA,MAAcC,gBACZlc,GAAiB,EACjBtL,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBgD,QAA4B,CAAC,CAAC,EAC9BokB,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACdnmB,IAAI0V,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAACX,IAAI,CACd,OAAO5K;YACL,MAAMuJ,WAAW,MAAM,IAAI,CAACmS,qBAAqB,CAAC1b,KAAKD;YACvD,IAAI,IAAI,CAAC1G,WAAW,IAAIxD,IAAIgM,UAAU,KAAK,KAAK;gBAC9C,MAAM9B;YACR;YACA,OAAOwJ;QACT,GACA;YAAE9U;YAAKoB;YAAKjB;YAAUgD;QAAM;IAEhC;IAQA,MAAc8jB,sBACZ1b,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOgB,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACwa,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACQ,yBAAyB,CAAClc,KAAKD;QAC7C;IACF;IAEA,MAAgBmc,0BACdlc,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAACnE,UAAU,CAACxC,GAAG,IAAI4G,IAAIpL,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACLwW,MAAM;gBACNvI,MAAM8N,qBAAY,CAACC,UAAU,CAAC;YAChC;QACF;QACA,MAAM,EAAE/a,GAAG,EAAE+B,KAAK,EAAE,GAAGoI;QAEvB,IAAI;YACF,IAAIsJ,SAAsC;YAE1C,MAAM6S,QAAQtmB,IAAIgM,UAAU,KAAK;YACjC,IAAIua,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAAC1jB,kBAAkB,CAACyC,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CoO,SAAS,MAAM,IAAI,CAACoR,kBAAkB,CAAC;wBACrCtV,MAAMiX,2CAAgC;wBACtCzkB;wBACA5B,QAAQ,CAAC;wBACTuX,WAAW;wBACXuN,cAAc;wBACdtlB,KAAKwK,IAAIvL,GAAG,CAACe,GAAG;oBAClB;oBACA4mB,eAAe9S,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAAC6C,OAAO,CAAC,SAAU;oBAC3C7C,SAAS,MAAM,IAAI,CAACoR,kBAAkB,CAAC;wBACrCtV,MAAM;wBACNxN;wBACA5B,QAAQ,CAAC;wBACTuX,WAAW;wBACX,qEAAqE;wBACrEuN,cAAc;wBACdtlB,KAAKwK,IAAIvL,GAAG,CAACe,GAAG;oBAClB;oBACA4mB,eAAe9S,WAAW;gBAC5B;YACF;YACA,IAAIgT,aAAa,CAAC,CAAC,EAAEzmB,IAAIgM,UAAU,EAAE;YAErC,IACE,CAAC7B,IAAIpI,KAAK,CAAC+jB,uBAAuB,IAClC,CAACrS,UACDkH,8BAAmB,CAACpB,QAAQ,CAACkN,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAAC1gB,UAAU,CAACxC,GAAG,EAAE;oBACjDkQ,SAAS,MAAM,IAAI,CAACoR,kBAAkB,CAAC;wBACrCtV,MAAMkX;wBACN1kB;wBACA5B,QAAQ,CAAC;wBACTuX,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACTuN,cAAc;wBACdtlB,KAAKwK,IAAIvL,GAAG,CAACe,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAAC8T,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAACoR,kBAAkB,CAAC;oBACrCtV,MAAM;oBACNxN;oBACA5B,QAAQ,CAAC;oBACTuX,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACTuN,cAAc;oBACdtlB,KAAKwK,IAAIvL,GAAG,CAACe,GAAG;gBAClB;gBACA8mB,aAAa;YACf;YAEA,IACElmB,QAAQC,GAAG,CAACkmB,QAAQ,KAAK,gBACzB,CAACH,gBACA,MAAM,IAAI,CAACjQ,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAACtT,oBAAoB;YAC3B;YAEA,IAAI,CAACyQ,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAAC1N,UAAU,CAACxC,GAAG,EAAE;oBACvB,OAAO;wBACLgS,MAAM;wBACN,mDAAmD;wBACnDvI,MAAM8N,qBAAY,CAACC,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC;oBAEd;gBACF;gBAEA,MAAM,IAAIjd,kBACR,IAAIE,MAAM;YAEd;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAIyV,OAAOuE,UAAU,CAAC4B,WAAW,EAAE;gBACjCpa,IAAAA,2BAAc,EAAC2K,IAAIvL,GAAG,EAAE,SAAS;oBAC/BwQ,YAAYqE,OAAOuE,UAAU,CAAC4B,WAAW,CAACxK,UAAU;oBACpDjP,QAAQ1B;gBACV;YACF,OAAO;gBACLkoB,IAAAA,8BAAiB,EAACxc,IAAIvL,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAACmY,8BAA8B,CAC9C;oBACE,GAAG5M,GAAG;oBACNpL,UAAU0nB;oBACV1gB,YAAY;wBACV,GAAGoE,IAAIpE,UAAU;wBACjBmE;oBACF;gBACF,GACAuJ;YAEJ,EAAE,OAAOmT,oBAAoB;gBAC3B,IAAIA,8BAA8B/oB,iBAAiB;oBACjD,MAAM,IAAIG,MAAM;gBAClB;gBACA,MAAM4oB;YACR;QACF,EAAE,OAAOhc,OAAO;YACd,MAAMic,oBAAoB7S,IAAAA,uBAAc,EAACpJ;YACzC,MAAMmb,iBAAiBc,6BAA6B/oB;YACpD,IAAI,CAACioB,gBAAgB;gBACnB,IAAI,CAAClb,QAAQ,CAACgc;YAChB;YACA7mB,IAAIgM,UAAU,GAAG;YACjB,MAAM8a,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9D5c,IAAIvL,GAAG,CAACe,GAAG;YAGb,IAAImnB,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;gBACnCtnB,IAAAA,2BAAc,EAAC2K,IAAIvL,GAAG,EAAE,SAAS;oBAC/BwQ,YAAY0X,mBAAmBlN,WAAW,CAAExK,UAAU;oBACtDjP,QAAQ1B;gBACV;gBAEA,OAAO,IAAI,CAACsY,8BAA8B,CACxC;oBACE,GAAG5M,GAAG;oBACNpL,UAAU;oBACVgH,YAAY;wBACV,GAAGoE,IAAIpE,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtCmE,KAAK6b,iBACDc,kBAAkB3oB,UAAU,GAC5B2oB;oBACN;gBACF,GACA;oBACE9kB;oBACAiW,YAAY8O;gBACd;YAEJ;YACA,OAAO;gBACLvR,MAAM;gBACNvI,MAAM8N,qBAAY,CAACC,UAAU,CAAC;YAChC;QACF;IACF;IAEA,MAAaiM,kBACX9c,GAAiB,EACjBtL,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBgD,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC6T,aAAa,CAAC,CAACzL,MAAQ,IAAI,CAAC0b,qBAAqB,CAAC1b,KAAKD,MAAM;YACvEtL;YACAoB;YACAjB;YACAgD;QACF;IACF;IAEA,MAAapB,UACX/B,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA8D,EAC9DqnB,aAAa,IAAI,EACF;QACf,MAAM,EAAEpnB,QAAQ,EAAEgD,KAAK,EAAE,GAAGjD,YAAYA,YAAYe,IAAAA,UAAQ,EAACjB,IAAIe,GAAG,EAAG;QAEvE,IAAI,IAAI,CAACvB,UAAU,CAACmG,IAAI,EAAE;YACxBxC,MAAMC,YAAY,KAAK,IAAI,CAAC5D,UAAU,CAACmG,IAAI,CAAC7C,aAAa;YACzDK,MAAME,mBAAmB,KAAK,IAAI,CAAC7D,UAAU,CAACmG,IAAI,CAAC7C,aAAa;QAClE;QAEA1B,IAAIgM,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC6F,WAAW,CAAC,MAAMjT,KAAKoB,KAAKjB,UAAWgD,OAAOokB;IAC5D;AACF"}