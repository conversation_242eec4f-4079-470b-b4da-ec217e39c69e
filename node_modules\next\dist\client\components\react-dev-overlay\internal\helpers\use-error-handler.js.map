{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../../../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs } from '../../../../lib/console'\nimport isError from '../../../../../lib/is-error'\nimport { createUnhandledError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from './stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleClientError(\n  originError: unknown,\n  consoleErrorArgs: any[],\n  capturedFromConsole: boolean = false\n) {\n  let error: Error\n  if (!originError || !isError(originError)) {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = formatConsoleArgs(consoleErrorArgs)\n    error = createUnhandledError(formattedErrorMessage)\n  } else {\n    error = capturedFromConsole\n      ? createUnhandledError(originError)\n      : originError\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  handleClientError(event.error, [])\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = createUnhandledError(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["handleClientError", "handleGlobalErrors", "useErrorHandler", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "originError", "consoleErrorArgs", "capturedFromConsole", "error", "isError", "formattedErrorMessage", "formatConsoleArgs", "createUnhandledError", "getReactStitchedError", "storeHydrationErrorStateFromConsoleArgs", "attachHydrationErrorState", "enqueueConsecutiveDedupedError", "handler", "handleOnUnhandledError", "handleOnUnhandledRejection", "useEffect", "for<PERSON>ach", "push", "splice", "indexOf", "onUnhandledError", "event", "isNextRouterError", "preventDefault", "onUnhandledRejection", "ev", "reason", "window", "Error", "stackTraceLimit", "addEventListener"], "mappings": ";;;;;;;;;;;;;;;;IAoBgBA,iBAAiB;eAAjBA;;IAgFAC,kBAAkB;eAAlBA;;IAlDAC,eAAe;eAAfA;;;;uBAlDU;2CACgB;mCACR;oCACsB;yBACtB;kEACd;8BACiB;oCACU;+BACT;AAEtC,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAE1C,SAASb,kBACdc,WAAoB,EACpBC,gBAAuB,EACvBC,mBAAoC;IAApCA,IAAAA,gCAAAA,sBAA+B;IAE/B,IAAIC;IACJ,IAAI,CAACH,eAAe,CAACI,IAAAA,gBAAO,EAACJ,cAAc;QACzC,sDAAsD;QACtD,MAAMK,wBAAwBC,IAAAA,0BAAiB,EAACL;QAChDE,QAAQI,IAAAA,kCAAoB,EAACF;IAC/B,OAAO;QACLF,QAAQD,sBACJK,IAAAA,kCAAoB,EAACP,eACrBA;IACN;IACAG,QAAQK,IAAAA,oCAAqB,EAACL;IAE9BM,IAAAA,2DAAuC,KAAIR;IAC3CS,IAAAA,oDAAyB,EAACP;IAE1BQ,IAAAA,kDAA8B,EAACf,YAAYO;IAC3C,KAAK,MAAMS,WAAWf,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbuB,QAAQT;QACV;IACF;AACF;AAEO,SAASf,gBACdyB,sBAAoC,EACpCC,0BAAwC;IAExCC,IAAAA,gBAAS,EAAC;QACR,wBAAwB;QACxBnB,WAAWoB,OAAO,CAACH;QACnBf,eAAekB,OAAO,CAACF;QAEvB,wBAAwB;QACxBjB,cAAcoB,IAAI,CAACJ;QACnBd,kBAAkBkB,IAAI,CAACH;QAEvB,OAAO;YACL,oBAAoB;YACpBjB,cAAcqB,MAAM,CAACrB,cAAcsB,OAAO,CAACN,yBAAyB;YACpEd,kBAAkBmB,MAAM,CACtBnB,kBAAkBoB,OAAO,CAACL,6BAC1B;QAEJ;IACF,GAAG;QAACD;QAAwBC;KAA2B;AACzD;AAEA,SAASM,iBAAiBC,KAA8B;IACtD,IAAIC,IAAAA,oCAAiB,EAACD,MAAMlB,KAAK,GAAG;QAClCkB,MAAME,cAAc;QACpB,OAAO;IACT;IACArC,kBAAkBmC,MAAMlB,KAAK,EAAE,EAAE;AACnC;AAEA,SAASqB,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,sBAAAA,GAAIC,MAAM;IACzB,IAAIJ,IAAAA,oCAAiB,EAACI,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAIpB,QAAQuB;IACZ,IAAIvB,SAAS,CAACC,IAAAA,gBAAO,EAACD,QAAQ;QAC5BA,QAAQI,IAAAA,kCAAoB,EAACJ,QAAQ;IACvC;IAEAL,eAAemB,IAAI,CAACd;IACpB,KAAK,MAAMS,WAAWb,kBAAmB;QACvCa,QAAQT;IACV;AACF;AAEO,SAAShB;IACd,IAAI,OAAOwC,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,UAAM,CAAC;QAETF,OAAOG,gBAAgB,CAAC,SAASV;QACjCO,OAAOG,gBAAgB,CAAC,sBAAsBN;IAChD;AACF"}