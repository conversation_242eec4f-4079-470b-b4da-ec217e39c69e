'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Activity, 
  Calendar, 
  Clock, 
  TrendingUp, 
  AlertCircle,
  RefreshCw,
  Download
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ApiKeyUsageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  apiKey: any;
}

export function ApiKeyUsageDialog({
  open,
  onOpenChange,
  apiKey
}: ApiKeyUsageDialogProps) {
  const [usageData, setUsageData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && apiKey?.id) {
      fetchUsageData();
    }
  }, [open, apiKey?.id]);

  const fetchUsageData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/user-api-keys/${apiKey.id}/usage`);
      
      if (response.ok) {
        const data = await response.json();
        setUsageData(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch usage data');
      }
    } catch (error) {
      console.error('Error fetching usage data:', error);
      setError('Failed to fetch usage data');
    } finally {
      setLoading(false);
    }
  };

  const exportUsageData = async () => {
    try {
      const response = await fetch(`/api/user-api-keys/${apiKey.id}/usage/export`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `api-key-usage-${apiKey.key_name}-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        setError('Failed to export usage data');
      }
    } catch (error) {
      console.error('Error exporting usage data:', error);
      setError('Failed to export usage data');
    }
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              API Key Usage - {apiKey?.key_name}
            </DialogTitle>
            <DialogDescription>
              Loading usage statistics and analytics...
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              API Key Usage - {apiKey?.key_name}
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Error Loading Usage Data</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchUsageData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                API Key Usage - {apiKey?.key_name}
              </DialogTitle>
              <DialogDescription>
                Detailed usage statistics and analytics for your API key
              </DialogDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={fetchUsageData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={exportUsageData}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </DialogHeader>

        {usageData ? (
          <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Requests</p>
                      <p className="text-2xl font-bold">{usageData.totalRequests?.toLocaleString() || 0}</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">This Month</p>
                      <p className="text-2xl font-bold">{usageData.thisMonth?.toLocaleString() || 0}</p>
                    </div>
                    <Calendar className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Today</p>
                      <p className="text-2xl font-bold">{usageData.today?.toLocaleString() || 0}</p>
                    </div>
                    <Clock className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Last Hour</p>
                      <p className="text-2xl font-bold">{usageData.lastHour?.toLocaleString() || 0}</p>
                    </div>
                    <Activity className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Rate Limit Status */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Rate Limit Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Per Minute</p>
                    <div className="mt-2">
                      <p className="text-lg font-semibold">
                        {usageData.rateLimitStatus?.perMinute?.current || 0} / {apiKey.rate_limit_per_minute}
                      </p>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ 
                            width: `${Math.min(100, ((usageData.rateLimitStatus?.perMinute?.current || 0) / apiKey.rate_limit_per_minute) * 100)}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Per Hour</p>
                    <div className="mt-2">
                      <p className="text-lg font-semibold">
                        {usageData.rateLimitStatus?.perHour?.current || 0} / {apiKey.rate_limit_per_hour}
                      </p>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ 
                            width: `${Math.min(100, ((usageData.rateLimitStatus?.perHour?.current || 0) / apiKey.rate_limit_per_hour) * 100)}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Per Day</p>
                    <div className="mt-2">
                      <p className="text-lg font-semibold">
                        {usageData.rateLimitStatus?.perDay?.current || 0} / {apiKey.rate_limit_per_day}
                      </p>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-orange-600 h-2 rounded-full" 
                          style={{ 
                            width: `${Math.min(100, ((usageData.rateLimitStatus?.perDay?.current || 0) / apiKey.rate_limit_per_day) * 100)}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            {usageData.recentActivity && usageData.recentActivity.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {usageData.recentActivity.slice(0, 10).map((activity: any, index: number) => (
                      <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-center gap-3">
                          <Badge variant={activity.status === 'success' ? 'default' : 'destructive'} className="text-xs">
                            {activity.status}
                          </Badge>
                          <span className="text-sm">{activity.endpoint || 'API Request'}</span>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">
                            {activity.timestamp ? formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true }) : 'Unknown time'}
                          </p>
                          {activity.ip_address && (
                            <p className="text-xs text-gray-500">{activity.ip_address}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Activity className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Usage Data</h3>
            <p className="text-gray-600">
              This API key hasn't been used yet or usage data is not available.
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
