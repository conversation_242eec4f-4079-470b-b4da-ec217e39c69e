'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Key, 
  Shield, 
  Clock, 
  Globe, 
  AlertTriangle,
  Copy,
  Eye,
  EyeOff,
  Plus,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface CreateApiKeyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateApiKey: (keyData: any) => Promise<any>;
  configName: string;
  creating: boolean;
  subscriptionTier: string;
}

export function CreateApiKeyDialog({
  open,
  onOpenChange,
  onCreateApiKey,
  configName,
  creating,
  subscriptionTier
}: CreateApiKeyDialogProps) {
  const [step, setStep] = useState<'form' | 'success'>('form');
  const [createdApiKey, setCreatedApiKey] = useState<any>(null);
  const [showFullKey, setShowFullKey] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    key_name: '',
    permissions: {
      chat: true,
      streaming: true,
      all_models: true
    },
    rate_limit_per_minute: getDefaultRateLimits(subscriptionTier).per_minute,
    rate_limit_per_hour: getDefaultRateLimits(subscriptionTier).per_hour,
    rate_limit_per_day: getDefaultRateLimits(subscriptionTier).per_day,
    allowed_ips: [] as string[],
    allowed_domains: [] as string[],
    expires_at: ''
  });

  const [ipInput, setIpInput] = useState('');
  const [domainInput, setDomainInput] = useState('');

  function getDefaultRateLimits(tier: string) {
    const limits = {
      starter: { per_minute: 30, per_hour: 500, per_day: 5000 },
      professional: { per_minute: 100, per_hour: 2000, per_day: 20000 },
      enterprise: { per_minute: 300, per_hour: 10000, per_day: 100000 }
    };
    return limits[tier as keyof typeof limits] || limits.starter;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.key_name.trim()) {
      toast.error('Please enter a name for your API key');
      return;
    }

    try {
      const result = await onCreateApiKey({
        ...formData,
        key_name: formData.key_name.trim(),
        expires_at: formData.expires_at || undefined
      });
      
      setCreatedApiKey(result);
      setStep('success');
    } catch (error) {
      // Error is handled in the parent component
    }
  };

  const addIpAddress = () => {
    if (ipInput.trim() && !formData.allowed_ips.includes(ipInput.trim())) {
      setFormData(prev => ({
        ...prev,
        allowed_ips: [...prev.allowed_ips, ipInput.trim()]
      }));
      setIpInput('');
    }
  };

  const removeIpAddress = (ip: string) => {
    setFormData(prev => ({
      ...prev,
      allowed_ips: prev.allowed_ips.filter(i => i !== ip)
    }));
  };

  const addDomain = () => {
    if (domainInput.trim() && !formData.allowed_domains.includes(domainInput.trim())) {
      setFormData(prev => ({
        ...prev,
        allowed_domains: [...prev.allowed_domains, domainInput.trim()]
      }));
      setDomainInput('');
    }
  };

  const removeDomain = (domain: string) => {
    setFormData(prev => ({
      ...prev,
      allowed_domains: prev.allowed_domains.filter(d => d !== domain)
    }));
  };

  const copyApiKey = async () => {
    if (createdApiKey?.api_key) {
      try {
        await navigator.clipboard.writeText(createdApiKey.api_key);
        toast.success('API key copied to clipboard');
      } catch (error) {
        toast.error('Failed to copy API key');
      }
    }
  };

  const handleClose = () => {
    setStep('form');
    setCreatedApiKey(null);
    setShowFullKey(false);
    setFormData({
      key_name: '',
      permissions: {
        chat: true,
        streaming: true,
        all_models: true
      },
      rate_limit_per_minute: getDefaultRateLimits(subscriptionTier).per_minute,
      rate_limit_per_hour: getDefaultRateLimits(subscriptionTier).per_hour,
      rate_limit_per_day: getDefaultRateLimits(subscriptionTier).per_day,
      allowed_ips: [],
      allowed_domains: [],
      expires_at: ''
    });
    setIpInput('');
    setDomainInput('');
    onOpenChange(false);
  };

  if (step === 'success' && createdApiKey) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="h-5 w-5 text-green-600" />
              API Key Created Successfully
            </DialogTitle>
            <DialogDescription>
              Your API key has been created. Make sure to copy it now as you won't be able to see it again.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Alert className="border-amber-200 bg-amber-50">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                <strong>Important:</strong> This is the only time you'll see the full API key. 
                Make sure to copy and store it securely.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Label>API Key</Label>
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
                <code className="flex-1 text-sm font-mono">
                  {showFullKey ? createdApiKey.api_key : `${createdApiKey.key_prefix}_${'*'.repeat(32)}`}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFullKey(!showFullKey)}
                  className="h-8 w-8 p-0"
                >
                  {showFullKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyApiKey}
                  className="h-8 w-8 p-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label>Key Name</Label>
                <p className="font-medium">{createdApiKey.key_name}</p>
              </div>
              <div>
                <Label>Created</Label>
                <p className="font-medium">{new Date(createdApiKey.created_at).toLocaleString()}</p>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Rate Limits</Label>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-semibold text-blue-900">{createdApiKey.rate_limits.per_minute}</div>
                  <div className="text-blue-700">per minute</div>
                </div>
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-semibold text-blue-900">{createdApiKey.rate_limits.per_hour}</div>
                  <div className="text-blue-700">per hour</div>
                </div>
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-semibold text-blue-900">{createdApiKey.rate_limits.per_day}</div>
                  <div className="text-blue-700">per day</div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={handleClose} className="w-full">
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Create API Key
          </DialogTitle>
          <DialogDescription>
            Create a new API key for programmatic access to {configName}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="key_name">API Key Name *</Label>
              <Input
                id="key_name"
                value={formData.key_name}
                onChange={(e) => setFormData(prev => ({ ...prev, key_name: e.target.value }))}
                placeholder="e.g., Production API Key"
                required
              />
              <p className="text-xs text-gray-600">
                A descriptive name to help you identify this API key
              </p>
            </div>
          </div>

          <Separator />

          {/* Permissions */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <Label className="text-base font-semibold">Permissions</Label>
            </div>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="chat"
                  checked={formData.permissions.chat}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({
                      ...prev,
                      permissions: { ...prev.permissions, chat: !!checked }
                    }))
                  }
                />
                <Label htmlFor="chat" className="text-sm">
                  Chat Completions
                </Label>
                <Badge variant="secondary" className="text-xs">Required</Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="streaming"
                  checked={formData.permissions.streaming}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({
                      ...prev,
                      permissions: { ...prev.permissions, streaming: !!checked }
                    }))
                  }
                />
                <Label htmlFor="streaming" className="text-sm">
                  Streaming Responses
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="all_models"
                  checked={formData.permissions.all_models}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({
                      ...prev,
                      permissions: { ...prev.permissions, all_models: !!checked }
                    }))
                  }
                />
                <Label htmlFor="all_models" className="text-sm">
                  Access to All Models
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Rate Limits */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <Label className="text-base font-semibold">Rate Limits</Label>
              <Badge variant="outline" className="text-xs">
                {subscriptionTier} plan
              </Badge>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="rate_minute">Per Minute</Label>
                <Input
                  id="rate_minute"
                  type="number"
                  min="1"
                  max="1000"
                  value={formData.rate_limit_per_minute}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    rate_limit_per_minute: parseInt(e.target.value) || 0
                  }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rate_hour">Per Hour</Label>
                <Input
                  id="rate_hour"
                  type="number"
                  min="1"
                  max="50000"
                  value={formData.rate_limit_per_hour}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    rate_limit_per_hour: parseInt(e.target.value) || 0
                  }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rate_day">Per Day</Label>
                <Input
                  id="rate_day"
                  type="number"
                  min="1"
                  max="500000"
                  value={formData.rate_limit_per_day}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    rate_limit_per_day: parseInt(e.target.value) || 0
                  }))}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Security Restrictions */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <Label className="text-base font-semibold">Security Restrictions</Label>
              <Badge variant="outline" className="text-xs">Optional</Badge>
            </div>

            {/* IP Restrictions */}
            <div className="space-y-2">
              <Label>Allowed IP Addresses</Label>
              <div className="flex gap-2">
                <Input
                  value={ipInput}
                  onChange={(e) => setIpInput(e.target.value)}
                  placeholder="e.g., *********** or 10.0.0.0/8"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addIpAddress())}
                />
                <Button type="button" onClick={addIpAddress} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.allowed_ips.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.allowed_ips.map((ip) => (
                    <Badge key={ip} variant="secondary" className="text-xs">
                      {ip}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeIpAddress(ip)}
                        className="ml-1 h-3 w-3 p-0"
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
              <p className="text-xs text-gray-600">
                Leave empty to allow all IP addresses
              </p>
            </div>

            {/* Domain Restrictions */}
            <div className="space-y-2">
              <Label>Allowed Domains (CORS)</Label>
              <div className="flex gap-2">
                <Input
                  value={domainInput}
                  onChange={(e) => setDomainInput(e.target.value)}
                  placeholder="e.g., example.com or *.example.com"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDomain())}
                />
                <Button type="button" onClick={addDomain} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.allowed_domains.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.allowed_domains.map((domain) => (
                    <Badge key={domain} variant="secondary" className="text-xs">
                      <Globe className="h-3 w-3 mr-1" />
                      {domain}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDomain(domain)}
                        className="ml-1 h-3 w-3 p-0"
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
              <p className="text-xs text-gray-600">
                Leave empty to allow all domains
              </p>
            </div>
          </div>

          <Separator />

          {/* Expiration */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="expires_at">Expiration Date (Optional)</Label>
              <Input
                id="expires_at"
                type="datetime-local"
                value={formData.expires_at}
                onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                min={new Date().toISOString().slice(0, 16)}
              />
              <p className="text-xs text-gray-600">
                Leave empty for no expiration
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? 'Creating...' : 'Create API Key'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
