{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../../../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs } from '../../../../lib/console'\nimport isError from '../../../../../lib/is-error'\nimport { createUnhandledError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from './stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleClientError(\n  originError: unknown,\n  consoleErrorArgs: any[],\n  capturedFromConsole: boolean = false\n) {\n  let error: Error\n  if (!originError || !isError(originError)) {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = formatConsoleArgs(consoleErrorArgs)\n    error = createUnhandledError(formattedErrorMessage)\n  } else {\n    error = capturedFromConsole\n      ? createUnhandledError(originError)\n      : originError\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  handleClientError(event.error, [])\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = createUnhandledError(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["useEffect", "attachHydrationErrorState", "isNextRouterError", "storeHydrationErrorStateFromConsoleArgs", "formatConsoleArgs", "isError", "createUnhandledError", "enqueueConsecutiveDedupedError", "getReactStitchedError", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "handleClientError", "originError", "consoleErrorArgs", "capturedFromConsole", "error", "formattedErrorMessage", "handler", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "push", "splice", "indexOf", "onUnhandledError", "event", "preventDefault", "onUnhandledRejection", "ev", "reason", "handleGlobalErrors", "window", "Error", "stackTraceLimit", "addEventListener"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,yBAAyB,QAAQ,iCAAgC;AAC1E,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,uCAAuC,QAAQ,yBAAwB;AAChF,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,OAAOC,aAAa,8BAA6B;AACjD,SAASC,oBAAoB,QAAQ,kBAAiB;AACtD,SAASC,8BAA8B,QAAQ,yBAAwB;AACvE,SAASC,qBAAqB,QAAQ,mBAAkB;AAExD,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAEjD,OAAO,SAASC,kBACdC,WAAoB,EACpBC,gBAAuB,EACvBC,mBAAoC;IAApCA,IAAAA,gCAAAA,sBAA+B;IAE/B,IAAIC;IACJ,IAAI,CAACH,eAAe,CAAChB,QAAQgB,cAAc;QACzC,sDAAsD;QACtD,MAAMI,wBAAwBrB,kBAAkBkB;QAChDE,QAAQlB,qBAAqBmB;IAC/B,OAAO;QACLD,QAAQD,sBACJjB,qBAAqBe,eACrBA;IACN;IACAG,QAAQhB,sBAAsBgB;IAE9BrB,2CAA2CmB;IAC3CrB,0BAA0BuB;IAE1BjB,+BAA+BS,YAAYQ;IAC3C,KAAK,MAAME,WAAWT,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbiB,QAAQF;QACV;IACF;AACF;AAEA,OAAO,SAASG,gBACdC,sBAAoC,EACpCC,0BAAwC;IAExC7B,UAAU;QACR,wBAAwB;QACxBgB,WAAWc,OAAO,CAACF;QACnBV,eAAeY,OAAO,CAACD;QAEvB,wBAAwB;QACxBZ,cAAcc,IAAI,CAACH;QACnBT,kBAAkBY,IAAI,CAACF;QAEvB,OAAO;YACL,oBAAoB;YACpBZ,cAAce,MAAM,CAACf,cAAcgB,OAAO,CAACL,yBAAyB;YACpET,kBAAkBa,MAAM,CACtBb,kBAAkBc,OAAO,CAACJ,6BAC1B;QAEJ;IACF,GAAG;QAACD;QAAwBC;KAA2B;AACzD;AAEA,SAASK,iBAAiBC,KAA8B;IACtD,IAAIjC,kBAAkBiC,MAAMX,KAAK,GAAG;QAClCW,MAAMC,cAAc;QACpB,OAAO;IACT;IACAhB,kBAAkBe,MAAMX,KAAK,EAAE,EAAE;AACnC;AAEA,SAASa,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,sBAAAA,GAAIC,MAAM;IACzB,IAAIrC,kBAAkBqC,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAIZ,QAAQe;IACZ,IAAIf,SAAS,CAACnB,QAAQmB,QAAQ;QAC5BA,QAAQlB,qBAAqBkB,QAAQ;IACvC;IAEAN,eAAea,IAAI,CAACP;IACpB,KAAK,MAAME,WAAWP,kBAAmB;QACvCO,QAAQF;IACV;AACF;AAEA,OAAO,SAASgB;IACd,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,UAAM,CAAC;QAETF,OAAOG,gBAAgB,CAAC,SAASV;QACjCO,OAAOG,gBAAgB,CAAC,sBAAsBP;IAChD;AACF"}