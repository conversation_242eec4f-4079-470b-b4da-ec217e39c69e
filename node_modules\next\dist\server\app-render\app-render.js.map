{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  FlightSegmentPath,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n} from '../../client/components/app-router-headers'\nimport {\n  createTrackedMetadataContext,\n  createMetadataContext,\n} from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport {\n  getShortDynamicParamType,\n  dynamicParamTypes,\n} from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { setReferenceManifestsSingleton } from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createPostponedAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  getFirstDynamicReason,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  consumeDynamicAccess,\n  type DynamicAccess,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseParameter } from '../../shared/lib/router/utils/route-regex'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../shared/lib/router/action-queue'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { ServerPrerenderStreamResult } from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n  prerenderServerWithPhases,\n  prerenderClientWithPhases,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport './clean-async-snapshot.external'\nimport { INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n} | null\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppRenderContext = {\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n}\n\nconst flightDataPathHeadKey = 'h'\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isPrefetchRequest =\n    isDevWarmupRequest ||\n    headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] !== undefined\n\n  const isHmrRefresh =\n    headers[NEXT_HMR_REFRESH_HEADER.toLowerCase()] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest =\n    isDevWarmupRequest || headers[RSC_HEADER.toLowerCase()] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n      )\n    : undefined\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  // Align the segment with parallel-route-default in next-app-loader\n  const components = loaderTree[2]\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['not-found'],\n        },\n      ],\n    },\n    components,\n  ]\n}\n\nexport type CreateSegmentPath = (child: FlightSegmentPath) => FlightSegmentPath\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n\n    const key = segmentParam.param\n\n    let value = params[key]\n\n    if (fallbackRouteParams && fallbackRouteParams.has(segmentParam.param)) {\n      value = fallbackRouteParams.get(segmentParam.param)\n    } else if (Array.isArray(value)) {\n      value = value.map((i) => encodeURIComponent(i))\n    } else if (typeof value === 'string') {\n      value = encodeURIComponent(value)\n    }\n\n    if (!value) {\n      const isCatchall = segmentParam.type === 'catchall'\n      const isOptionalCatchall = segmentParam.type === 'optional-catchall'\n\n      if (isCatchall || isOptionalCatchall) {\n        const dynamicParamType = dynamicParamTypes[segmentParam.type]\n        // handle the case where an optional catchall does not have a value,\n        // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n        if (isOptionalCatchall) {\n          return {\n            param: key,\n            value: null,\n            type: dynamicParamType,\n            treeSegment: [key, '', dynamicParamType],\n          }\n        }\n\n        // handle the case where a catchall or optional catchall does not have a value,\n        // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n        value = pagePath\n          .split('/')\n          // remove the first empty string\n          .slice(1)\n          // replace any dynamic params with the actual values\n          .flatMap((pathSegment) => {\n            const param = parseParameter(pathSegment)\n            // if the segment matches a param, return the param value\n            // otherwise, it's a static segment, so just return that\n            return params[param.key] ?? param.key\n          })\n\n        return {\n          param: key,\n          value,\n          type: dynamicParamType,\n          // This value always has to be a string.\n          treeSegment: [key, value.join('/'), dynamicParamType],\n        }\n      }\n    }\n\n    const type = getShortDynamicParamType(segmentParam.type)\n\n    return {\n      param: key,\n      // The value that is passed to user code.\n      value: value,\n      // The value that is rendered in the router tree.\n      treeSegment: [key, Array.isArray(value) ? value.join('/') : value, type],\n      type: type,\n    }\n  }\n}\n\nfunction NonIndex({ ctx }: { ctx: AppRenderContext }) {\n  const is404Page = ctx.pagePath === '/404'\n  const isInvalidStatusCode =\n    typeof ctx.res.statusCode === 'number' && ctx.res.statusCode > 400\n\n  if (is404Page || isInvalidStatusCode) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `Next-Router-State-Tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const searchParams = createServerSearchParamsForMetadata(query, workStore)\n    const [MetadataTree, getMetadataReady] = createMetadataComponents({\n      tree: loaderTree,\n      searchParams,\n      metadataContext: createTrackedMetadataContext(\n        url.pathname,\n        ctx.renderOpts,\n        workStore\n      ),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      createServerParamsForMetadata,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n    })\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        createSegmentPath: (child) => child,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        isFirst: true,\n        // For flight, render metadata inside leaf page\n        rscPayloadHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            <NonIndex ctx={ctx} />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <MetadataTree key={requestId} />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getMetadataReady,\n        preloadCallbacks,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.renderOpts.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.renderOpts.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    routeType: ctx.isAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during dynamicIO development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  if (\n    // We only want this behavior when running `next dev`\n    renderOpts.dev &&\n    // We only want this behavior when we have React's dev builds available\n    process.env.NODE_ENV === 'development' &&\n    // We only have a Prerender environment for projects opted into dynamicIO\n    renderOpts.experimental.dynamicIO\n  ) {\n    const [resolveValidation, validationOutlet] = createValidationOutlet()\n    RSCPayload._validation = validationOutlet\n\n    spawnDynamicValidationInDev(\n      resolveValidation,\n      ctx.componentMod.tree,\n      ctx,\n      false,\n      ctx.clientReferenceManifest,\n      ctx.workStore.route,\n      requestStore\n    ).catch(resolveValidation) // avoid unhandled rejections and a forever hanging promise\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n  if (!renderOpts.dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const cacheSignal = new CacheSignal()\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    implicitTags: [],\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    ctx.componentMod.renderToReadableStream,\n    rscPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling\n  await cacheSignal.cacheReady()\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n    devRenderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  const searchParams = createServerSearchParamsForMetadata(query, workStore)\n  const [MetadataTree, getMetadataReady] = createMetadataComponents({\n    tree,\n    errorType: is404 ? 'not-found' : undefined,\n    searchParams,\n    metadataContext: createTrackedMetadataContext(\n      url.pathname,\n      ctx.renderOpts,\n      workStore\n    ),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    createServerParamsForMetadata,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    createSegmentPath: (child) => child,\n    loaderTree: tree,\n    parentParams: {},\n    firstItem: true,\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex ctx={ctx} />\n      {/* Adding requestId as react key to make metadata remount for each render */}\n      <MetadataTree key={ctx.requestId} />\n    </React.Fragment>\n  )\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.renderOpts.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    requestId,\n    workStore,\n  } = ctx\n\n  const searchParams = createServerSearchParamsForMetadata(query, workStore)\n  const [MetadataTree] = createMetadataComponents({\n    tree,\n    searchParams,\n    // We create an untracked metadata context here because we can't postpone\n    // again during the error render.\n    metadataContext: createMetadataContext(url.pathname, ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    createServerParamsForMetadata,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n  })\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex ctx={ctx} />\n      {/* Adding requestId as react key to make metadata remount for each render */}\n      <MetadataTree key={requestId} />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const initialSeedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head></head>\n      <body></body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.renderOpts.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        initialSeedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n  ServerInsertedHTMLProvider,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    // location and initialParallelRoutes are not initialized in the SSR render\n    // they are set to an empty map and window.location, respectively during hydration\n    initialParallelRoutes: null!,\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedHTMLProvider>\n        <AppRouter\n          actionQueue={actionQueue}\n          globalErrorComponentAndStyles={response.G}\n          assetPrefix={response.p}\n        />\n      </ServerInsertedHTMLProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction AppWithoutContext<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    // location and initialParallelRoutes are not initialized in the SSR render\n    // they are set to an empty map and window.location, respectively during hydration\n    initialParallelRoutes: null!,\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState)\n\n  return (\n    <AppRouter\n      actionQueue={actionQueue}\n      globalErrorComponentAndStyles={response.G}\n      assetPrefix={response.p}\n    />\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  requestEndedState: { ended?: boolean },\n  postponedState: PostponedState | null,\n  implicitTags: Array<string>,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n    // @ts-ignore\n    globalThis.__next_require__ = instrumented.require\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we wrap the loadChunk in this tracking. This allows us\n    // to treat chunk loading with similar semantics as cache reads to avoid\n    // async loading chunks from causing a prerender to abort too early.\n    // @ts-ignore\n    globalThis.__next_chunk_load__ = (...args: Array<any>) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      trackChunkLoading(loadingChunk)\n      return loadingChunk\n    }\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setAppIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    req.originalRequest.on('end', () => {\n      requestEndedState.ended = true\n\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to client components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = require('next/dist/compiled/nanoid').nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const { isStaticGeneration, fallbackRouteParams } = workStore\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isActionRequest = getServerActionRequestMetadata(req).isServerAction\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isAction: isActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      workStore,\n      loaderTree,\n      implicitTags\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.revalidatedTags\n    ) {\n      options.waitUntil = Promise.all([\n        workStore.incrementalCache?.revalidateTag(\n          workStore.revalidatedTags || []\n        ),\n        ...Object.values(workStore.pendingRevalidates || {}),\n        ...(workStore.pendingRevalidateWrites || []),\n      ])\n    }\n\n    if (response.collectedTags) {\n      metadata.fetchTags = response.collectedTags.join(',')\n    }\n\n    // Let the client router know how long to keep the cached entry around.\n    const staleHeader = String(response.collectedStale)\n    res.setHeader(NEXT_ROUTER_STALE_TIME_HEADER, staleHeader)\n    metadata.headers ??= {}\n    metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n    // If force static is specifically set to false, we should not revalidate\n    // the page.\n    if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n      metadata.revalidate = 0\n    } else {\n      // Copy the revalidation value onto the render result metadata.\n      metadata.revalidate =\n        response.collectedRevalidate >= INFINITE_CACHE\n          ? false\n          : response.collectedRevalidate\n    }\n\n    // provide bailout info for debugging\n    if (metadata.revalidate === 0) {\n      metadata.staticBailoutInfo = {\n        description: workStore.dynamicUsageDescription,\n        stack: workStore.dynamicUsageStack,\n      }\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.devRenderResumeDataCache ??\n      postponedState?.renderResumeDataCache\n\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setAppIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setAppIsrStatus = renderOpts.setAppIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setAppIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      return generateDynamicFlightRenderResult(req, ctx, requestStore)\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            workStore,\n            notFoundLoaderTree,\n            formState,\n            postponedState\n          )\n\n          return new RenderResult(stream, { metadata })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      workStore,\n      loaderTree,\n      formState,\n      postponedState\n    )\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.revalidatedTags\n    ) {\n      options.waitUntil = Promise.all([\n        workStore.incrementalCache?.revalidateTag(\n          workStore.revalidatedTags || []\n        ),\n        ...Object.values(workStore.pendingRevalidates || {}),\n        ...(workStore.pendingRevalidateWrites || []),\n      ])\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n  })\n\n  const { isPrefetchRequest } = parsedRequestHeaders\n\n  const requestEndedState = { ended: false }\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.devRenderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const implicitTags = getImplicitTags(\n    renderOpts.routeModule.definition.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    fallbackRouteParams,\n    renderOpts,\n    requestEndedState,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    requestEndedState,\n    postponedState,\n    implicitTags,\n    serverComponentsHmrCache\n  )\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null\n): Promise<ReadableStream<Uint8Array>> {\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      renderOpts.dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into dynamicIO\n      renderOpts.experimental.dynamicIO\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during dynamicIO development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame(url: string, _functionName: string): boolean {\n                // The default implementation filters out <anonymous> stack frames\n                // but we want to retain them because current Server Components and\n                // built-in Components in parent stacks don't have source location.\n                return !url.startsWith('node:') && !url.includes('node_modules')\n              },\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        workStore.route,\n        requestStore\n      ).catch(resolveValidation) // avoid unhandled rejections and a forever hanging promise\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          ctx.nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const postponed = getPostponedFromState(postponedState)\n\n        const resume = require('react-dom/server.edge')\n          .resume as (typeof import('react-dom/server.edge'))['resume']\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={ctx.nonce}\n          />,\n          postponed,\n          {\n            onError: htmlRendererErrorHandler,\n            nonce: ctx.nonce,\n          }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            ctx.nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = require('react-dom/server.edge')\n      .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={ctx.nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce: ctx.nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            setHeader(key, value)\n          })\n        },\n        maxHeadersLength: renderOpts.reactMaxHeadersLength,\n        // When debugging the static shell, client-side rendering should be\n        // disabled to prevent blanking out the page.\n        bootstrapScripts: renderOpts.isDebugStaticShell\n          ? []\n          : [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath: renderOpts.basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML = renderOpts.supportsDynamicResponse !== true\n    const validateRootLayout = renderOpts.dev\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        ctx.nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      getServerInsertedHTML,\n      serverInsertedHTMLToHead: true,\n      validateRootLayout,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer: require('react-dom/server.edge'),\n          element: (\n            <AppWithoutContext\n              reactServerStream={errorServerStream}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={ctx.nonce}\n            />\n          ),\n          streamOptions: {\n            nonce: ctx.nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML = renderOpts.supportsDynamicResponse !== true\n      const validateRootLayout = renderOpts.dev\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          ctx.nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        serverInsertedHTMLToHead: true,\n        validateRootLayout,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  route: string,\n  requestStore: RequestStore\n): Promise<void> {\n  const { componentMod: ComponentMod } = ctx\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  const cacheSignal = new CacheSignal()\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    implicitTags: [],\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const initialClientController = new AbortController()\n  const initialClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    implicitTags: [],\n    renderSignal: initialClientController.signal,\n    controller: initialClientController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  let initialServerStream\n  try {\n    initialServerStream = workUnitAsyncStorage.run(\n      initialServerPrerenderStore,\n      ComponentMod.renderToReadableStream,\n      firstAttemptRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // The render aborted before this error was handled which indicates\n            // the error is caused by unfinished components within the render\n            return\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n        signal: initialServerRenderController.signal,\n      }\n    )\n  } catch (err: unknown) {\n    if (\n      initialServerPrerenderController.signal.aborted ||\n      initialServerRenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, route)\n    }\n  }\n\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n  const nonce = '1'\n\n  if (initialServerStream) {\n    const [warmupStream, renderStream] = initialServerStream.tee()\n    initialServerStream = null\n    // Before we attempt the SSR initial render we need to ensure all client modules\n    // are already loaded.\n    await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={renderStream}\n        preinitScripts={() => {}}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (initialClientController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n      }\n    )\n    pendingInitialClientResult.catch((err: unknown) => {\n      if (initialClientController.signal.aborted) {\n        // We aborted the render normally and can ignore this error\n      } else {\n        // We're going to retry to so we normally would suppress this error but\n        // when verbose logging is on we print it\n        if (process.env.__NEXT_VERBOSE_LOGGING) {\n          printDebugThrownValueForProspectiveRender(err, route)\n        }\n      }\n    })\n  }\n\n  await cacheSignal.cacheReady()\n  // It is important that we abort the SSR render first to avoid\n  // connection closed errors from having an incomplete RSC stream\n  initialClientController.abort()\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We've now filled caches and triggered any inadvertent sync bailouts\n  // due to lazy module initialization. We can restart our render to capture results\n\n  const finalServerController = new AbortController()\n  const serverDynamicTracking = createDynamicTrackingState(false)\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    implicitTags: [],\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const finalClientController = new AbortController()\n  const clientDynamicTracking = createDynamicTrackingState(false)\n  const dynamicValidation = createDynamicValidationState()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    implicitTags: [],\n    renderSignal: finalClientController.signal,\n    controller: finalClientController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const finalServerPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverPrerenderStreamResult = await prerenderServerWithPhases(\n    finalServerController.signal,\n    () =>\n      workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.renderToReadableStream,\n        finalServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          onError: (err) => {\n            if (\n              finalServerController.signal.aborted &&\n              isPrerenderInterruptedError(err)\n            ) {\n              return err.digest\n            }\n\n            return getDigestForWellKnownError(err)\n          },\n          signal: finalServerController.signal,\n        }\n      ),\n    () => {\n      finalServerController.abort()\n    }\n  )\n\n  const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n  try {\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    await prerenderClientWithPhases(\n      () =>\n        workUnitAsyncStorage.run(\n          finalClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={serverPhasedStream}\n            preinitScripts={() => {}}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={ctx.nonce}\n          />,\n          {\n            signal: finalClientController.signal,\n            onError: (err, errorInfo) => {\n              if (\n                isPrerenderInterruptedError(err) ||\n                finalClientController.signal.aborted\n              ) {\n                requestStore.usedDynamic = true\n\n                const componentStack = errorInfo.componentStack\n                if (typeof componentStack === 'string') {\n                  trackAllowedDynamicAccess(\n                    route,\n                    componentStack,\n                    dynamicValidation,\n                    serverDynamicTracking,\n                    clientDynamicTracking\n                  )\n                }\n                return\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n          }\n        ),\n      () => {\n        finalClientController.abort()\n        serverPhasedStream.assertExhausted()\n      }\n    )\n  } catch (err) {\n    if (\n      isPrerenderInterruptedError(err) ||\n      finalClientController.signal.aborted\n    ) {\n      // we don't have a root because the abort errored in the root. We can just ignore this error\n    } else {\n      // This error is something else and should bubble up\n      throw err\n    }\n  }\n\n  function LogDynamicValidation() {\n    try {\n      throwIfDisallowedDynamic(\n        route,\n        dynamicValidation,\n        serverDynamicTracking,\n        clientDynamicTracking\n      )\n    } catch {}\n    return null\n  }\n\n  resolveValidation(<LogDynamicValidation />)\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { fallbackRouteParams, isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n    return false\n  }\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  implicitTags: Array<string>\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n  const fallbackRouteParams = workStore.fallbackRouteParams\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!renderOpts.experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult:\n    | null\n    | ReactServerPrerenderResult\n    | ServerPrerenderStreamResult = null\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n\n    return res\n  }\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (renderOpts.experimental.dynamicIO) {\n      if (renderOpts.experimental.isRoutePPREnabled) {\n        /**\n         * dynamicIO with PPR\n         *\n         * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n         * Once we have settled all cache reads we restart the render and abort after a single Task.\n         *\n         * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n         * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n         * and a synchronous abort might prevent us from filling all caches.\n         *\n         * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n         * and the reactServerIsDynamic value to determine how to treat the resulting render\n         */\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        // The cacheSignal helps us track whether caches are still filling or we are ready\n        // to cut the render off.\n        const cacheSignal = new CacheSignal()\n\n        // The resume data cache here should use a fresh instance as it's\n        // performing a fresh prerender. If we get to implementing the\n        // prerendering of an already prerendered page, we should use the passed\n        // resume data cache instead.\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          implicitTags: implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const initialServerPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const pendingInitialServerResult = workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          ComponentMod.prerender,\n          initialServerPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (initialServerPrerenderController.signal.aborted) {\n                // The render aborted before this error was handled which indicates\n                // the error is caused by unfinished components within the render\n                return\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            // we don't care to track postpones during the prospective render because we need\n            // to always do a final render anyway\n            onPostpone: undefined,\n            // We don't want to stop rendering until the cacheSignal is complete so we pass\n            // a different signal to this render call than is used by dynamic APIs to signify\n            // transitioning out of the prerender environment\n            signal: initialServerRenderController.signal,\n          }\n        )\n\n        await cacheSignal.cacheReady()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        let initialServerResult\n        try {\n          initialServerResult = await createReactServerPrerenderResult(\n            pendingInitialServerResult\n          )\n        } catch (err) {\n          if (\n            initialServerRenderController.signal.aborted ||\n            initialServerPrerenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerResult) {\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(\n            initialServerResult.asStream(),\n            clientReferenceManifest\n          )\n\n          const initialClientController = new AbortController()\n          const initialClientPrerenderStore: PrerenderStore = {\n            type: 'prerender',\n            phase: 'render',\n            implicitTags: implicitTags,\n            renderSignal: initialClientController.signal,\n            controller: initialClientController,\n            cacheSignal: null,\n            dynamicTracking: null,\n            revalidate: INFINITE_CACHE,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags],\n            prerenderResumeDataCache,\n          }\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          await prerenderAndAbortInSequentialTasks(\n            () =>\n              workUnitAsyncStorage.run(\n                initialClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={initialServerResult.asUnclosingStream()}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  nonce={ctx.nonce}\n                />,\n                {\n                  signal: initialClientController.signal,\n                  onError: (err) => {\n                    const digest = getDigestForWellKnownError(err)\n\n                    if (digest) {\n                      return digest\n                    }\n\n                    if (initialClientController.signal.aborted) {\n                      // These are expected errors that might error the prerender. we ignore them.\n                    } else if (\n                      process.env.NEXT_DEBUG_BUILD ||\n                      process.env.__NEXT_VERBOSE_LOGGING\n                    ) {\n                      // We don't normally log these errors because we are going to retry anyway but\n                      // it can be useful for debugging Next.js itself to get visibility here when needed\n                      printDebugThrownValueForProspectiveRender(\n                        err,\n                        workStore.route\n                      )\n                    }\n                  },\n                  // When debugging the static shell, client-side rendering should be\n                  // disabled to prevent blanking out the page.\n                  bootstrapScripts: renderOpts.isDebugStaticShell\n                    ? []\n                    : [bootstrapScript],\n                }\n              ),\n            () => {\n              initialClientController.abort()\n            }\n          ).catch((err) => {\n            if (\n              initialServerRenderController.signal.aborted ||\n              isPrerenderInterruptedError(err)\n            ) {\n              // These are expected errors that might error the prerender. we ignore them.\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              // We don't normally log these errors because we are going to retry anyway but\n              // it can be useful for debugging Next.js itself to get visibility here when needed\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          })\n        }\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalRenderPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          implicitTags: implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n          finalRenderPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n        const reactServerResult = (reactServerPrerenderResult =\n          await createReactServerPrerenderResult(\n            prerenderAndAbortInSequentialTasks(\n              () =>\n                workUnitAsyncStorage.run(\n                  // The store to scope\n                  finalRenderPrerenderStore,\n                  // The function to run\n                  ComponentMod.prerender,\n                  // ... the arguments for the function to run\n                  finalAttemptRSCPayload,\n                  clientReferenceManifest.clientModules,\n                  {\n                    onError: (err: unknown) => {\n                      if (finalServerController.signal.aborted) {\n                        serverIsDynamic = true\n                        return\n                      }\n\n                      return serverComponentsErrorHandler(err)\n                    },\n                    signal: finalServerController.signal,\n                  }\n                ),\n              () => {\n                finalServerController.abort()\n              }\n            )\n          ))\n\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const finalClientController = new AbortController()\n        const finalClientPrerenderStore: PrerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          implicitTags: implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // For HTML Generation we don't need to track cache reads (RSC only)\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        }\n\n        let clientIsDynamic = false\n        let dynamicValidation = createDynamicValidationState()\n\n        const prerender = require('react-dom/static.edge')\n          .prerender as (typeof import('react-dom/static.edge'))['prerender']\n        let { prelude, postponed } = await prerenderAndAbortInSequentialTasks(\n          () =>\n            workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                nonce={ctx.nonce}\n              />,\n              {\n                signal: finalClientController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientController.signal.aborted\n                  ) {\n                    clientIsDynamic = true\n\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore.route,\n                        componentStack,\n                        dynamicValidation,\n                        serverDynamicTracking,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    setHeader(key, value)\n                  })\n                },\n                maxHeadersLength: renderOpts.reactMaxHeadersLength,\n                // When debugging the static shell, client-side rendering should be\n                // disabled to prevent blanking out the page.\n                bootstrapScripts: renderOpts.isDebugStaticShell\n                  ? []\n                  : [bootstrapScript],\n              }\n            ),\n          () => {\n            finalClientController.abort()\n          }\n        )\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalRenderPrerenderStore,\n          ComponentMod,\n          renderOpts\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          if (postponed != null) {\n            // Dynamic HTML case\n            metadata.postponed = await getDynamicHTMLPostponedState(\n              postponed,\n              fallbackRouteParams,\n              prerenderResumeDataCache\n            )\n          } else {\n            // Dynamic Data case\n            metadata.postponed = await getDynamicDataPostponedState(\n              prerenderResumeDataCache\n            )\n          }\n          reactServerResult.consume()\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueDynamicPrerender(prelude, {\n              getServerInsertedHTML,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: finalRenderPrerenderStore.stale,\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        } else {\n          // Static case\n          if (workStore.forceDynamic) {\n            throw new StaticGenBailoutError(\n              'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n            )\n          }\n\n          let htmlStream = prelude\n          if (postponed != null) {\n            // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n            // so we can set all the postponed boundaries to client render mode before we store the HTML response\n            const resume = require('react-dom/server.edge')\n              .resume as (typeof import('react-dom/server.edge'))['resume']\n\n            // We don't actually want to render anything so we just pass a stream\n            // that never resolves. The resume call is going to abort immediately anyway\n            const foreverStream = new ReadableStream<Uint8Array>()\n\n            const resumeStream = await resume(\n              <App\n                reactServerStream={foreverStream}\n                preinitScripts={() => {}}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                nonce={ctx.nonce}\n              />,\n              JSON.parse(JSON.stringify(postponed)),\n              {\n                signal: createPostponedAbortSignal('static prerender resume'),\n                onError: htmlRendererErrorHandler,\n                nonce: ctx.nonce,\n              }\n            )\n\n            // First we write everything from the prerender, then we write everything from the aborted resume render\n            htmlStream = chainStreams(prelude, resumeStream)\n          }\n\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueStaticPrerender(htmlStream, {\n              inlinedDataStream: createInlinedDataReadableStream(\n                reactServerResult.consumeAsStream(),\n                ctx.nonce,\n                formState\n              ),\n              getServerInsertedHTML,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: finalRenderPrerenderStore.stale,\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        }\n      } else {\n        /**\n         * dynamicIO without PPR\n         *\n         * The general approach is to render the RSC tree first allowing for any inflight\n         * caches to resolve. Once we have settled inflight caches we can check and see if any\n         * synchronous dynamic APIs were used. If so we don't need to bother doing anything more\n         * because the page will be dynamic on re-render anyway\n         *\n         * If no sync dynamic APIs were used we then re-render and abort after a single Task.\n         * If the render errors we know that the page has some dynamic IO. This assumes and relies\n         * upon caches reading from a in process memory cache and resolving in a microtask. While this\n         * is true from our own default cache implementation and if you don't exceed our LRU size it\n         * might not be true for custom cache implementations.\n         *\n         * Future implementations can do some different strategies during build like using IPC to\n         * synchronously fill caches during this special rendering mode. For now this heuristic should work\n         */\n\n        const cache = workStore.incrementalCache\n        if (!cache) {\n          throw new Error(\n            'Expected incremental cache to exist. This is a bug in Next.js'\n          )\n        }\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        const cacheSignal = new CacheSignal()\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          implicitTags: implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const initialClientController = new AbortController()\n        const initialClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          implicitTags: implicitTags,\n          renderSignal: initialClientController.signal,\n          controller: initialClientController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        let initialServerStream\n        try {\n          initialServerStream = workUnitAsyncStorage.run(\n            initialServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            firstAttemptRSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (\n                  initialServerPrerenderController.signal.aborted ||\n                  initialServerRenderController.signal.aborted\n                ) {\n                  // The render aborted before this error was handled which indicates\n                  // the error is caused by unfinished components within the render\n                  return\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              signal: initialServerRenderController.signal,\n            }\n          )\n        } catch (err: unknown) {\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerStream) {\n          const [warmupStream, renderStream] = initialServerStream.tee()\n          initialServerStream = null\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const pendingInitialClientResult = workUnitAsyncStorage.run(\n            initialClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={renderStream}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={ctx.nonce}\n            />,\n            {\n              signal: initialClientController.signal,\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (initialClientController.signal.aborted) {\n                  // These are expected errors that might error the prerender. we ignore them.\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  // We don't normally log these errors because we are going to retry anyway but\n                  // it can be useful for debugging Next.js itself to get visibility here when needed\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              // When debugging the static shell, client-side rendering should be\n              // disabled to prevent blanking out the page.\n              bootstrapScripts: renderOpts.isDebugStaticShell\n                ? []\n                : [bootstrapScript],\n            }\n          )\n          pendingInitialClientResult.catch((err: unknown) => {\n            if (initialClientController.signal.aborted) {\n              // We aborted the render normally and can ignore this error\n            } else {\n              // We're going to retry to so we normally would suppress this error but\n              // when verbose logging is on we print it\n              if (process.env.__NEXT_VERBOSE_LOGGING) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            }\n          })\n        }\n\n        await cacheSignal.cacheReady()\n        // It is important that we abort the SSR render first to avoid\n        // connection closed errors from having an incomplete RSC stream\n        initialClientController.abort()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        // We've now filled caches and triggered any inadvertant sync bailouts\n        // due to lazy module initialization. We can restart our render to capture results\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          implicitTags: implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        let clientIsDynamic = false\n        const finalClientController = new AbortController()\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const dynamicValidation = createDynamicValidationState()\n\n        const finalClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          implicitTags: implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const finalServerPayload = await workUnitAsyncStorage.run(\n          finalServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const serverPrerenderStreamResult = (reactServerPrerenderResult =\n          await prerenderServerWithPhases(\n            finalServerController.signal,\n            () =>\n              workUnitAsyncStorage.run(\n                finalServerPrerenderStore,\n                ComponentMod.renderToReadableStream,\n                finalServerPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  onError: (err: unknown) => {\n                    if (finalServerController.signal.aborted) {\n                      serverIsDynamic = true\n                      if (isPrerenderInterruptedError(err)) {\n                        return err.digest\n                      }\n                      return getDigestForWellKnownError(err)\n                    }\n\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerController.signal,\n                }\n              ),\n            () => {\n              finalServerController.abort()\n            }\n          ))\n\n        let htmlStream\n        const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n        try {\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const result = await prerenderClientWithPhases(\n            () =>\n              workUnitAsyncStorage.run(\n                finalClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={serverPhasedStream}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  nonce={ctx.nonce}\n                />,\n                {\n                  signal: finalClientController.signal,\n                  onError: (err: unknown, errorInfo: ErrorInfo) => {\n                    if (\n                      isPrerenderInterruptedError(err) ||\n                      finalClientController.signal.aborted\n                    ) {\n                      clientIsDynamic = true\n\n                      const componentStack: string | undefined = (\n                        errorInfo as any\n                      ).componentStack\n                      if (typeof componentStack === 'string') {\n                        trackAllowedDynamicAccess(\n                          workStore.route,\n                          componentStack,\n                          dynamicValidation,\n                          serverDynamicTracking,\n                          clientDynamicTracking\n                        )\n                      }\n                      return\n                    }\n\n                    return htmlRendererErrorHandler(err, errorInfo)\n                  },\n                  // When debugging the static shell, client-side rendering should be\n                  // disabled to prevent blanking out the page.\n                  bootstrapScripts: renderOpts.isDebugStaticShell\n                    ? []\n                    : [bootstrapScript],\n                }\n              ),\n            () => {\n              finalClientController.abort()\n              serverPhasedStream.assertExhausted()\n            }\n          )\n          htmlStream = result.prelude\n        } catch (err) {\n          if (\n            isPrerenderInterruptedError(err) ||\n            finalClientController.signal.aborted\n          ) {\n            // we don't have a root because the abort errored in the root. We can just ignore this error\n          } else {\n            // This error is something else and should bubble up\n            throw err\n          }\n        }\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          const dynamicReason = serverIsDynamic\n            ? getFirstDynamicReason(serverDynamicTracking)\n            : getFirstDynamicReason(clientDynamicTracking)\n          if (dynamicReason) {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          } else {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically it accessed data without explicitly caching it. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          }\n        }\n\n        const flightData = await streamToBuffer(\n          serverPrerenderStreamResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalClientPrerenderStore,\n          ComponentMod,\n          renderOpts\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        const validateRootLayout = renderOpts.dev\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueFizzStream(htmlStream!, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              serverPrerenderStreamResult.asStream(),\n              ctx.nonce,\n              formState\n            ),\n            isStaticGeneration: true,\n            getServerInsertedHTML,\n            serverInsertedHTMLToHead: true,\n            validateRootLayout,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: finalServerPrerenderStore.stale,\n          collectedTags: finalServerPrerenderStore.tags,\n        }\n      }\n    } else if (renderOpts.experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(\n        renderOpts.isDebugDynamicAccesses\n      )\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        implicitTags: implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        implicitTags: implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n        prerenderResumeDataCache,\n      }\n      const prerender = require('react-dom/static.edge')\n        .prerender as (typeof import('react-dom/static.edge'))['prerender']\n      const { prelude, postponed } = await workUnitAsyncStorage.run(\n        ssrPrerenderStore,\n        prerender,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          nonce={ctx.nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          onHeaders: (headers: Headers) => {\n            headers.forEach((value, key) => {\n              setHeader(key, value)\n            })\n          },\n          maxHeadersLength: renderOpts.reactMaxHeadersLength,\n          // When debugging the static shell, client-side rendering should be\n          // disabled to prevent blanking out the page.\n          bootstrapScripts: renderOpts.isDebugStaticShell\n            ? []\n            : [bootstrapScript],\n        }\n      )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = require('react-dom/server.edge')\n            .resume as (typeof import('react-dom/server.edge'))['resume']\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={ctx.nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce: ctx.nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              ctx.nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        implicitTags: implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = require('react-dom/server.edge')\n        .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          nonce={ctx.nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce: ctx.nonce,\n          // When debugging the static shell, client-side rendering should be\n          // disabled to prevent blanking out the page.\n          bootstrapScripts: renderOpts.isDebugStaticShell\n            ? []\n            : [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            ctx.nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML,\n          serverInsertedHTMLToHead: true,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: prerenderLegacyStore.stale,\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      implicitTags: implicitTags,\n      revalidate: INFINITE_CACHE,\n      expire: INFINITE_CACHE,\n      stale: INFINITE_CACHE,\n      tags: [...implicitTags],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      const fizzStream = await renderToInitialFizzStream({\n        ReactDOMServer: require('react-dom/server.edge'),\n        element: (\n          <AppWithoutContext\n            reactServerStream={errorServerStream}\n            preinitScripts={errorPreinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            nonce={ctx.nonce}\n          />\n        ),\n        streamOptions: {\n          nonce: ctx.nonce,\n          // Include hydration scripts in the HTML\n          bootstrapScripts: [errorBootstrapScript],\n          formState,\n        },\n      })\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const validateRootLayout = renderOpts.dev\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream =\n        reactServerPrerenderResult instanceof ServerPrerenderStreamResult\n          ? reactServerPrerenderResult.asStream()\n          : reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            ctx.nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath: renderOpts.basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          serverInsertedHTMLToHead: true,\n          validateRootLayout,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale:\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE,\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst loadingChunks: Set<Promise<unknown>> = new Set()\nconst chunkListeners: Array<(x?: unknown) => void> = []\n\nfunction trackChunkLoading(load: Promise<unknown>) {\n  loadingChunks.add(load)\n  load.finally(() => {\n    if (loadingChunks.has(load)) {\n      loadingChunks.delete(load)\n      if (loadingChunks.size === 0) {\n        // We are not currently loading any chunks. We can notify all listeners\n        for (let i = 0; i < chunkListeners.length; i++) {\n          chunkListeners[i]()\n        }\n        chunkListeners.length = 0\n      }\n    }\n  })\n}\n\nexport async function warmFlightResponse(\n  flightStream: ReadableStream<Uint8Array>,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n) {\n  let createFromReadableStream\n  if (process.env.TURBOPACK) {\n    createFromReadableStream =\n      // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-turbopack/client.edge').createFromReadableStream\n  } else {\n    createFromReadableStream =\n      // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge').createFromReadableStream\n  }\n\n  try {\n    createFromReadableStream(flightStream, {\n      serverConsumerManifest: {\n        moduleLoading: clientReferenceManifest.moduleLoading,\n        moduleMap: clientReferenceManifest.ssrModuleMapping,\n        serverModuleMap: null,\n      },\n    })\n  } catch {\n    // We don't want to handle errors here but we don't want it to\n    // interrupt the outer flow. We simply ignore it here and expect\n    // it will bubble up during a render\n  }\n\n  // We'll wait at least one task and then if no chunks have started to load\n  // we'll we can infer that there are none to load from this flight response\n  trackChunkLoading(waitAtLeastOneReactRenderTask())\n  return new Promise((r) => {\n    chunkListeners.push(r)\n  })\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<React.ReactNode | undefined> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n\n  return globalErrorStyles\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    renderOpts.experimental.isRoutePPREnabled !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: null,\n  }\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest\n  )\n}\n"], "names": ["renderToHTMLOrFlight", "warmFlightResponse", "flightDataPathHeadKey", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "toLowerCase", "undefined", "isHmrRefresh", "NEXT_HMR_REFRESH_HEADER", "isRSCRequest", "RSC_HEADER", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "parseAndValidateFlightRouterState", "NEXT_ROUTER_STATE_TREE_HEADER", "csp", "nonce", "getScriptNonceFromHeader", "createNotFoundLoaderTree", "loaderTree", "components", "children", "PAGE_SEGMENT_KEY", "page", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "getSegmentParam", "key", "param", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "type", "isOptionalCatchall", "dynamicParamType", "dynamicParamTypes", "treeSegment", "split", "slice", "flatMap", "pathSegment", "parseParameter", "join", "getShortDynamicParamType", "NonIndex", "ctx", "is404Page", "isInvalidStatusCode", "res", "statusCode", "meta", "name", "content", "generateDynamicRSCPayload", "flightData", "componentMod", "tree", "createServerSearchParamsForMetadata", "createServerParamsForMetadata", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "requestId", "workStore", "url", "skipFlight", "preloadCallbacks", "searchParams", "MetadataTree", "getMetadataReady", "metadataContext", "createTrackedMetadataContext", "pathname", "renderOpts", "walkTreeWithFlightRouterState", "createSegmentPath", "child", "loaderTreeToFilter", "parentParams", "<PERSON><PERSON><PERSON><PERSON>", "rscPayloadHead", "React", "Fragment", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "path", "actionResult", "a", "f", "b", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "isAction", "revalidateReason", "getRevalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "createFlightReactServerErrorHandler", "dev", "RSCPayload", "workUnitAsyncStorage", "run", "process", "env", "NODE_ENV", "experimental", "dynamicIO", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "spawnDynamicValidationInDev", "clientReferenceManifest", "route", "catch", "flightReadableStream", "renderToReadableStream", "clientModules", "temporaryReferences", "FlightRenderResult", "fetchMetrics", "warmupDevRender", "InvariantError", "prerenderResumeDataCache", "createPrerenderResumeDataCache", "renderController", "AbortController", "prerenderController", "cacheSignal", "CacheSignal", "prerenderStore", "phase", "implicitTags", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "INFINITE_CACHE", "expire", "stale", "tags", "rscPayload", "cacheReady", "abort", "devRenderResumeDataCache", "createRenderResumeDataCache", "prepareInitialCanonicalUrl", "search", "getRSCPayload", "is404", "missingSlots", "GlobalError", "initialTree", "createFlightRouterStateFromLoaderTree", "errorType", "seedData", "createComponentTree", "firstItem", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "NEXT_URL", "initialHead", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "createMetadataContext", "initialSeedData", "html", "id", "head", "body", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "response", "use", "useFlightStream", "initialState", "createInitialRouterState", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "location", "prerendered", "actionQueue", "createMutableActionQueue", "HeadManagerContext", "require", "Provider", "appDir", "AppRouter", "globalErrorComponentAndStyles", "AppWithoutContext", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "requestEndedState", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "ComponentMod", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "wrapClientComponentLoader", "globalThis", "__next_require__", "__next_chunk_load__", "args", "loadingChunk", "loadChunk", "trackChunkLoading", "URL", "setAppIsrStatus", "NEXT_RUNTIME", "isNodeNextRequest", "originalRequest", "on", "ended", "metrics", "getClientComponentLoaderMetrics", "reset", "getTracer", "startSpan", "NextNodeServerSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "metadata", "appUsingSizeAdjust", "serverModuleMap", "createServerModuleMap", "setReferenceManifestsSingleton", "patchFetch", "taintObjectReference", "stripInternalQueries", "crypto", "randomUUID", "nanoid", "isActionRequest", "getServerActionRequestMetadata", "isServerAction", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "AppRenderSpan", "getBodyResult", "spanName", "prerenderToStream", "dynamicAccess", "accessedDynamicData", "isDebugDynamicAccesses", "warn", "access", "formatDynamicAPIAccesses", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "isUserLandError", "pendingRevalidates", "pendingRevalidateWrites", "revalidatedTags", "waitUntil", "Promise", "all", "incrementalCache", "revalidateTag", "Object", "collectedTags", "fetchTags", "staleHeader", "String", "collectedStale", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STALE_TIME_HEADER", "forceStatic", "collectedRevalidate", "staticBailoutInfo", "description", "dynamicUsageDescription", "stack", "dynamicUsageStack", "RenderResult", "streamToString", "stream", "renderResumeDataCache", "createRequestStoreForRender", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "handleAction", "generateFlight", "notFoundLoaderTree", "result", "assignMetadata", "Error", "parseRelativeUrl", "parsePostponedState", "getImplicitTags", "routeModule", "definition", "createWorkStore", "workAsyncStorage", "renderServerInsertedHTML", "createServerInsertedHTML", "tracingMetadata", "getTracedMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "buildManifest", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "getAssetQueryString", "integrity", "subresourceIntegrityManifest", "crossOrigin", "noModule", "bootstrapScript", "getRequiredScripts", "reactServerErrorsByDigest", "Map", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "createHTMLReactServerErrorHandler", "nextExport", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "createHTMLErrorHandler", "reactServerResult", "bind", "scheduleInSequentialTasks", "prerenderPhase", "environmentName", "filterStackFrame", "_functionName", "startsWith", "ReactServerResult", "waitAtLeastOneReactRenderTask", "DynamicState", "DATA", "inlinedReactServerDataStream", "createInlinedDataReadableStream", "tee", "chainStreams", "createDocumentClosingStream", "getPostponedFromState", "resume", "htmlStream", "getServerInsertedHTML", "makeGetServerInsertedHTML", "serverCapturedErrors", "basePath", "continueDynamicHTMLResume", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactMaxHeadersLength", "bootstrapScripts", "isDebugStaticShell", "generateStaticHTML", "supportsDynamicResponse", "validateRootLayout", "continueFizzStream", "serverInsertedHTMLToHead", "isStaticGenBailoutError", "message", "shouldBailoutToCSR", "isBailoutToCSRError", "getStackWithoutErrorMessage", "error", "reason", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isRedirectError", "getRedirectStatusCodeFromError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "Headers", "appendMutableCookies", "mutableCookies", "from", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "errorServerStream", "fizzStream", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "resolve", "isNotFound", "initialServerPrerenderController", "initialServerRenderController", "initialServerPrerenderStore", "initialClientController", "initialClientPrerenderStore", "firstAttemptRSCPayload", "initialServerStream", "digest", "getDigestForWellKnownError", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "warmupStream", "renderStream", "prerender", "pendingInitialClientResult", "finalServerController", "serverDynamicTracking", "createDynamicTrackingState", "finalServerPrerenderStore", "finalClientController", "clientDynamicTracking", "dynamicValidation", "createDynamicValidationState", "finalClientPrerenderStore", "finalServerPayload", "serverPrerenderStreamResult", "prerenderServerWithPhases", "isPrerenderInterruptedError", "serverPhasedStream", "asPhasedStream", "prerenderClientWithPhases", "errorInfo", "componentStack", "trackAllowedDynamicAccess", "assertExhausted", "LogDynamicValidation", "throwIfDisallowedDynamic", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "initialServerPayload", "pendingInitialServerResult", "onPostpone", "initialServerResult", "createReactServerPrerenderResult", "asStream", "prerenderAndAbortInSequentialTasks", "asUnclosingStream", "serverIsDynamic", "finalRenderPrerenderStore", "finalAttemptRSCPayload", "clientIsDynamic", "prelude", "streamToBuffer", "segmentData", "collectSegmentData", "getDynamicHTMLPostponedState", "getDynamicDataPostponedState", "continueDynamicPrerender", "consumeDynamicAccess", "collectedExpire", "StaticGenBailoutError", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "createPostponedAbortSignal", "continueStaticP<PERSON><PERSON>", "consumeAsStream", "cache", "dynamicReason", "getFirstDynamicReason", "DynamicServerError", "reactServerPrerenderStore", "createReactServerPrerenderResultFromRender", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "isDynamicServerError", "flightStream", "ServerPrerenderStreamResult", "loadingChunks", "chunkListeners", "load", "add", "finally", "delete", "createFromReadableStream", "TURBOPACK", "serverConsumerManifest", "moduleLoading", "moduleMap", "ssrModuleMapping", "r", "push", "modules", "globalErrorModule", "parseLoaderTree", "styles", "createComponentStylesAndScripts", "filePath", "getComponent", "fullPageDataBuffer", "isEdgeRuntime", "edgeRscModuleMapping", "rscModuleMapping", "staleTime"], "mappings": ";;;;;;;;;;;;;;;IAo7CaA,oBAAoB;eAApBA;;IAw3ESC,kBAAkB;eAAlBA;;;;0CA3xHf;8DAayC;qEAKzC;sCAWA;+BAC8B;kCAQ9B;iCAIA;8BACqC;2BACZ;oCAKzB;0BAIA;+BACyB;8BACA;2BACkB;wBACxB;oCACS;oCAQ5B;0CAIA;iCACyB;0CACS;mDACS;uDACI;+BACzB;8BACO;qBACR;gCACS;oCACI;iCACN;+BACL;2CACY;+CACI;qCACV;qCACA;iCACW;gCAKxC;oCAM8B;mCAI9B;yCAIA;mCACqC;kCAarC;+CAIA;6BAC+B;yBACJ;4BACH;kCACE;kEACX;yCAGyB;0CACN;6BACA;uBACL;yBACH;yCAGW;wCAUc;sCAChB;2BACI;8CAIvC;6BACqB;wBACM;gCACH;QAExB;4BACwB;iDACiB;iCAChB;iCAIzB;;;;;;AA0CP,MAAMC,wBAAwB;AAiB9B,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,MAAMC,oBACJF,sBACAF,OAAO,CAACK,6CAA2B,CAACC,WAAW,GAAG,KAAKC;IAEzD,MAAMC,eACJR,OAAO,CAACS,yCAAuB,CAACH,WAAW,GAAG,KAAKC;IAErD,2DAA2D;IAC3D,MAAMG,eACJR,sBAAsBF,OAAO,CAACW,4BAAU,CAACL,WAAW,GAAG,KAAKC;IAE9D,MAAMK,iCACJF,gBAAiB,CAAA,CAACN,qBAAqB,CAACH,QAAQY,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBG,IAAAA,oEAAiC,EAC/Bf,OAAO,CAACgB,+CAA6B,CAACV,WAAW,GAAG,IAEtDC;IAEJ,MAAMU,MACJjB,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMkB,QACJ,OAAOD,QAAQ,WAAWE,IAAAA,kDAAwB,EAACF,OAAOV;IAE5D,OAAO;QACLO;QACAV;QACAI;QACAE;QACAR;QACAgB;IACF;AACF;AAEA,SAASE,yBAAyBC,UAAsB;IACtD,mEAAmE;IACnE,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,OAAO;QACL;QACA;YACEE,UAAU;gBACRC,yBAAgB;gBAChB,CAAC;gBACD;oBACEC,MAAMH,UAAU,CAAC,YAAY;gBAC/B;aACD;QACH;QACAA;KACD;AACH;AAIA;;CAEC,GACD,SAASI,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAeC,IAAAA,gCAAe,EAACF;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QAEA,MAAME,MAAMF,aAAaG,KAAK;QAE9B,IAAIC,QAAQT,MAAM,CAACO,IAAI;QAEvB,IAAIL,uBAAuBA,oBAAoBQ,GAAG,CAACL,aAAaG,KAAK,GAAG;YACtEC,QAAQP,oBAAoBS,GAAG,CAACN,aAAaG,KAAK;QACpD,OAAO,IAAII,MAAMC,OAAO,CAACJ,QAAQ;YAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;QAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;YACpCA,QAAQO,mBAAmBP;QAC7B;QAEA,IAAI,CAACA,OAAO;YACV,MAAMQ,aAAaZ,aAAaa,IAAI,KAAK;YACzC,MAAMC,qBAAqBd,aAAaa,IAAI,KAAK;YAEjD,IAAID,cAAcE,oBAAoB;gBACpC,MAAMC,mBAAmBC,2CAAiB,CAAChB,aAAaa,IAAI,CAAC;gBAC7D,oEAAoE;gBACpE,6DAA6D;gBAC7D,IAAIC,oBAAoB;oBACtB,OAAO;wBACLX,OAAOD;wBACPE,OAAO;wBACPS,MAAME;wBACNE,aAAa;4BAACf;4BAAK;4BAAIa;yBAAiB;oBAC1C;gBACF;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxFX,QAAQR,SACLsB,KAAK,CAAC,IACP,gCAAgC;iBAC/BC,KAAK,CAAC,EACP,oDAAoD;iBACnDC,OAAO,CAAC,CAACC;oBACR,MAAMlB,QAAQmB,IAAAA,0BAAc,EAACD;oBAC7B,yDAAyD;oBACzD,wDAAwD;oBACxD,OAAO1B,MAAM,CAACQ,MAAMD,GAAG,CAAC,IAAIC,MAAMD,GAAG;gBACvC;gBAEF,OAAO;oBACLC,OAAOD;oBACPE;oBACAS,MAAME;oBACN,wCAAwC;oBACxCE,aAAa;wBAACf;wBAAKE,MAAMmB,IAAI,CAAC;wBAAMR;qBAAiB;gBACvD;YACF;QACF;QAEA,MAAMF,OAAOW,IAAAA,kDAAwB,EAACxB,aAAaa,IAAI;QAEvD,OAAO;YACLV,OAAOD;YACP,yCAAyC;YACzCE,OAAOA;YACP,iDAAiD;YACjDa,aAAa;gBAACf;gBAAKK,MAAMC,OAAO,CAACJ,SAASA,MAAMmB,IAAI,CAAC,OAAOnB;gBAAOS;aAAK;YACxEA,MAAMA;QACR;IACF;AACF;AAEA,SAASY,SAAS,EAAEC,GAAG,EAA6B;IAClD,MAAMC,YAAYD,IAAI9B,QAAQ,KAAK;IACnC,MAAMgC,sBACJ,OAAOF,IAAIG,GAAG,CAACC,UAAU,KAAK,YAAYJ,IAAIG,GAAG,CAACC,UAAU,GAAG;IAEjE,IAAIH,aAAaC,qBAAqB;QACpC,qBAAO,qBAACG;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbR,GAAqB,EACrBzD,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAIkE,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAMhD,UAAU,EAChBiD,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACD5C,0BAA0B,EAC1B6C,sBAAsB,EACtBC,KAAK,EACLC,SAAS,EACT/D,iBAAiB,EACjBgE,SAAS,EACTC,GAAG,EACJ,GAAGrB;IAEJ,IAAI,EAACzD,2BAAAA,QAAS+E,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAMC,eAAeZ,oCAAoCM,OAAOE;QAChE,MAAM,CAACK,cAAcC,iBAAiB,GAAGZ,yBAAyB;YAChEH,MAAMhD;YACN6D;YACAG,iBAAiBC,IAAAA,6CAA4B,EAC3CP,IAAIQ,QAAQ,EACZ7B,IAAI8B,UAAU,EACdV;YAEFhD;YACA6C;YACAJ;YACAO;YACAL;YACAC;QACF;QACAP,aAAa,AACX,CAAA,MAAMsB,IAAAA,4DAA6B,EAAC;YAClC/B;YACAgC,mBAAmB,CAACC,QAAUA;YAC9BC,oBAAoBvE;YACpBwE,cAAc,CAAC;YACf/E;YACAgF,SAAS;YACT,+CAA+C;YAC/CC,8BACE,sBAACC,cAAK,CAACC,QAAQ;;kCACb,qBAACxC;wBAASC,KAAKA;;kCAEf,qBAACyB,kBAAkBN;;eAHA/E;YAMvBoG,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBlB;YACAH;QACF,EAAC,EACDxC,GAAG,CAAC,CAAC8D,OAASA,KAAKpD,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAIlD,2BAAAA,QAASuG,YAAY,EAAE;QACzB,OAAO;YACLC,GAAGxG,QAAQuG,YAAY;YACvBE,GAAGvC;YACHwC,GAAGjD,IAAI8B,UAAU,CAACoB,OAAO;QAC3B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLD,GAAGjD,IAAI8B,UAAU,CAACoB,OAAO;QACzBF,GAAGvC;QACH0C,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACPrD,GAAqB,EACrBsD,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAWxD,IAAI9B,QAAQ;QACvBuF,WAAWzD,IAAI0D,QAAQ,GAAG,WAAW;QACrCJ;QACAK,kBAAkBC,IAAAA,0BAAmB,EAAC5D,IAAIoB,SAAS;IACrD;AACF;AACA;;;CAGC,GACD,eAAeyC,kCACbC,GAAoB,EACpB9D,GAAqB,EACrB+D,YAA0B,EAC1BxH,OAMC;IAED,MAAMuF,aAAa9B,IAAI8B,UAAU;IAEjC,SAASkC,wBAAwBC,GAAkB;QACjD,OAAOnC,WAAWoC,6BAA6B,oBAAxCpC,WAAWoC,6BAA6B,MAAxCpC,YACLmC,KACAH,KACAT,mBAAmBrD,KAAK;IAE5B;IACA,MAAMmE,UAAUC,IAAAA,uDAAmC,EACjD,CAAC,CAACtC,WAAWuC,GAAG,EAChBL;IAGF,MAAMM,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACAvD,2BACAR,KACAzD;IAGF,IACE,qDAAqD;IACrDuF,WAAWuC,GAAG,IACd,uEAAuE;IACvEI,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,yEAAyE;IACzE7C,WAAW8C,YAAY,CAACC,SAAS,EACjC;QACA,MAAM,CAACC,mBAAmBC,iBAAiB,GAAGC;QAC9CV,WAAWW,WAAW,GAAGF;QAEzBG,4BACEJ,mBACA9E,IAAIU,YAAY,CAACC,IAAI,EACrBX,KACA,OACAA,IAAImF,uBAAuB,EAC3BnF,IAAIoB,SAAS,CAACgE,KAAK,EACnBrB,cACAsB,KAAK,CAACP,mBAAmB,2DAA2D;;IACxF;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMQ,uBAAuBf,kDAAoB,CAACC,GAAG,CACnDT,cACA/D,IAAIU,YAAY,CAAC6E,sBAAsB,EACvCjB,YACAtE,IAAImF,uBAAuB,CAACK,aAAa,EACzC;QACErB;QACAsB,mBAAmB,EAAElJ,2BAAAA,QAASkJ,mBAAmB;IACnD;IAGF,OAAO,IAAIC,sCAAkB,CAACJ,sBAAsB;QAClDK,cAAc3F,IAAIoB,SAAS,CAACuE,YAAY;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,eAAeC,gBACb9B,GAAoB,EACpB9D,GAAqB;IAErB,MAAM8B,aAAa9B,IAAI8B,UAAU;IACjC,IAAI,CAACA,WAAWuC,GAAG,EAAE;QACnB,MAAM,IAAIwB,8BAAc,CACtB;IAEJ;IAEA,SAAS7B,wBAAwBC,GAAkB;QACjD,OAAOnC,WAAWoC,6BAA6B,oBAAxCpC,WAAWoC,6BAA6B,MAAxCpC,YACLmC,KACAH,KACAT,mBAAmBrD,KAAK;IAE5B;IACA,MAAMmE,UAAUC,IAAAA,uDAAmC,EACjD,MACAJ;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAM8B,2BAA2BC,IAAAA,+CAA8B;IAE/D,MAAMC,mBAAmB,IAAIC;IAC7B,MAAMC,sBAAsB,IAAID;IAChC,MAAME,cAAc,IAAIC,wBAAW;IACnC,MAAMC,iBAAiC;QACrClH,MAAM;QACNmH,OAAO;QACPC,cAAc,EAAE;QAChBC,cAAcR,iBAAiBS,MAAM;QACrCC,YAAYR;QACZC;QACAQ,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMmB,aAAa,MAAM1C,kDAAoB,CAACC,GAAG,CAC/C6B,gBACA7F,2BACAR;IAGF,0FAA0F;IAC1F,mCAAmC;IACnCuE,kDAAoB,CAACC,GAAG,CACtB6B,gBACArG,IAAIU,YAAY,CAAC6E,sBAAsB,EACvC0B,YACAjH,IAAImF,uBAAuB,CAACK,aAAa,EACzC;QACErB;QACAsC,QAAQT,iBAAiBS,MAAM;IACjC;IAGF,6CAA6C;IAC7C,MAAMN,YAAYe,UAAU;IAC5B,uFAAuF;IACvFb,eAAeP,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBE,iBAAiBmB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAIzB,sCAAkB,CAAC,IAAI;QAChCC,cAAc3F,IAAIoB,SAAS,CAACuE,YAAY;QACxCyB,0BAA0BC,IAAAA,4CAA2B,EACnDvB;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAASwB,2BAA2BjG,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIQ,QAAQ,GAAGR,IAAIkG,MAAM,AAAD,EAAG/H,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAegI,cACb7G,IAAgB,EAChBX,GAAqB,EACrByH,KAAc;IAEd,MAAMjF,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIiF;IAEJ,sDAAsD;IACtD,IAAIjD,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C+C,eAAe,IAAIjF;IACrB;IAEA,MAAM,EACJrE,0BAA0B,EAC1B8C,KAAK,EACLD,sBAAsB,EACtBP,cAAc,EACZiH,WAAW,EACX/G,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDK,GAAG,EACHD,SAAS,EACV,GAAGpB;IAEJ,MAAM4H,cAAcC,IAAAA,4EAAqC,EACvDlH,MACAvC,4BACA8C;IAGF,MAAMM,eAAeZ,oCAAoCM,OAAOE;IAChE,MAAM,CAACK,cAAcC,iBAAiB,GAAGZ,yBAAyB;QAChEH;QACAmH,WAAWL,QAAQ,cAAc5K;QACjC2E;QACAG,iBAAiBC,IAAAA,6CAA4B,EAC3CP,IAAIQ,QAAQ,EACZ7B,IAAI8B,UAAU,EACdV;QAEFhD;QACA6C;QACAJ;QACAO;QACAL;QACAC;IACF;IAEA,MAAMO,mBAAqC,EAAE;IAE7C,MAAMwG,WAAW,MAAMC,IAAAA,wCAAmB,EAAC;QACzChI;QACAgC,mBAAmB,CAACC,QAAUA;QAC9BtE,YAAYgD;QACZwB,cAAc,CAAC;QACf8F,WAAW;QACXzF;QACAE;QACAC;QACAC,oBAAoB;QACpBlB;QACAgG;QACAnG;QACA2G,gBAAgBlI,IAAI8B,UAAU,CAAC8C,YAAY,CAACsD,cAAc;IAC5D;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAMC,aAAanI,IAAIG,GAAG,CAACiI,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACC,0BAAQ;IAEhE,MAAMC,4BACJ,sBAAClG,cAAK,CAACC,QAAQ;;0BACb,qBAACxC;gBAASC,KAAKA;;0BAEf,qBAACyB,kBAAkBzB,IAAImB,SAAS;;OAHb/E;IAOvB,MAAMqM,oBAAoB,MAAMC,qBAAqB/H,MAAMX;IAE3D,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAM2I,wBACJvH,UAAUgC,kBAAkB,IAC5BpD,IAAI8B,UAAU,CAAC8C,YAAY,CAACzH,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FyL,iBAAG,qBAACC;YAAStH,kBAAkBA;;QAC/B0B,GAAGjD,IAAI8B,UAAU,CAACoB,OAAO;QACzB4F,GAAG9I,IAAI+I,WAAW;QAClBC,GAAG1B,2BAA2BjG;QAC9BrC,GAAG,CAAC,CAACqJ;QACLrF,GAAG;YACD;gBACE4E;gBACAG;gBACAS;gBACAG;aACD;SACF;QACDM,GAAGvB;QACHwB,GAAG;YAACvB;YAAac;SAAkB;QACnCU,GAAG,OAAOnJ,IAAI8B,UAAU,CAACsH,SAAS,KAAK;QACvCjG,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAASyF,SAAS,EAAEtH,gBAAgB,EAAoC;IACtEA,iBAAiB8H,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb5I,IAAgB,EAChBX,GAAqB,EACrB8H,SAAqD;IAErD,MAAM,EACJ1J,0BAA0B,EAC1B8C,KAAK,EACLD,sBAAsB,EACtBP,cAAc,EACZiH,WAAW,EACX/G,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDK,GAAG,EACHF,SAAS,EACTC,SAAS,EACV,GAAGpB;IAEJ,MAAMwB,eAAeZ,oCAAoCM,OAAOE;IAChE,MAAM,CAACK,aAAa,GAAGX,yBAAyB;QAC9CH;QACAa;QACA,yEAAyE;QACzE,iCAAiC;QACjCG,iBAAiB6H,IAAAA,sCAAqB,EAACnI,IAAIQ,QAAQ,EAAE7B,IAAI8B,UAAU;QACnEgG;QACA1J;QACA6C;QACAJ;QACAO;QACAL;QACAC;IACF;IAEA,MAAMwH,4BACJ,sBAAClG,cAAK,CAACC,QAAQ;;0BACb,qBAACxC;gBAASC,KAAKA;;0BAEf,qBAACyB,kBAAkBN;YAClBsD,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACtE;gBAAKC,MAAK;gBAAaC,SAAQ;;;OALfnE;IAUvB,MAAMwL,cAAcC,IAAAA,4EAAqC,EACvDlH,MACAvC,4BACA8C;IAGF,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMuI,kBAAqC;QACzC7B,WAAW,CAAC,EAAE;sBACd,sBAAC8B;YAAKC,IAAG;;8BACP,qBAACC;8BACD,qBAACC;;;QAEH,CAAC;QACD;QACA;KACD;IAED,MAAMpB,oBAAoB,MAAMC,qBAAqB/H,MAAMX;IAE3D,MAAM2I,wBACJvH,UAAUgC,kBAAkB,IAC5BpD,IAAI8B,UAAU,CAAC8C,YAAY,CAACzH,iBAAiB,KAAK;IAEpD,OAAO;QACL8F,GAAGjD,IAAI8B,UAAU,CAACoB,OAAO;QACzB4F,GAAG9I,IAAI+I,WAAW;QAClBC,GAAG1B,2BAA2BjG;QAC9B4H,GAAGpM;QACHmC,GAAG;QACHgE,GAAG;YACD;gBACE4E;gBACA6B;gBACAjB;gBACAG;aACD;SACF;QACDO,GAAG;YAACvB;YAAac;SAAkB;QACnCU,GAAG,OAAOnJ,IAAI8B,UAAU,CAACsH,SAAS,KAAK;QACvCjG,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA,mFAAmF;AACnF,SAAS0G,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACd7E,uBAAuB,EACvB3H,KAAK,EACLyM,0BAA0B,EAO3B;IACCD;IACA,MAAME,WAAW5H,cAAK,CAAC6H,GAAG,CACxBC,IAAAA,kCAAe,EACbL,mBACA5E,yBACA3H;IAIJ,MAAM6M,eAAeC,IAAAA,kDAAwB,EAAC;QAC5CC,mBAAmBL,SAASlH,CAAC;QAC7BwH,0BAA0BN,SAASlB,CAAC;QACpC,2EAA2E;QAC3E,kFAAkF;QAClFyB,uBAAuB;QACvBC,UAAU;QACVrC,oBAAoB6B,SAASlL,CAAC;QAC9BoK,WAAWc,SAASf,CAAC;QACrBwB,aAAaT,SAAS/G,CAAC;IACzB;IAEA,MAAMyH,cAAcC,IAAAA,qCAAwB,EAACR;IAE7C,MAAM,EAAES,kBAAkB,EAAE,GAC1BC,QAAQ;IAEV,qBACE,qBAACD,mBAAmBE,QAAQ;QAC1BtM,OAAO;YACLuM,QAAQ;YACRzN;QACF;kBAEA,cAAA,qBAACyM;sBACC,cAAA,qBAACiB,kBAAS;gBACRN,aAAaA;gBACbO,+BAA+BjB,SAAShB,CAAC;gBACzCH,aAAamB,SAASpB,CAAC;;;;AAKjC;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAASsC,kBAAqB,EAC5BrB,iBAAiB,EACjBC,cAAc,EACd7E,uBAAuB,EACvB3H,KAAK,EAMN;IACCwM;IACA,MAAME,WAAW5H,cAAK,CAAC6H,GAAG,CACxBC,IAAAA,kCAAe,EACbL,mBACA5E,yBACA3H;IAIJ,MAAM6M,eAAeC,IAAAA,kDAAwB,EAAC;QAC5CC,mBAAmBL,SAASlH,CAAC;QAC7BwH,0BAA0BN,SAASlB,CAAC;QACpC,2EAA2E;QAC3E,kFAAkF;QAClFyB,uBAAuB;QACvBC,UAAU;QACVrC,oBAAoB6B,SAASlL,CAAC;QAC9BoK,WAAWc,SAASf,CAAC;QACrBwB,aAAaT,SAAS/G,CAAC;IACzB;IAEA,MAAMyH,cAAcC,IAAAA,qCAAwB,EAACR;IAE7C,qBACE,qBAACa,kBAAS;QACRN,aAAaA;QACbO,+BAA+BjB,SAAShB,CAAC;QACzCH,aAAamB,SAASpB,CAAC;;AAG7B;AASA,eAAeuC,yBACbvH,GAAoB,EACpB3D,GAAqB,EACrBkB,GAAwC,EACxCnD,QAAgB,EAChBgD,KAAyB,EACzBY,UAAsB,EACtBV,SAAoB,EACpBkK,oBAA0C,EAC1CC,iBAAsC,EACtCC,cAAqC,EACrCjF,YAA2B,EAC3BkF,wBAA8D;IAE9D,MAAMC,iBAAiBxN,aAAa;IACpC,IAAIwN,gBAAgB;QAClBvL,IAAIC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAMuL,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACblD,cAAc,EAAE,EAChBmD,cAAc,EACf,GAAGpK;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAIiK,aAAaI,YAAY,EAAE;QAC7B,MAAMC,eAAeC,IAAAA,wDAAyB,EAACN;QAC/C,aAAa;QACbO,WAAWC,gBAAgB,GAAGH,aAAarB,OAAO;QAClD,kEAAkE;QAClE,qEAAqE;QACrE,wEAAwE;QACxE,oEAAoE;QACpE,aAAa;QACbuB,WAAWE,mBAAmB,GAAG,CAAC,GAAGC;YACnC,MAAMC,eAAeN,aAAaO,SAAS,IAAIF;YAC/CG,kBAAkBF;YAClB,OAAOA;QACT;IACF;IAEA,IAAIjI,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAE9C,QAAQ,EAAE,GAAG,IAAIgL,IAAI/I,IAAIzC,GAAG,IAAI,KAAK;QAC7CS,WAAWgL,eAAe,oBAA1BhL,WAAWgL,eAAe,MAA1BhL,YAA6BD,UAAU;IACzC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7D4C,QAAQC,GAAG,CAACqI,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAClJ,MAClB;QACAA,IAAImJ,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B3B,kBAAkB4B,KAAK,GAAG;YAE1B,IAAI,iBAAiBb,YAAY;gBAC/B,MAAMc,UAAUC,IAAAA,8DAA+B,EAAC;oBAAEC,OAAO;gBAAK;gBAC9D,IAAIF,SAAS;oBACXG,IAAAA,iBAAS,IACNC,SAAS,CAACC,6BAAkB,CAACC,sBAAsB,EAAE;wBACpDC,WAAWP,QAAQQ,wBAAwB;wBAC3CC,YAAY;4BACV,iCACET,QAAQU,wBAAwB;4BAClC,kBAAkBL,6BAAkB,CAACC,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFX,QAAQQ,wBAAwB,GAC9BR,QAAQY,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMC,WAAwC,CAAC;IAE/C,MAAMhN,yBAAyB,CAAC,EAAC+K,oCAAAA,iBAAkBkC,kBAAkB;IAErE,4BAA4B;IAC5B,MAAM/I,0BAA0BrD,WAAWqD,uBAAuB;IAElE,MAAMgJ,kBAAkBC,IAAAA,kCAAqB,EAAC;QAAEtC;IAAsB;IAEtEuC,IAAAA,+CAA8B,EAAC;QAC7BtQ,MAAMqD,UAAUrD,IAAI;QACpBoH;QACA2G;QACAqC;IACF;IAEApC,aAAauC,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAE3N,MAAMhD,UAAU,EAAE4Q,oBAAoB,EAAE,GAAGxC;IAEnD,IAAIG,gBAAgB;QAClBqC,qBACE,kFACA9J,QAAQC,GAAG;IAEf;IAEAtD,UAAUuE,YAAY,GAAG,EAAE;IAC3BsI,SAAStI,YAAY,GAAGvE,UAAUuE,YAAY;IAE9C,qCAAqC;IACrCzE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnBsN,IAAAA,mCAAoB,EAACtN;IAErB,MAAM,EACJ9D,iBAAiB,EACjBV,iBAAiB,EACjBM,YAAY,EACZR,kBAAkB,EAClBM,YAAY,EACZU,KAAK,EACN,GAAG8N;IAEJ;;;GAGC,GACD,IAAInK;IAEJ,IAAIsD,QAAQC,GAAG,CAACqI,YAAY,KAAK,QAAQ;QACvC5L,YAAYsN,OAAOC,UAAU;IAC/B,OAAO;QACLvN,YAAY4J,QAAQ,6BAA6B4D,MAAM;IACzD;IAEA;;GAEC,GACD,MAAM1Q,SAAS6D,WAAW7D,MAAM,IAAI,CAAC;IAErC,MAAM,EAAEmF,kBAAkB,EAAEjF,mBAAmB,EAAE,GAAGiD;IAEpD,MAAMhD,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAMyQ,kBAAkBC,IAAAA,uDAA8B,EAAC/K,KAAKgL,cAAc;IAE1E,MAAM9O,MAAwB;QAC5BU,cAAcqL;QACd1K;QACAS;QACAV;QACAkK;QACAlN;QACA8C;QACA6N,YAAYrS;QACZgH,UAAUkL;QACVjD;QACA1K;QACA7D;QACA+D;QACAjD;QACAiH;QACA4D;QACA2C;QACAlO;QACA2C;IACF;IAEAoN,IAAAA,iBAAS,IAAGyB,oBAAoB,CAAC,cAAc9Q;IAE/C,IAAIkF,oBAAoB;QACtB,mEAAmE;QACnE,4CAA4C;QAC5C,MAAM6L,+BAA+B1B,IAAAA,iBAAS,IAAG2B,IAAI,CACnDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAEnR,UAAU;YAC7C2P,YAAY;gBACV,cAAc3P;YAChB;QACF,GACAoR;QAGF,MAAMpF,WAAW,MAAM+E,6BACrBnL,KACA3D,KACAH,KACAiO,UACA7M,WACAzD,YACA4I;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACE2D,SAASqF,aAAa,IACtBC,IAAAA,qCAAmB,EAACtF,SAASqF,aAAa,KAC1CzN,WAAW2N,sBAAsB,EACjC;YACAC,IAAAA,SAAI,EAAC;YACL,KAAK,MAAMC,UAAUC,IAAAA,0CAAwB,EAAC1F,SAASqF,aAAa,EAAG;gBACrEG,IAAAA,SAAI,EAACC;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAIzF,SAAS2F,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoB7F,SAAS2F,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAGvR,KAAK;YACxE,IAAIqR,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAI7F,SAASgG,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoB7F,SAASgG,SAAS,CAACE,IAAI,CAAC,CAACnM,MACjDoM,IAAAA,mCAAe,EAACpM;YAElB,IAAI8L,mBAAmB,MAAMA;QAC/B;QAEA,MAAMxT,UAA+B;YACnC0R;QACF;QACA,oEAAoE;QACpE,IACE7M,UAAUkP,kBAAkB,IAC5BlP,UAAUmP,uBAAuB,IACjCnP,UAAUoP,eAAe,EACzB;gBAEEpP;YADF7E,QAAQkU,SAAS,GAAGC,QAAQC,GAAG,CAAC;iBAC9BvP,8BAAAA,UAAUwP,gBAAgB,qBAA1BxP,4BAA4ByP,aAAa,CACvCzP,UAAUoP,eAAe,IAAI,EAAE;mBAE9BM,OAAOd,MAAM,CAAC5O,UAAUkP,kBAAkB,IAAI,CAAC;mBAC9ClP,UAAUmP,uBAAuB,IAAI,EAAE;aAC5C;QACH;QAEA,IAAIrG,SAAS6G,aAAa,EAAE;YAC1B9C,SAAS+C,SAAS,GAAG9G,SAAS6G,aAAa,CAAClR,IAAI,CAAC;QACnD;QAEA,uEAAuE;QACvE,MAAMoR,cAAcC,OAAOhH,SAASiH,cAAc;QAClDhR,IAAIiR,SAAS,CAACC,+CAA6B,EAAEJ;QAC7ChD,SAAS3R,OAAO,KAAK,CAAC;QACtB2R,SAAS3R,OAAO,CAAC+U,+CAA6B,CAAC,GAAGJ;QAElD,yEAAyE;QACzE,YAAY;QACZ,IAAI7P,UAAUkQ,WAAW,KAAK,SAASpH,SAASqH,mBAAmB,KAAK,GAAG;YACzEtD,SAASrH,UAAU,GAAG;QACxB,OAAO;YACL,+DAA+D;YAC/DqH,SAASrH,UAAU,GACjBsD,SAASqH,mBAAmB,IAAI1K,0BAAc,GAC1C,QACAqD,SAASqH,mBAAmB;QACpC;QAEA,qCAAqC;QACrC,IAAItD,SAASrH,UAAU,KAAK,GAAG;YAC7BqH,SAASuD,iBAAiB,GAAG;gBAC3BC,aAAarQ,UAAUsQ,uBAAuB;gBAC9CC,OAAOvQ,UAAUwQ,iBAAiB;YACpC;QACF;QAEA,OAAO,IAAIC,qBAAY,CAAC,MAAMC,IAAAA,oCAAc,EAAC5H,SAAS6H,MAAM,GAAGxV;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMyV,wBACJlQ,WAAWsF,wBAAwB,KACnCoE,kCAAAA,eAAgBwG,qBAAqB;QAEvC,MAAMjO,eAAekO,IAAAA,yCAA2B,EAC9CnO,KACA3D,KACAkB,KACAkF,cACAzE,WAAWoQ,eAAe,EAC1BpQ,WAAWqQ,YAAY,EACvBrV,cACA2O,0BACAuG;QAGF,IACEvN,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB7C,WAAWgL,eAAe,IAC1B,qEAAqE;QACrE,6DAA6D;QAC7DrI,QAAQC,GAAG,CAACqI,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAClJ,QAClB,CAACtH,oBACD;YACA,MAAMsQ,kBAAkBhL,WAAWgL,eAAe;YAClDhJ,IAAImJ,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAACnJ,aAAaqO,WAAW,IAAI,CAAChR,UAAUiR,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAExQ,QAAQ,EAAE,GAAG,IAAIgL,IAAI/I,IAAIzC,GAAG,IAAI,KAAK;oBAC7CyL,gBAAgBjL,UAAU;gBAC5B;YACF;QACF;QAEA,IAAIrF,oBAAoB;YACtB,OAAOoJ,gBAAgB9B,KAAK9D;QAC9B,OAAO,IAAIhD,cAAc;YACvB,OAAO6G,kCAAkCC,KAAK9D,KAAK+D;QACrD;QAEA,MAAMuO,4BAA4B/E,IAAAA,iBAAS,IAAG2B,IAAI,CAChDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAEnR,UAAU;YAC1C2P,YAAY;gBACV,cAAc3P;YAChB;QACF,GACAqU;QAGF,IAAIC,YAAwB;QAC5B,IAAI5D,iBAAiB;YACnB,gFAAgF;YAChF,MAAM6D,sBAAsB,MAAMC,IAAAA,2BAAY,EAAC;gBAC7C5O;gBACA3D;gBACA4L;gBACAoC;gBACAwE,gBAAgB9O;gBAChBzC;gBACA2C;gBACAkI;gBACAjM;YACF;YAEA,IAAIyS,qBAAqB;gBACvB,IAAIA,oBAAoBtT,IAAI,KAAK,aAAa;oBAC5C,MAAMyT,qBAAqBlV,yBAAyBC;oBACpDwC,IAAIC,UAAU,GAAG;oBACjB,MAAM2R,SAAS,MAAMO,0BACnBvO,cACAD,KACA3D,KACAH,KACAoB,WACAwR,oBACAJ,WACAhH;oBAGF,OAAO,IAAIqG,qBAAY,CAACE,QAAQ;wBAAE9D;oBAAS;gBAC7C,OAAO,IAAIwE,oBAAoBtT,IAAI,KAAK,QAAQ;oBAC9C,IAAIsT,oBAAoBI,MAAM,EAAE;wBAC9BJ,oBAAoBI,MAAM,CAACC,cAAc,CAAC7E;wBAC1C,OAAOwE,oBAAoBI,MAAM;oBACnC,OAAO,IAAIJ,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAMjW,UAA+B;YACnC0R;QACF;QAEA,MAAM8D,SAAS,MAAMO,0BACnBvO,cACAD,KACA3D,KACAH,KACAoB,WACAzD,YACA6U,WACAhH;QAGF,oEAAoE;QACpE,IACEpK,UAAUkP,kBAAkB,IAC5BlP,UAAUmP,uBAAuB,IACjCnP,UAAUoP,eAAe,EACzB;gBAEEpP;YADF7E,QAAQkU,SAAS,GAAGC,QAAQC,GAAG,CAAC;iBAC9BvP,+BAAAA,UAAUwP,gBAAgB,qBAA1BxP,6BAA4ByP,aAAa,CACvCzP,UAAUoP,eAAe,IAAI,EAAE;mBAE9BM,OAAOd,MAAM,CAAC5O,UAAUkP,kBAAkB,IAAI,CAAC;mBAC9ClP,UAAUmP,uBAAuB,IAAI,EAAE;aAC5C;QACH;QAEA,iDAAiD;QACjD,OAAO,IAAIsB,qBAAY,CAACE,QAAQxV;IAClC;AACF;AAaO,MAAML,uBAAsC,CACjD4H,KACA3D,KACAjC,UACAgD,OACA/C,qBACA2D,YACA2J,0BACAhP;IAEA,IAAI,CAACqH,IAAIzC,GAAG,EAAE;QACZ,MAAM,IAAI0R,MAAM;IAClB;IAEA,MAAM1R,MAAM2R,IAAAA,kCAAgB,EAAClP,IAAIzC,GAAG,EAAExE,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAMyO,uBAAuBjP,oBAAoByH,IAAIxH,OAAO,EAAE;QAC5DG;QACAU,mBAAmB2E,WAAW8C,YAAY,CAACzH,iBAAiB,KAAK;IACnE;IAEA,MAAM,EAAET,iBAAiB,EAAE,GAAG4O;IAE9B,MAAMC,oBAAoB;QAAE4B,OAAO;IAAM;IACzC,IAAI3B,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAO1J,WAAWsH,SAAS,KAAK,UAAU;QAC5C,IAAIjL,qBAAqB;YACvB,MAAM,IAAI0H,8BAAc,CACtB;QAEJ;QAEA2F,iBAAiByH,IAAAA,mCAAmB,EAClCnR,WAAWsH,SAAS,EACpBtH,WAAW7D,MAAM;IAErB;IAEA,IACEuN,CAAAA,kCAAAA,eAAgBwG,qBAAqB,KACrClQ,WAAWsF,wBAAwB,EACnC;QACA,MAAM,IAAIvB,8BAAc,CACtB;IAEJ;IAEA,MAAMU,eAAe2M,IAAAA,6BAAe,EAClCpR,WAAWqR,WAAW,CAACC,UAAU,CAACrV,IAAI,EACtCsD,KACAlD;IAGF,MAAMiD,YAAYiS,IAAAA,0BAAe,EAAC;QAChCtV,MAAM+D,WAAWqR,WAAW,CAACC,UAAU,CAACrV,IAAI;QAC5CI;QACA2D;QACAyJ;QACA,8CAA8C;QAC9C7O;IACF;IAEA,OAAO4W,0CAAgB,CAAC9O,GAAG,CACzBpD,WACA,sBAAsB;IACtBiK,0BACA,mBAAmB;IACnBvH,KACA3D,KACAkB,KACAnD,UACAgD,OACAY,YACAV,WACAkK,sBACAC,mBACAC,gBACAjF,cACAkF;AAEJ;AAEA,eAAe8G,eACbxO,YAA0B,EAC1BD,GAAoB,EACpB3D,GAAqB,EACrBH,GAAqB,EACrBoB,SAAoB,EACpBT,IAAgB,EAChB6R,SAAc,EACdhH,cAAqC;IAErC,MAAM1J,aAAa9B,IAAI8B,UAAU;IACjC,MAAMiK,eAAejK,WAAWiK,YAAY;IAC5C,4BAA4B;IAC5B,MAAM5G,0BAA0BrD,WAAWqD,uBAAuB;IAElE,MAAM,EAAE8E,0BAA0B,EAAEsJ,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAE1B,MAAMC,kBAAkBC,IAAAA,yBAAiB,EACvCnG,IAAAA,iBAAS,IAAGoG,uBAAuB,IACnC7R,WAAW8C,YAAY,CAACgP,mBAAmB;IAG7C,MAAMC,YACJ/R,WAAWgS,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnV,GAAG,CAAC,CAACkV;YAKOnS;eALO;YAClBqS,KAAK,GAAGnU,IAAI+I,WAAW,CAAC,OAAO,EAAEkL,WAAWG,IAAAA,wCAAmB,EAC7DpU,KACA,QACC;YACHqU,SAAS,GAAEvS,2CAAAA,WAAWwS,4BAA4B,qBAAvCxS,wCAAyC,CAACmS,SAAS;YAC9DM,aAAazS,WAAWyS,WAAW;YACnCC,UAAU;YACVhX,OAAOwC,IAAIxC,KAAK;QAClB;;IAEJ,MAAM,CAACwM,gBAAgByK,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1D5S,WAAWgS,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9E9T,IAAI+I,WAAW,EACfjH,WAAWyS,WAAW,EACtBzS,WAAWwS,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACpU,KAAK,OACzBA,IAAIxC,KAAK,EACTsE,WAAW/D,IAAI;IAGjB,MAAM4W,4BAAwD,IAAIC;IAClE,MAAMC,gBAAgB;IACtB,SAASC,qBAAqB7Q,GAAkB;QAC9C,OAAOnC,WAAWoC,6BAA6B,oBAAxCpC,WAAWoC,6BAA6B,MAAxCpC,YACLmC,KACAH,KACAT,mBAAmBrD,KAAK;IAE5B;IACA,MAAM+U,+BAA+BC,IAAAA,qDAAiC,EACpE,CAAC,CAAClT,WAAWuC,GAAG,EAChB,CAAC,CAACvC,WAAWmT,UAAU,EACvBN,2BACAE,eACAC;IAGF,SAASI,qBAAqBjR,GAAkB;QAC9C,OAAOnC,WAAWoC,6BAA6B,oBAAxCpC,WAAWoC,6BAA6B,MAAxCpC,YACLmC,KACAH,KACAT,mBAAmBrD,KAAK;IAE5B;IAEA,MAAMmV,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD,CAAC,CAACvT,WAAWuC,GAAG,EAChB,CAAC,CAACvC,WAAWmT,UAAU,EACvBN,2BACAQ,mBACAN,eACAK;IAGF,IAAII,oBAA8C;IAElD,MAAMlE,YAAYjR,IAAIiR,SAAS,CAACmE,IAAI,CAACpV;IAErC,IAAI;QACF,IACE,qDAAqD;QACrD2B,WAAWuC,GAAG,IACd,uEAAuE;QACvEI,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAACqI,YAAY,KAAK,UAC7B,yEAAyE;QACzEjL,WAAW8C,YAAY,CAACC,SAAS,EACjC;YACA,wFAAwF;YACxF,MAAMP,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACAyD,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAM,CAAC0E,mBAAmBC,iBAAiB,GAAGC;YAC9CV,WAAWW,WAAW,GAAGF;YAEzB,MAAMgF,oBAAoB,MAAMxF,kDAAoB,CAACC,GAAG,CACtDT,cACAyR,+CAAyB,EACzB;gBACEzR,aAAa0R,cAAc,GAAG;gBAC9B,OAAO1J,aAAaxG,sBAAsB,CACxCjB,YACAa,wBAAwBK,aAAa,EACrC;oBACErB,SAAS4Q;oBACTW,iBAAiB,IACf3R,aAAa0R,cAAc,KAAK,OAAO,cAAc;oBACvDE,kBAAiBtU,GAAW,EAAEuU,aAAqB;wBACjD,kEAAkE;wBAClE,mEAAmE;wBACnE,mEAAmE;wBACnE,OAAO,CAACvU,IAAIwU,UAAU,CAAC,YAAY,CAACxU,IAAIiH,QAAQ,CAAC;oBACnD;gBACF;YAEJ,GACA;gBACEvE,aAAa0R,cAAc,GAAG;YAChC;YAGFvQ,4BACEJ,mBACAnE,MACAX,KACAG,IAAIC,UAAU,KAAK,KACnB+E,yBACA/D,UAAUgE,KAAK,EACfrB,cACAsB,KAAK,CAACP,mBAAmB,2DAA2D;;YAEtFwQ,oBAAoB,IAAIQ,0CAAiB,CAAC/L;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMzF,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/CT,cACAyD,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;YAGrBkV,oBAAoB,IAAIQ,0CAAiB,CACvCvR,kDAAoB,CAACC,GAAG,CACtBT,cACAgI,aAAaxG,sBAAsB,EACnCjB,YACAa,wBAAwBK,aAAa,EACrC;gBACErB,SAAS4Q;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMgB,IAAAA,wCAA6B;QAEnC,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAOjU,WAAWsH,SAAS,KAAK,UAAU;YAC5C,IAAIoC,CAAAA,kCAAAA,eAAgBrM,IAAI,MAAK6W,4BAAY,CAACC,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+BC,IAAAA,kDAA+B,EAClEb,kBAAkBc,GAAG,IACrBpW,IAAIxC,KAAK,EACTgV;gBAGF,OAAO6D,IAAAA,kCAAY,EACjBH,8BACAI,IAAAA,iDAA2B;YAE/B,OAAO,IAAI9K,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAMpC,YAAYmN,IAAAA,qCAAqB,EAAC/K;gBAExC,MAAMgL,SAASzL,QAAQ,yBACpByL,MAAM;gBAET,MAAMC,aAAa,MAAMlS,kDAAoB,CAACC,GAAG,CAC/CT,cACAyS,sBACA,qBAAC1M;oBACCC,mBAAmBuL,kBAAkBc,GAAG;oBACxCpM,gBAAgBA;oBAChB7E,yBAAyBA;oBACzB8E,4BAA4BA;oBAC5BzM,OAAOwC,IAAIxC,KAAK;oBAElB4L,WACA;oBACEjF,SAASiR;oBACT5X,OAAOwC,IAAIxC,KAAK;gBAClB;gBAGF,MAAMkZ,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAN;oBACAqD,sBAAsBzB;oBACtB0B,UAAU/U,WAAW+U,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACA,OAAO,MAAMqD,IAAAA,+CAAyB,EAACL,YAAY;oBACjDM,mBAAmBZ,IAAAA,kDAA+B,EAChDb,kBAAkB0B,OAAO,IACzBhX,IAAIxC,KAAK,EACTgV;oBAEFkE;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAMnR,yBAAyBwF,QAAQ,yBACpCxF,sBAAsB;QAEzB,MAAMkR,aAAa,MAAMlS,kDAAoB,CAACC,GAAG,CAC/CT,cACAwB,sCACA,qBAACuE;YACCC,mBAAmBuL,kBAAkBc,GAAG;YACxCpM,gBAAgBA;YAChB7E,yBAAyBA;YACzB8E,4BAA4BA;YAC5BzM,OAAOwC,IAAIxC,KAAK;YAElB;YACE2G,SAASiR;YACT5X,OAAOwC,IAAIxC,KAAK;YAChByZ,WAAW,CAAC3a;gBACVA,QAAQ+M,OAAO,CAAC,CAAC3K,OAAOF;oBACtB4S,UAAU5S,KAAKE;gBACjB;YACF;YACAwY,kBAAkBpV,WAAWqV,qBAAqB;YAClD,mEAAmE;YACnE,6CAA6C;YAC7CC,kBAAkBtV,WAAWuV,kBAAkB,GAC3C,EAAE,GACF;gBAAC5C;aAAgB;YACrBjC;QACF;QAGF,MAAMkE,wBAAwBC,IAAAA,oDAAyB,EAAC;YACtD9C;YACAN;YACAqD,sBAAsBzB;YACtB0B,UAAU/U,WAAW+U,QAAQ;YAC7BpD,iBAAiBA;QACnB;QACA;;;;;;;;;;;;KAYC,GACD,MAAM6D,qBAAqBxV,WAAWyV,uBAAuB,KAAK;QAClE,MAAMC,qBAAqB1V,WAAWuC,GAAG;QACzC,OAAO,MAAMoT,IAAAA,wCAAkB,EAAChB,YAAY;YAC1CM,mBAAmBZ,IAAAA,kDAA+B,EAChDb,kBAAkB0B,OAAO,IACzBhX,IAAIxC,KAAK,EACTgV;YAEFpP,oBAAoBkU;YACpBZ;YACAgB,0BAA0B;YAC1BF;QACF;IACF,EAAE,OAAOvT,KAAK;QACZ,IACE0T,IAAAA,gDAAuB,EAAC1T,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAI2T,OAAO,KAAK,YACvB3T,IAAI2T,OAAO,CAACtP,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMrE;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4T,qBAAqBC,IAAAA,iCAAmB,EAAC7T;QAC/C,IAAI4T,oBAAoB;YACtB,MAAMlG,QAAQoG,IAAAA,8CAA2B,EAAC9T;YAC1C+T,IAAAA,UAAK,EACH,GAAG/T,IAAIgU,MAAM,CAAC,mDAAmD,EAAEjY,IAAI9B,QAAQ,CAAC,kFAAkF,EAAEyT,OAAO;YAG7K,MAAM1N;QACR;QAEA,IAAI6D;QAEJ,IAAIoQ,IAAAA,6CAAyB,EAACjU,MAAM;YAClC9D,IAAIC,UAAU,GAAG+X,IAAAA,+CAA2B,EAAClU;YAC7C6D,YAAYsQ,IAAAA,sDAAkC,EAACjY,IAAIC,UAAU;QAC/D,OAAO,IAAIiY,IAAAA,8BAAe,EAACpU,MAAM;YAC/B6D,YAAY;YACZ3H,IAAIC,UAAU,GAAGkY,IAAAA,wCAA8B,EAACrU;YAEhD,MAAMsU,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACxU,MACxBnC,WAAW+U,QAAQ;YAGrB,gEAAgE;YAChE,YAAY;YACZ,MAAMva,UAAU,IAAIoc;YACpB,IAAIC,IAAAA,oCAAoB,EAACrc,SAASyH,aAAa6U,cAAc,GAAG;gBAC9DxH,UAAU,cAAcvS,MAAMga,IAAI,CAACvc,QAAQ0T,MAAM;YACnD;YAEAoB,UAAU,YAAYmH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B1X,IAAIC,UAAU,GAAG;QACnB;QAEA,MAAM,CAAC0Y,qBAAqBC,qBAAqB,GAAGrE,IAAAA,mCAAkB,EACpE5S,WAAWgS,aAAa,EACxB9T,IAAI+I,WAAW,EACfjH,WAAWyS,WAAW,EACtBzS,WAAWwS,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACpU,KAAK,QACzBA,IAAIxC,KAAK,EACT;QAGF,MAAMwb,kBAAkB,MAAMzU,kDAAoB,CAACC,GAAG,CACpDT,cACAwF,oBACA5I,MACAX,KACA8H;QAGF,MAAMmR,oBAAoB1U,kDAAoB,CAACC,GAAG,CAChDT,cACAgI,aAAaxG,sBAAsB,EACnCyT,iBACA7T,wBAAwBK,aAAa,EACrC;YACErB,SAAS4Q;QACX;QAGF,IAAIO,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAMrR;QACR;QAEA,IAAI;YACF,MAAMiV,aAAa,MAAM3U,kDAAoB,CAACC,GAAG,CAC/CT,cACAoV,+CAAyB,EACzB;gBACEC,gBAAgBrO,QAAQ;gBACxBsO,uBACE,qBAACjO;oBACCrB,mBAAmBkP;oBACnBjP,gBAAgB8O;oBAChB3T,yBAAyBA;oBACzB3H,OAAOwC,IAAIxC,KAAK;;gBAGpB8b,eAAe;oBACb9b,OAAOwC,IAAIxC,KAAK;oBAChB,wCAAwC;oBACxC4Z,kBAAkB;wBAAC2B;qBAAqB;oBACxCvG;gBACF;YACF;YAGF;;;;;;;;;;;;OAYC,GACD,MAAM8E,qBAAqBxV,WAAWyV,uBAAuB,KAAK;YAClE,MAAMC,qBAAqB1V,WAAWuC,GAAG;YACzC,OAAO,MAAMoT,IAAAA,wCAAkB,EAACyB,YAAY;gBAC1CnC,mBAAmBZ,IAAAA,kDAA+B,EAChD,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACTb,kBAAkB0B,OAAO,IACzBhX,IAAIxC,KAAK,EACTgV;gBAEFpP,oBAAoBkU;gBACpBZ,uBAAuBC,IAAAA,oDAAyB,EAAC;oBAC/C9C;oBACAN;oBACAqD,sBAAsB,EAAE;oBACxBC,UAAU/U,WAAW+U,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACAiE,0BAA0B;gBAC1BF;YACF;QACF,EAAE,OAAO+B,UAAe;YACtB,IACE9U,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBuT,IAAAA,6CAAyB,EAACqB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BzO,QAAQ;gBACVyO;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAASvU;IACP,IAAIF;IACJ,IAAI2U,SAAS,IAAI/I,QAAyB,CAACgJ;QACzC5U,oBAAoB4U;IACtB;IACA,OAAO;QAAC5U;QAAoB2U;KAAO;AACrC;AAEA,eAAevU,4BACbJ,iBAA+D,EAC/DnE,IAAgB,EAChBX,GAAqB,EACrB2Z,UAAmB,EACnBxU,uBAA2E,EAC3EC,KAAa,EACbrB,YAA0B;IAE1B,MAAM,EAAErD,cAAcqL,YAAY,EAAE,GAAG/L;IAEvC,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAM4Z,mCAAmC,IAAI3T;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAM4T,gCAAgC,IAAI5T;IAE1C,MAAME,cAAc,IAAIC,wBAAW;IACnC,MAAMN,2BAA2BC,IAAAA,+CAA8B;IAC/D,MAAM+T,8BAA8C;QAClD3a,MAAM;QACNmH,OAAO;QACPC,cAAc,EAAE;QAChBC,cAAcqT,8BAA8BpT,MAAM;QAClDC,YAAYkT;QACZzT;QACAQ,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMiU,0BAA0B,IAAI9T;IACpC,MAAM+T,8BAA8C;QAClD7a,MAAM;QACNmH,OAAO;QACPC,cAAc,EAAE;QAChBC,cAAcuT,wBAAwBtT,MAAM;QAC5CC,YAAYqT;QACZ5T;QACAQ,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAMmU,yBAAyB,MAAM1V,kDAAoB,CAACC,GAAG,CAC3DsV,6BACAtS,eACA7G,MACAX,KACA2Z;IAGF,IAAIO;IACJ,IAAI;QACFA,sBAAsB3V,kDAAoB,CAACC,GAAG,CAC5CsV,6BACA/N,aAAaxG,sBAAsB,EACnC0U,wBACA9U,wBAAwBK,aAAa,EACrC;YACErB,SAAS,CAACF;gBACR,MAAMkW,SAASC,IAAAA,8CAA0B,EAACnW;gBAE1C,IAAIkW,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IACEP,iCAAiCnT,MAAM,CAAC4T,OAAO,IAC/CR,8BAA8BpT,MAAM,CAAC4T,OAAO,EAC5C;oBACA,mEAAmE;oBACnE,iEAAiE;oBACjE;gBACF,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;oBACAC,IAAAA,iEAAyC,EAACvW,KAAKmB;gBACjD;YACF;YACAqB,QAAQoT,8BAA8BpT,MAAM;QAC9C;IAEJ,EAAE,OAAOxC,KAAc;QACrB,IACE2V,iCAAiCnT,MAAM,CAAC4T,OAAO,IAC/CR,8BAA8BpT,MAAM,CAAC4T,OAAO,EAC5C;QACA,4EAA4E;QAC9E,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAACvW,KAAKmB;QACjD;IACF;IAEA,MAAM,EAAE6E,0BAA0B,EAAE,GAAGuJ,IAAAA,4CAAwB;IAC/D,MAAMhW,QAAQ;IAEd,IAAI0c,qBAAqB;QACvB,MAAM,CAACO,cAAcC,aAAa,GAAGR,oBAAoB9D,GAAG;QAC5D8D,sBAAsB;QACtB,gFAAgF;QAChF,sBAAsB;QACtB,MAAM/d,mBAAmBse,cAActV;QAEvC,MAAMwV,YAAY5P,QAAQ,yBACvB4P,SAAS;QACZ,MAAMC,6BAA6BrW,kDAAoB,CAACC,GAAG,CACzDwV,6BACAW,yBACA,qBAAC7Q;YACCC,mBAAmB2Q;YACnB1Q,gBAAgB,KAAO;YACvB7E,yBAAyBA;YACzB8E,4BAA4BA;YAC5BzM,OAAOA;YAET;YACEiJ,QAAQsT,wBAAwBtT,MAAM;YACtCtC,SAAS,CAACF;gBACR,MAAMkW,SAASC,IAAAA,8CAA0B,EAACnW;gBAE1C,IAAIkW,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAIJ,wBAAwBtT,MAAM,CAAC4T,OAAO,EAAE;gBAC1C,4EAA4E;gBAC9E,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAACvW,KAAKmB;gBACjD;YACF;QACF;QAEFwV,2BAA2BvV,KAAK,CAAC,CAACpB;YAChC,IAAI8V,wBAAwBtT,MAAM,CAAC4T,OAAO,EAAE;YAC1C,2DAA2D;YAC7D,OAAO;gBACL,uEAAuE;gBACvE,yCAAyC;gBACzC,IAAI5V,QAAQC,GAAG,CAAC6V,sBAAsB,EAAE;oBACtCC,IAAAA,iEAAyC,EAACvW,KAAKmB;gBACjD;YACF;QACF;IACF;IAEA,MAAMe,YAAYe,UAAU;IAC5B,8DAA8D;IAC9D,gEAAgE;IAChE6S,wBAAwB5S,KAAK;IAC7B0S,8BAA8B1S,KAAK;IACnCyS,iCAAiCzS,KAAK;IAEtC,sEAAsE;IACtE,kFAAkF;IAElF,MAAM0T,wBAAwB,IAAI5U;IAClC,MAAM6U,wBAAwBC,IAAAA,4CAA0B,EAAC;IAEzD,MAAMC,4BAA4C;QAChD7b,MAAM;QACNmH,OAAO;QACPC,cAAc,EAAE;QAChBC,cAAcqU,sBAAsBpU,MAAM;QAC1CC,YAAYmU;QACZ,uFAAuF;QACvF1U,aAAa;QACbQ,iBAAiBmU;QACjBlU,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMmV,wBAAwB,IAAIhV;IAClC,MAAMiV,wBAAwBH,IAAAA,4CAA0B,EAAC;IACzD,MAAMI,oBAAoBC,IAAAA,8CAA4B;IAEtD,MAAMC,4BAA4C;QAChDlc,MAAM;QACNmH,OAAO;QACPC,cAAc,EAAE;QAChBC,cAAcyU,sBAAsBxU,MAAM;QAC1CC,YAAYuU;QACZ,uFAAuF;QACvF9U,aAAa;QACbQ,iBAAiBuU;QACjBtU,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMwV,qBAAqB,MAAM/W,kDAAoB,CAACC,GAAG,CACvDwW,2BACAxT,eACA7G,MACAX,KACA2Z;IAGF,MAAM4B,8BAA8B,MAAMC,IAAAA,kDAAyB,EACjEX,sBAAsBpU,MAAM,EAC5B,IACElC,kDAAoB,CAACC,GAAG,CACtBwW,2BACAjP,aAAaxG,sBAAsB,EACnC+V,oBACAnW,wBAAwBK,aAAa,EACrC;YACErB,SAAS,CAACF;gBACR,IACE4W,sBAAsBpU,MAAM,CAAC4T,OAAO,IACpCoB,IAAAA,6CAA2B,EAACxX,MAC5B;oBACA,OAAOA,IAAIkW,MAAM;gBACnB;gBAEA,OAAOC,IAAAA,8CAA0B,EAACnW;YACpC;YACAwC,QAAQoU,sBAAsBpU,MAAM;QACtC,IAEJ;QACEoU,sBAAsB1T,KAAK;IAC7B;IAGF,MAAMuU,qBAAqBH,4BAA4BI,cAAc;IACrE,IAAI;QACF,MAAMhB,YAAY5P,QAAQ,yBACvB4P,SAAS;QACZ,MAAMiB,IAAAA,kDAAyB,EAC7B,IACErX,kDAAoB,CAACC,GAAG,CACtB6W,2BACAV,yBACA,qBAAC7Q;gBACCC,mBAAmB2R;gBACnB1R,gBAAgB,KAAO;gBACvB7E,yBAAyBA;gBACzB8E,4BAA4BA;gBAC5BzM,OAAOwC,IAAIxC,KAAK;gBAElB;gBACEiJ,QAAQwU,sBAAsBxU,MAAM;gBACpCtC,SAAS,CAACF,KAAK4X;oBACb,IACEJ,IAAAA,6CAA2B,EAACxX,QAC5BgX,sBAAsBxU,MAAM,CAAC4T,OAAO,EACpC;wBACAtW,aAAaqO,WAAW,GAAG;wBAE3B,MAAM0J,iBAAiBD,UAAUC,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtCC,IAAAA,2CAAyB,EACvB3W,OACA0W,gBACAX,mBACAL,uBACAI;wBAEJ;wBACA;oBACF;oBAEA,OAAOd,IAAAA,8CAA0B,EAACnW;gBACpC;YACF,IAEJ;YACEgX,sBAAsB9T,KAAK;YAC3BuU,mBAAmBM,eAAe;QACpC;IAEJ,EAAE,OAAO/X,KAAK;QACZ,IACEwX,IAAAA,6CAA2B,EAACxX,QAC5BgX,sBAAsBxU,MAAM,CAAC4T,OAAO,EACpC;QACA,4FAA4F;QAC9F,OAAO;YACL,oDAAoD;YACpD,MAAMpW;QACR;IACF;IAEA,SAASgY;QACP,IAAI;YACFC,IAAAA,0CAAwB,EACtB9W,OACA+V,mBACAL,uBACAI;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;IACT;IAEApW,gCAAkB,qBAACmX;AACrB;AAaA;;CAEC,GACD,SAASE,+BAA+B/a,SAAoB;IAC1D,MAAM,EAAEjD,mBAAmB,EAAEiF,kBAAkB,EAAE,GAAGhC;IACpD,IAAI,CAACgC,oBAAoB,OAAO;IAEhC,IAAIjF,uBAAuBA,oBAAoB2R,IAAI,GAAG,GAAG;QACvD,OAAO;IACT;IAEA,OAAO;AACT;AAEA,eAAeR,kBACbxL,GAAoB,EACpB3D,GAAqB,EACrBH,GAAqB,EACrBiO,QAAqC,EACrC7M,SAAoB,EACpBT,IAAgB,EAChB4F,YAA2B;IAE3B,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMiM,YAAY;IAElB,MAAM1Q,aAAa9B,IAAI8B,UAAU;IACjC,MAAMiK,eAAejK,WAAWiK,YAAY;IAC5C,4BAA4B;IAC5B,MAAM5G,0BAA0BrD,WAAWqD,uBAAuB;IAClE,MAAMhH,sBAAsBiD,UAAUjD,mBAAmB;IAEzD,MAAM,EAAE8L,0BAA0B,EAAEsJ,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAE1B,MAAMC,kBAAkBC,IAAAA,yBAAiB,EACvCnG,IAAAA,iBAAS,IAAGoG,uBAAuB,IACnC7R,WAAW8C,YAAY,CAACgP,mBAAmB;IAG7C,MAAMC,YACJ/R,WAAWgS,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnV,GAAG,CAAC,CAACkV;YAKOnS;eALO;YAClBqS,KAAK,GAAGnU,IAAI+I,WAAW,CAAC,OAAO,EAAEkL,WAAWG,IAAAA,wCAAmB,EAC7DpU,KACA,QACC;YACHqU,SAAS,GAAEvS,2CAAAA,WAAWwS,4BAA4B,qBAAvCxS,wCAAyC,CAACmS,SAAS;YAC9DM,aAAazS,WAAWyS,WAAW;YACnCC,UAAU;YACVhX,OAAOwC,IAAIxC,KAAK;QAClB;;IAEJ,MAAM,CAACwM,gBAAgByK,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1D5S,WAAWgS,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9E9T,IAAI+I,WAAW,EACfjH,WAAWyS,WAAW,EACtBzS,WAAWwS,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACpU,KAAK,OACzBA,IAAIxC,KAAK,EACTsE,WAAW/D,IAAI;IAGjB,MAAM4W,4BAAwD,IAAIC;IAClE,+EAA+E;IAC/E,MAAMC,gBAAgB,CAAC,CAAC/S,WAAW8C,YAAY,CAACzH,iBAAiB;IACjE,SAAS2X,qBAAqB7Q,GAAkB;QAC9C,OAAOnC,WAAWoC,6BAA6B,oBAAxCpC,WAAWoC,6BAA6B,MAAxCpC,YACLmC,KACAH,KACAT,mBAAmBrD,KAAK;IAE5B;IACA,MAAM+U,+BAA+BC,IAAAA,qDAAiC,EACpE,CAAC,CAAClT,WAAWuC,GAAG,EAChB,CAAC,CAACvC,WAAWmT,UAAU,EACvBN,2BACAE,eACAC;IAGF,SAASI,qBAAqBjR,GAAkB;QAC9C,OAAOnC,WAAWoC,6BAA6B,oBAAxCpC,WAAWoC,6BAA6B,MAAxCpC,YACLmC,KACAH,KACAT,mBAAmBrD,KAAK;IAE5B;IACA,MAAMmV,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD,CAAC,CAACvT,WAAWuC,GAAG,EAChB,CAAC,CAACvC,WAAWmT,UAAU,EACvBN,2BACAQ,mBACAN,eACAK;IAGF,IAAIkH,6BAG8B;IAClC,MAAMhL,YAAY,CAAC9Q,MAAc5B;QAC/ByB,IAAIiR,SAAS,CAAC9Q,MAAM5B;QAEpBuP,SAAS3R,OAAO,KAAK,CAAC;QACtB2R,SAAS3R,OAAO,CAACgE,KAAK,GAAGH,IAAIiI,SAAS,CAAC9H;QAEvC,OAAOH;IACT;IAEA,IAAIkG,iBAAwC;IAE5C,IAAI;QACF,IAAIvE,WAAW8C,YAAY,CAACC,SAAS,EAAE;YACrC,IAAI/C,WAAW8C,YAAY,CAACzH,iBAAiB,EAAE;gBAC7C;;;;;;;;;;;;SAYC,GAED,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAMyc,mCAAmC,IAAI3T;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAM4T,gCAAgC,IAAI5T;gBAE1C,kFAAkF;gBAClF,yBAAyB;gBACzB,MAAME,cAAc,IAAIC,wBAAW;gBAEnC,iEAAiE;gBACjE,8DAA8D;gBAC9D,wEAAwE;gBACxE,6BAA6B;gBAC7B,MAAMN,2BAA2BC,IAAAA,+CAA8B;gBAE/D,MAAM+T,8BAA+CzT,iBAAiB;oBACpElH,MAAM;oBACNmH,OAAO;oBACPC,cAAcA;oBACdC,cAAcqT,8BAA8BpT,MAAM;oBAClDC,YAAYkT;oBACZzT;oBACAQ,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAMuW,uBAAuB,MAAM9X,kDAAoB,CAACC,GAAG,CACzDsV,6BACAtS,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;gBAGrB,MAAMkc,6BAA6B/X,kDAAoB,CAACC,GAAG,CACzDsV,6BACA/N,aAAa4O,SAAS,EACtB0B,sBACAlX,wBAAwBK,aAAa,EACrC;oBACErB,SAAS,CAACF;wBACR,MAAMkW,SAASC,IAAAA,8CAA0B,EAACnW;wBAE1C,IAAIkW,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAIP,iCAAiCnT,MAAM,CAAC4T,OAAO,EAAE;4BACnD,mEAAmE;4BACnE,iEAAiE;4BACjE;wBACF,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;4BACAC,IAAAA,iEAAyC,EAACvW,KAAK7C,UAAUgE,KAAK;wBAChE;oBACF;oBACA,iFAAiF;oBACjF,qCAAqC;oBACrCmX,YAAY1f;oBACZ,+EAA+E;oBAC/E,iFAAiF;oBACjF,iDAAiD;oBACjD4J,QAAQoT,8BAA8BpT,MAAM;gBAC9C;gBAGF,MAAMN,YAAYe,UAAU;gBAC5B2S,8BAA8B1S,KAAK;gBACnCyS,iCAAiCzS,KAAK;gBAEtC,IAAIqV;gBACJ,IAAI;oBACFA,sBAAsB,MAAMC,IAAAA,yDAAgC,EAC1DH;gBAEJ,EAAE,OAAOrY,KAAK;oBACZ,IACE4V,8BAA8BpT,MAAM,CAAC4T,OAAO,IAC5CT,iCAAiCnT,MAAM,CAAC4T,OAAO,EAC/C;oBACA,4EAA4E;oBAC9E,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAACvW,KAAK7C,UAAUgE,KAAK;oBAChE;gBACF;gBAEA,IAAIoX,qBAAqB;oBACvB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMrgB,mBACJqgB,oBAAoBE,QAAQ,IAC5BvX;oBAGF,MAAM4U,0BAA0B,IAAI9T;oBACpC,MAAM+T,8BAA8C;wBAClD7a,MAAM;wBACNmH,OAAO;wBACPC,cAAcA;wBACdC,cAAcuT,wBAAwBtT,MAAM;wBAC5CC,YAAYqT;wBACZ5T,aAAa;wBACbQ,iBAAiB;wBACjBC,YAAYC,0BAAc;wBAC1BC,QAAQD,0BAAc;wBACtBE,OAAOF,0BAAc;wBACrBG,MAAM;+BAAIT;yBAAa;wBACvBT;oBACF;oBAEA,MAAM6U,YAAY5P,QAAQ,yBACvB4P,SAAS;oBACZ,MAAMgC,IAAAA,2DAAkC,EACtC,IACEpY,kDAAoB,CAACC,GAAG,CACtBwV,6BACAW,yBACA,qBAAC7Q;4BACCC,mBAAmByS,oBAAoBI,iBAAiB;4BACxD5S,gBAAgBA;4BAChB7E,yBAAyBA;4BACzB8E,4BAA4BA;4BAC5BzM,OAAOwC,IAAIxC,KAAK;4BAElB;4BACEiJ,QAAQsT,wBAAwBtT,MAAM;4BACtCtC,SAAS,CAACF;gCACR,MAAMkW,SAASC,IAAAA,8CAA0B,EAACnW;gCAE1C,IAAIkW,QAAQ;oCACV,OAAOA;gCACT;gCAEA,IAAIJ,wBAAwBtT,MAAM,CAAC4T,OAAO,EAAE;gCAC1C,4EAA4E;gCAC9E,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;oCACA,8EAA8E;oCAC9E,mFAAmF;oCACnFC,IAAAA,iEAAyC,EACvCvW,KACA7C,UAAUgE,KAAK;gCAEnB;4BACF;4BACA,mEAAmE;4BACnE,6CAA6C;4BAC7CgS,kBAAkBtV,WAAWuV,kBAAkB,GAC3C,EAAE,GACF;gCAAC5C;6BAAgB;wBACvB,IAEJ;wBACEsF,wBAAwB5S,KAAK;oBAC/B,GACA9B,KAAK,CAAC,CAACpB;wBACP,IACE4V,8BAA8BpT,MAAM,CAAC4T,OAAO,IAC5CoB,IAAAA,6CAA2B,EAACxX,MAC5B;wBACA,4EAA4E;wBAC9E,OAAO,IACLQ,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnFC,IAAAA,iEAAyC,EAACvW,KAAK7C,UAAUgE,KAAK;wBAChE;oBACF;gBACF;gBAEA,IAAIyX,kBAAkB;gBACtB,MAAMhC,wBAAwB,IAAI5U;gBAClC,MAAM6U,wBAAwBC,IAAAA,4CAA0B,EACtDjZ,WAAW2N,sBAAsB;gBAGnC,MAAMqN,4BAA6CzW,iBAAiB;oBAClElH,MAAM;oBACNmH,OAAO;oBACPC,cAAcA;oBACdC,cAAcqU,sBAAsBpU,MAAM;oBAC1CC,YAAYmU;oBACZ,uFAAuF;oBACvF1U,aAAa;oBACbQ,iBAAiBmU;oBACjBlU,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,MAAMiX,yBAAyB,MAAMxY,kDAAoB,CAACC,GAAG,CAC3DsY,2BACAtV,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;gBAErB,MAAMkV,oBAAqB8G,6BACzB,MAAMK,IAAAA,yDAAgC,EACpCE,IAAAA,2DAAkC,EAChC,IACEpY,kDAAoB,CAACC,GAAG,CACtB,qBAAqB;oBACrBsY,2BACA,sBAAsB;oBACtB/Q,aAAa4O,SAAS,EACtB,4CAA4C;oBAC5CoC,wBACA5X,wBAAwBK,aAAa,EACrC;wBACErB,SAAS,CAACF;4BACR,IAAI4W,sBAAsBpU,MAAM,CAAC4T,OAAO,EAAE;gCACxCwC,kBAAkB;gCAClB;4BACF;4BAEA,OAAO9H,6BAA6B9Q;wBACtC;wBACAwC,QAAQoU,sBAAsBpU,MAAM;oBACtC,IAEJ;oBACEoU,sBAAsB1T,KAAK;gBAC7B;gBAIN,MAAM+T,wBAAwBH,IAAAA,4CAA0B,EACtDjZ,WAAW2N,sBAAsB;gBAEnC,MAAMwL,wBAAwB,IAAIhV;gBAClC,MAAMoV,4BAA4C;oBAChDlc,MAAM;oBACNmH,OAAO;oBACPC,cAAcA;oBACdC,cAAcyU,sBAAsBxU,MAAM;oBAC1CC,YAAYuU;oBACZ,oEAAoE;oBACpE9U,aAAa;oBACbQ,iBAAiBuU;oBACjBtU,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,IAAIkX,kBAAkB;gBACtB,IAAI7B,oBAAoBC,IAAAA,8CAA4B;gBAEpD,MAAMT,YAAY5P,QAAQ,yBACvB4P,SAAS;gBACZ,IAAI,EAAEsC,OAAO,EAAE7T,SAAS,EAAE,GAAG,MAAMuT,IAAAA,2DAAkC,EACnE,IACEpY,kDAAoB,CAACC,GAAG,CACtB6W,2BACAV,yBACA,qBAAC7Q;wBACCC,mBAAmBuL,kBAAkBsH,iBAAiB;wBACtD5S,gBAAgBA;wBAChB7E,yBAAyBA;wBACzB8E,4BAA4BA;wBAC5BzM,OAAOwC,IAAIxC,KAAK;wBAElB;wBACEiJ,QAAQwU,sBAAsBxU,MAAM;wBACpCtC,SAAS,CAACF,KAAc4X;4BACtB,IACEJ,IAAAA,6CAA2B,EAACxX,QAC5BgX,sBAAsBxU,MAAM,CAAC4T,OAAO,EACpC;gCACA2C,kBAAkB;gCAElB,MAAMlB,iBAAqC,AACzCD,UACAC,cAAc;gCAChB,IAAI,OAAOA,mBAAmB,UAAU;oCACtCC,IAAAA,2CAAyB,EACvB3a,UAAUgE,KAAK,EACf0W,gBACAX,mBACAL,uBACAI;gCAEJ;gCACA;4BACF;4BAEA,OAAO9F,yBAAyBnR,KAAK4X;wBACvC;wBACA5E,WAAW,CAAC3a;4BACVA,QAAQ+M,OAAO,CAAC,CAAC3K,OAAOF;gCACtB4S,UAAU5S,KAAKE;4BACjB;wBACF;wBACAwY,kBAAkBpV,WAAWqV,qBAAqB;wBAClD,mEAAmE;wBACnE,6CAA6C;wBAC7CC,kBAAkBtV,WAAWuV,kBAAkB,GAC3C,EAAE,GACF;4BAAC5C;yBAAgB;oBACvB,IAEJ;oBACEwG,sBAAsB9T,KAAK;gBAC7B;gBAGF+U,IAAAA,0CAAwB,EACtB9a,UAAUgE,KAAK,EACf+V,mBACAL,uBACAI;gBAGF,MAAMxE,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAN;oBACAqD,sBAAsBzB;oBACtB0B,UAAU/U,WAAW+U,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBAEA,MAAMhT,aAAa,MAAMyc,IAAAA,oCAAc,EAAC5H,kBAAkBoH,QAAQ;gBAClEzO,SAASxN,UAAU,GAAGA;gBACtBwN,SAASkP,WAAW,GAAG,MAAMC,mBAC3B3c,YACAqc,2BACA/Q,cACAjK;gBAGF,IAAI+a,mBAAmBG,iBAAiB;oBACtC,IAAI5T,aAAa,MAAM;wBACrB,oBAAoB;wBACpB6E,SAAS7E,SAAS,GAAG,MAAMiU,IAAAA,4CAA4B,EACrDjU,WACAjL,qBACA2H;oBAEJ,OAAO;wBACL,oBAAoB;wBACpBmI,SAAS7E,SAAS,GAAG,MAAMkU,IAAAA,4CAA4B,EACrDxX;oBAEJ;oBACAwP,kBAAkB0B,OAAO;oBACzB,OAAO;wBACLnH,iBAAiB8E;wBACjBzE,WAAWiF;wBACXpD,QAAQ,MAAMwL,IAAAA,8CAAwB,EAACN,SAAS;4BAC9CvG;wBACF;wBACAnH,eAAeiO,IAAAA,sCAAoB,EACjC1C,uBACAI;wBAEF,0CAA0C;wBAC1C3J,qBAAqBuL,0BAA0BlW,UAAU;wBACzD6W,iBAAiBX,0BAA0BhW,MAAM;wBACjDqK,gBAAgB2L,0BAA0B/V,KAAK;wBAC/CgK,eAAe+L,0BAA0B9V,IAAI;oBAC/C;gBACF,OAAO;oBACL,cAAc;oBACd,IAAI5F,UAAUiR,YAAY,EAAE;wBAC1B,MAAM,IAAIqL,8CAAqB,CAC7B;oBAEJ;oBAEA,IAAIjH,aAAawG;oBACjB,IAAI7T,aAAa,MAAM;wBACrB,+FAA+F;wBAC/F,qGAAqG;wBACrG,MAAMoN,SAASzL,QAAQ,yBACpByL,MAAM;wBAET,qEAAqE;wBACrE,4EAA4E;wBAC5E,MAAMmH,gBAAgB,IAAIC;wBAE1B,MAAMC,eAAe,MAAMrH,qBACzB,qBAAC1M;4BACCC,mBAAmB4T;4BACnB3T,gBAAgB,KAAO;4BACvB7E,yBAAyBA;4BACzB8E,4BAA4BA;4BAC5BzM,OAAOwC,IAAIxC,KAAK;4BAElBsgB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAC5U,aAC1B;4BACE3C,QAAQwX,IAAAA,4CAA0B,EAAC;4BACnC9Z,SAASiR;4BACT5X,OAAOwC,IAAIxC,KAAK;wBAClB;wBAGF,wGAAwG;wBACxGiZ,aAAaJ,IAAAA,kCAAY,EAAC4G,SAASY;oBACrC;oBAEA,OAAO;wBACLhO,iBAAiB8E;wBACjBzE,WAAWiF;wBACXpD,QAAQ,MAAMmM,IAAAA,6CAAuB,EAACzH,YAAY;4BAChDM,mBAAmBZ,IAAAA,kDAA+B,EAChDb,kBAAkB6I,eAAe,IACjCne,IAAIxC,KAAK,EACTgV;4BAEFkE;wBACF;wBACAnH,eAAeiO,IAAAA,sCAAoB,EACjC1C,uBACAI;wBAEF,0CAA0C;wBAC1C3J,qBAAqBuL,0BAA0BlW,UAAU;wBACzD6W,iBAAiBX,0BAA0BhW,MAAM;wBACjDqK,gBAAgB2L,0BAA0B/V,KAAK;wBAC/CgK,eAAe+L,0BAA0B9V,IAAI;oBAC/C;gBACF;YACF,OAAO;gBACL;;;;;;;;;;;;;;;;SAgBC,GAED,MAAMoX,QAAQhd,UAAUwP,gBAAgB;gBACxC,IAAI,CAACwN,OAAO;oBACV,MAAM,IAAIrL,MACR;gBAEJ;gBAEA,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAM6G,mCAAmC,IAAI3T;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAM4T,gCAAgC,IAAI5T;gBAE1C,MAAME,cAAc,IAAIC,wBAAW;gBACnC,MAAMN,2BAA2BC,IAAAA,+CAA8B;gBAE/D,MAAM+T,8BAA+CzT,iBAAiB;oBACpElH,MAAM;oBACNmH,OAAO;oBACPC,cAAcA;oBACdC,cAAcqT,8BAA8BpT,MAAM;oBAClDC,YAAYkT;oBACZzT;oBACAQ,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,MAAMiU,0BAA0B,IAAI9T;gBACpC,MAAM+T,8BAA+C3T,iBAAiB;oBACpElH,MAAM;oBACNmH,OAAO;oBACPC,cAAcA;oBACdC,cAAcuT,wBAAwBtT,MAAM;oBAC5CC,YAAYqT;oBACZ5T;oBACAQ,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAMmU,yBAAyB,MAAM1V,kDAAoB,CAACC,GAAG,CAC3DsV,6BACAtS,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;gBAGrB,IAAI8Z;gBACJ,IAAI;oBACFA,sBAAsB3V,kDAAoB,CAACC,GAAG,CAC5CsV,6BACA/N,aAAaxG,sBAAsB,EACnC0U,wBACA9U,wBAAwBK,aAAa,EACrC;wBACErB,SAAS,CAACF;4BACR,MAAMkW,SAASC,IAAAA,8CAA0B,EAACnW;4BAE1C,IAAIkW,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IACEP,iCAAiCnT,MAAM,CAAC4T,OAAO,IAC/CR,8BAA8BpT,MAAM,CAAC4T,OAAO,EAC5C;gCACA,mEAAmE;gCACnE,iEAAiE;gCACjE;4BACF,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;gCACAC,IAAAA,iEAAyC,EACvCvW,KACA7C,UAAUgE,KAAK;4BAEnB;wBACF;wBACAqB,QAAQoT,8BAA8BpT,MAAM;oBAC9C;gBAEJ,EAAE,OAAOxC,KAAc;oBACrB,IACE2V,iCAAiCnT,MAAM,CAAC4T,OAAO,IAC/CR,8BAA8BpT,MAAM,CAAC4T,OAAO,EAC5C;oBACA,4EAA4E;oBAC9E,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAACvW,KAAK7C,UAAUgE,KAAK;oBAChE;gBACF;gBAEA,IAAI8U,qBAAqB;oBACvB,MAAM,CAACO,cAAcC,aAAa,GAAGR,oBAAoB9D,GAAG;oBAC5D8D,sBAAsB;oBACtB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAM/d,mBAAmBse,cAActV;oBAEvC,MAAMwV,YAAY5P,QAAQ,yBACvB4P,SAAS;oBACZ,MAAMC,6BAA6BrW,kDAAoB,CAACC,GAAG,CACzDwV,6BACAW,yBACA,qBAAC7Q;wBACCC,mBAAmB2Q;wBACnB1Q,gBAAgBA;wBAChB7E,yBAAyBA;wBACzB8E,4BAA4BA;wBAC5BzM,OAAOwC,IAAIxC,KAAK;wBAElB;wBACEiJ,QAAQsT,wBAAwBtT,MAAM;wBACtCtC,SAAS,CAACF;4BACR,MAAMkW,SAASC,IAAAA,8CAA0B,EAACnW;4BAE1C,IAAIkW,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IAAIJ,wBAAwBtT,MAAM,CAAC4T,OAAO,EAAE;4BAC1C,4EAA4E;4BAC9E,OAAO,IACL5V,QAAQC,GAAG,CAAC4V,gBAAgB,IAC5B7V,QAAQC,GAAG,CAAC6V,sBAAsB,EAClC;gCACA,8EAA8E;gCAC9E,mFAAmF;gCACnFC,IAAAA,iEAAyC,EACvCvW,KACA7C,UAAUgE,KAAK;4BAEnB;wBACF;wBACA,mEAAmE;wBACnE,6CAA6C;wBAC7CgS,kBAAkBtV,WAAWuV,kBAAkB,GAC3C,EAAE,GACF;4BAAC5C;yBAAgB;oBACvB;oBAEFmG,2BAA2BvV,KAAK,CAAC,CAACpB;wBAChC,IAAI8V,wBAAwBtT,MAAM,CAAC4T,OAAO,EAAE;wBAC1C,2DAA2D;wBAC7D,OAAO;4BACL,uEAAuE;4BACvE,yCAAyC;4BACzC,IAAI5V,QAAQC,GAAG,CAAC6V,sBAAsB,EAAE;gCACtCC,IAAAA,iEAAyC,EAACvW,KAAK7C,UAAUgE,KAAK;4BAChE;wBACF;oBACF;gBACF;gBAEA,MAAMe,YAAYe,UAAU;gBAC5B,8DAA8D;gBAC9D,gEAAgE;gBAChE6S,wBAAwB5S,KAAK;gBAC7B0S,8BAA8B1S,KAAK;gBACnCyS,iCAAiCzS,KAAK;gBAEtC,sEAAsE;gBACtE,kFAAkF;gBAElF,IAAI0V,kBAAkB;gBACtB,MAAMhC,wBAAwB,IAAI5U;gBAClC,MAAM6U,wBAAwBC,IAAAA,4CAA0B,EACtDjZ,WAAW2N,sBAAsB;gBAGnC,MAAMuL,4BAA6C3U,iBAAiB;oBAClElH,MAAM;oBACNmH,OAAO;oBACPC,cAAcA;oBACdC,cAAcqU,sBAAsBpU,MAAM;oBAC1CC,YAAYmU;oBACZ,uFAAuF;oBACvF1U,aAAa;oBACbQ,iBAAiBmU;oBACjBlU,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,IAAIkX,kBAAkB;gBACtB,MAAM/B,wBAAwB,IAAIhV;gBAClC,MAAMiV,wBAAwBH,IAAAA,4CAA0B,EACtDjZ,WAAW2N,sBAAsB;gBAEnC,MAAM0L,oBAAoBC,IAAAA,8CAA4B;gBAEtD,MAAMC,4BAA6ChV,iBAAiB;oBAClElH,MAAM;oBACNmH,OAAO;oBACPC,cAAcA;oBACdC,cAAcyU,sBAAsBxU,MAAM;oBAC1CC,YAAYuU;oBACZ,uFAAuF;oBACvF9U,aAAa;oBACbQ,iBAAiBuU;oBACjBtU,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,MAAMwV,qBAAqB,MAAM/W,kDAAoB,CAACC,GAAG,CACvDwW,2BACAxT,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;gBAGrB,MAAMmb,8BAA+Ba,6BACnC,MAAMZ,IAAAA,kDAAyB,EAC7BX,sBAAsBpU,MAAM,EAC5B,IACElC,kDAAoB,CAACC,GAAG,CACtBwW,2BACAjP,aAAaxG,sBAAsB,EACnC+V,oBACAnW,wBAAwBK,aAAa,EACrC;wBACErB,SAAS,CAACF;4BACR,IAAI4W,sBAAsBpU,MAAM,CAAC4T,OAAO,EAAE;gCACxCwC,kBAAkB;gCAClB,IAAIpB,IAAAA,6CAA2B,EAACxX,MAAM;oCACpC,OAAOA,IAAIkW,MAAM;gCACnB;gCACA,OAAOC,IAAAA,8CAA0B,EAACnW;4BACpC;4BAEA,OAAO8Q,6BAA6B9Q;wBACtC;wBACAwC,QAAQoU,sBAAsBpU,MAAM;oBACtC,IAEJ;oBACEoU,sBAAsB1T,KAAK;gBAC7B;gBAGJ,IAAIsP;gBACJ,MAAMiF,qBAAqBH,4BAA4BI,cAAc;gBACrE,IAAI;oBACF,MAAMhB,YAAY5P,QAAQ,yBACvB4P,SAAS;oBACZ,MAAM9H,SAAS,MAAM+I,IAAAA,kDAAyB,EAC5C,IACErX,kDAAoB,CAACC,GAAG,CACtB6W,2BACAV,yBACA,qBAAC7Q;4BACCC,mBAAmB2R;4BACnB1R,gBAAgBA;4BAChB7E,yBAAyBA;4BACzB8E,4BAA4BA;4BAC5BzM,OAAOwC,IAAIxC,KAAK;4BAElB;4BACEiJ,QAAQwU,sBAAsBxU,MAAM;4BACpCtC,SAAS,CAACF,KAAc4X;gCACtB,IACEJ,IAAAA,6CAA2B,EAACxX,QAC5BgX,sBAAsBxU,MAAM,CAAC4T,OAAO,EACpC;oCACA2C,kBAAkB;oCAElB,MAAMlB,iBAAqC,AACzCD,UACAC,cAAc;oCAChB,IAAI,OAAOA,mBAAmB,UAAU;wCACtCC,IAAAA,2CAAyB,EACvB3a,UAAUgE,KAAK,EACf0W,gBACAX,mBACAL,uBACAI;oCAEJ;oCACA;gCACF;gCAEA,OAAO9F,yBAAyBnR,KAAK4X;4BACvC;4BACA,mEAAmE;4BACnE,6CAA6C;4BAC7CzE,kBAAkBtV,WAAWuV,kBAAkB,GAC3C,EAAE,GACF;gCAAC5C;6BAAgB;wBACvB,IAEJ;wBACEwG,sBAAsB9T,KAAK;wBAC3BuU,mBAAmBM,eAAe;oBACpC;oBAEFvF,aAAa5D,OAAOoK,OAAO;gBAC7B,EAAE,OAAOhZ,KAAK;oBACZ,IACEwX,IAAAA,6CAA2B,EAACxX,QAC5BgX,sBAAsBxU,MAAM,CAAC4T,OAAO,EACpC;oBACA,4FAA4F;oBAC9F,OAAO;wBACL,oDAAoD;wBACpD,MAAMpW;oBACR;gBACF;gBAEAiY,IAAAA,0CAAwB,EACtB9a,UAAUgE,KAAK,EACf+V,mBACAL,uBACAI;gBAGF,IAAI2B,mBAAmBG,iBAAiB;oBACtC,MAAMqB,gBAAgBxB,kBAClByB,IAAAA,uCAAqB,EAACxD,yBACtBwD,IAAAA,uCAAqB,EAACpD;oBAC1B,IAAImD,eAAe;wBACjB,MAAM,IAAIE,sCAAkB,CAC1B,CAAC,OAAO,EAAEnd,UAAUgE,KAAK,CAAC,oDAAoD,EAAEiZ,cAAc,4EAA4E,CAAC;oBAE/K,OAAO;wBACL,MAAM,IAAIE,sCAAkB,CAC1B,CAAC,OAAO,EAAEnd,UAAUgE,KAAK,CAAC,0JAA0J,CAAC;oBAEzL;gBACF;gBAEA,MAAM3E,aAAa,MAAMyc,IAAAA,oCAAc,EACrC3B,4BAA4BmB,QAAQ;gBAEtCzO,SAASxN,UAAU,GAAGA;gBACtBwN,SAASkP,WAAW,GAAG,MAAMC,mBAC3B3c,YACA4a,2BACAtP,cACAjK;gBAGF,MAAM4U,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAN;oBACAqD,sBAAsBzB;oBACtB0B,UAAU/U,WAAW+U,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACA,MAAM+D,qBAAqB1V,WAAWuC,GAAG;gBACzC,OAAO;oBACLwL,iBAAiB8E;oBACjBzE,WAAWiF;oBACXpD,QAAQ,MAAM0F,IAAAA,wCAAkB,EAAChB,YAAa;wBAC5CM,mBAAmBZ,IAAAA,kDAA+B,EAChDoF,4BAA4BmB,QAAQ,IACpC1c,IAAIxC,KAAK,EACTgV;wBAEFpP,oBAAoB;wBACpBsT;wBACAgB,0BAA0B;wBAC1BF;oBACF;oBACAjI,eAAeiO,IAAAA,sCAAoB,EACjC1C,uBACAI;oBAEF,0CAA0C;oBAC1C3J,qBAAqByJ,0BAA0BpU,UAAU;oBACzD6W,iBAAiBzC,0BAA0BlU,MAAM;oBACjDqK,gBAAgB6J,0BAA0BjU,KAAK;oBAC/CgK,eAAeiK,0BAA0BhU,IAAI;gBAC/C;YACF;QACF,OAAO,IAAIlF,WAAW8C,YAAY,CAACzH,iBAAiB,EAAE;YACpD,uEAAuE;YACvE,IAAIwJ,kBAAkBoU,IAAAA,4CAA0B,EAC9CjZ,WAAW2N,sBAAsB;YAGnC,MAAM3J,2BAA2BC,IAAAA,+CAA8B;YAC/D,MAAMyY,4BAA6CnY,iBAAiB;gBAClElH,MAAM;gBACNmH,OAAO;gBACPC,cAAcA;gBACdI;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIT;iBAAa;gBACvBT;YACF;YACA,MAAMxB,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/Cga,2BACAhX,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAMkV,oBAAqB8G,6BACzB,MAAMqC,IAAAA,mEAA0C,EAC9Cla,kDAAoB,CAACC,GAAG,CACtBga,2BACAzS,aAAaxG,sBAAsB,EACnC,4CAA4C;YAC5CjB,YACAa,wBAAwBK,aAAa,EACrC;gBACErB,SAAS4Q;YACX;YAIN,MAAM2J,oBAAoC;gBACxCvf,MAAM;gBACNmH,OAAO;gBACPC,cAAcA;gBACdI;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIT;iBAAa;gBACvBT;YACF;YACA,MAAM6U,YAAY5P,QAAQ,yBACvB4P,SAAS;YACZ,MAAM,EAAEsC,OAAO,EAAE7T,SAAS,EAAE,GAAG,MAAM7E,kDAAoB,CAACC,GAAG,CAC3Dka,mBACA/D,yBACA,qBAAC7Q;gBACCC,mBAAmBuL,kBAAkBsH,iBAAiB;gBACtD5S,gBAAgBA;gBAChB7E,yBAAyBA;gBACzB8E,4BAA4BA;gBAC5BzM,OAAOwC,IAAIxC,KAAK;gBAElB;gBACE2G,SAASiR;gBACT6B,WAAW,CAAC3a;oBACVA,QAAQ+M,OAAO,CAAC,CAAC3K,OAAOF;wBACtB4S,UAAU5S,KAAKE;oBACjB;gBACF;gBACAwY,kBAAkBpV,WAAWqV,qBAAqB;gBAClD,mEAAmE;gBACnE,6CAA6C;gBAC7CC,kBAAkBtV,WAAWuV,kBAAkB,GAC3C,EAAE,GACF;oBAAC5C;iBAAgB;YACvB;YAEF,MAAMiC,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD9C;gBACAN;gBACAqD,sBAAsBzB;gBACtB0B,UAAU/U,WAAW+U,QAAQ;gBAC7BpD,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMhT,aAAa,MAAMyc,IAAAA,oCAAc,EAAC5H,kBAAkBoH,QAAQ;YAElE,IAAIP,+BAA+B/a,YAAY;gBAC7C6M,SAASxN,UAAU,GAAGA;gBACtBwN,SAASkP,WAAW,GAAG,MAAMC,mBAC3B3c,YACAie,mBACA3S,cACAjK;YAEJ;YAEA;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAI0N,IAAAA,qCAAmB,EAAC7I,gBAAgBgY,eAAe,GAAG;gBACxD,IAAIvV,aAAa,MAAM;oBACrB,qBAAqB;oBACrB6E,SAAS7E,SAAS,GAAG,MAAMiU,IAAAA,4CAA4B,EACrDjU,WACAjL,qBACA2H;gBAEJ,OAAO;oBACL,qBAAqB;oBACrBmI,SAAS7E,SAAS,GAAG,MAAMkU,IAAAA,4CAA4B,EACrDxX;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtDwP,kBAAkB0B,OAAO;gBACzB,OAAO;oBACLnH,iBAAiB8E;oBACjBzE,WAAWiF;oBACXpD,QAAQ,MAAMwL,IAAAA,8CAAwB,EAACN,SAAS;wBAC9CvG;oBACF;oBACAnH,eAAe5I,gBAAgBgY,eAAe;oBAC9C,0CAA0C;oBAC1CpN,qBAAqBiN,0BAA0B5X,UAAU;oBACzD6W,iBAAiBe,0BAA0B1X,MAAM;oBACjDqK,gBAAgBqN,0BAA0BzX,KAAK;oBAC/CgK,eAAeyN,0BAA0BxX,IAAI;gBAC/C;YACF,OAAO,IAAI7I,uBAAuBA,oBAAoB2R,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/B7B,SAAS7E,SAAS,GAAG,MAAMkU,IAAAA,4CAA4B,EACrDxX;gBAGF,OAAO;oBACL+J,iBAAiB8E;oBACjBzE,WAAWiF;oBACXpD,QAAQ,MAAMwL,IAAAA,8CAAwB,EAACN,SAAS;wBAC9CvG;oBACF;oBACAnH,eAAe5I,gBAAgBgY,eAAe;oBAC9C,0CAA0C;oBAC1CpN,qBAAqBiN,0BAA0B5X,UAAU;oBACzD6W,iBAAiBe,0BAA0B1X,MAAM;oBACjDqK,gBAAgBqN,0BAA0BzX,KAAK;oBAC/CgK,eAAeyN,0BAA0BxX,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAI5F,UAAUiR,YAAY,EAAE;oBAC1B,MAAM,IAAIqL,8CAAqB,CAC7B;gBAEJ;gBAEA,IAAIjH,aAAawG;gBACjB,IAAI7T,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMoN,SAASzL,QAAQ,yBACpByL,MAAM;oBAET,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMmH,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMrH,qBACzB,qBAAC1M;wBACCC,mBAAmB4T;wBACnB3T,gBAAgB,KAAO;wBACvB7E,yBAAyBA;wBACzB8E,4BAA4BA;wBAC5BzM,OAAOwC,IAAIxC,KAAK;wBAElBsgB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAC5U,aAC1B;wBACE3C,QAAQwX,IAAAA,4CAA0B,EAAC;wBACnC9Z,SAASiR;wBACT5X,OAAOwC,IAAIxC,KAAK;oBAClB;oBAGF,wGAAwG;oBACxGiZ,aAAaJ,IAAAA,kCAAY,EAAC4G,SAASY;gBACrC;gBAEA,OAAO;oBACLhO,iBAAiB8E;oBACjBzE,WAAWiF;oBACXpD,QAAQ,MAAMmM,IAAAA,6CAAuB,EAACzH,YAAY;wBAChDM,mBAAmBZ,IAAAA,kDAA+B,EAChDb,kBAAkB6I,eAAe,IACjCne,IAAIxC,KAAK,EACTgV;wBAEFkE;oBACF;oBACAnH,eAAe5I,gBAAgBgY,eAAe;oBAC9C,0CAA0C;oBAC1CpN,qBAAqBiN,0BAA0B5X,UAAU;oBACzD6W,iBAAiBe,0BAA0B1X,MAAM;oBACjDqK,gBAAgBqN,0BAA0BzX,KAAK;oBAC/CgK,eAAeyN,0BAA0BxX,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAM4X,uBAAwCvY,iBAAiB;gBAC7DlH,MAAM;gBACNmH,OAAO;gBACPC,cAAcA;gBACdK,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIT;iBAAa;YACzB;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAMjC,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/Coa,sBACApX,eACA7G,MACAX,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAMkV,oBAAqB8G,6BACzB,MAAMqC,IAAAA,mEAA0C,EAC9Cla,kDAAoB,CAACC,GAAG,CACtBoa,sBACA7S,aAAaxG,sBAAsB,EACnCjB,YACAa,wBAAwBK,aAAa,EACrC;gBACErB,SAAS4Q;YACX;YAIN,MAAMxP,yBAAyBwF,QAAQ,yBACpCxF,sBAAsB;YAEzB,MAAMkR,aAAa,MAAMlS,kDAAoB,CAACC,GAAG,CAC/Coa,sBACArZ,sCACA,qBAACuE;gBACCC,mBAAmBuL,kBAAkBsH,iBAAiB;gBACtD5S,gBAAgBA;gBAChB7E,yBAAyBA;gBACzB8E,4BAA4BA;gBAC5BzM,OAAOwC,IAAIxC,KAAK;gBAElB;gBACE2G,SAASiR;gBACT5X,OAAOwC,IAAIxC,KAAK;gBAChB,mEAAmE;gBACnE,6CAA6C;gBAC7C4Z,kBAAkBtV,WAAWuV,kBAAkB,GAC3C,EAAE,GACF;oBAAC5C;iBAAgB;YACvB;YAGF,IAAI0H,+BAA+B/a,YAAY;gBAC7C,MAAMX,aAAa,MAAMyc,IAAAA,oCAAc,EAAC5H,kBAAkBoH,QAAQ;gBAClEzO,SAASxN,UAAU,GAAGA;gBACtBwN,SAASkP,WAAW,GAAG,MAAMC,mBAC3B3c,YACAme,sBACA7S,cACAjK;YAEJ;YAEA,MAAM4U,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD9C;gBACAN;gBACAqD,sBAAsBzB;gBACtB0B,UAAU/U,WAAW+U,QAAQ;gBAC7BpD,iBAAiBA;YACnB;YACA,OAAO;gBACL5D,iBAAiB8E;gBACjBzE,WAAWiF;gBACXpD,QAAQ,MAAM0F,IAAAA,wCAAkB,EAAChB,YAAY;oBAC3CM,mBAAmBZ,IAAAA,kDAA+B,EAChDb,kBAAkB6I,eAAe,IACjCne,IAAIxC,KAAK,EACTgV;oBAEFpP,oBAAoB;oBACpBsT;oBACAgB,0BAA0B;gBAC5B;gBACA,0CAA0C;gBAC1CnG,qBAAqBqN,qBAAqBhY,UAAU;gBACpD6W,iBAAiBmB,qBAAqB9X,MAAM;gBAC5CqK,gBAAgByN,qBAAqB7X,KAAK;gBAC1CgK,eAAe6N,qBAAqB5X,IAAI;YAC1C;QACF;IACF,EAAE,OAAO/C,KAAK;QACZ,IACE0T,IAAAA,gDAAuB,EAAC1T,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAI2T,OAAO,KAAK,YACvB3T,IAAI2T,OAAO,CAACtP,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMrE;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAI4a,IAAAA,wCAAoB,EAAC5a,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4T,qBAAqBC,IAAAA,iCAAmB,EAAC7T;QAC/C,IAAI4T,oBAAoB;YACtB,MAAMlG,QAAQoG,IAAAA,8CAA2B,EAAC9T;YAC1C+T,IAAAA,UAAK,EACH,GAAG/T,IAAIgU,MAAM,CAAC,mDAAmD,EAAEjY,IAAI9B,QAAQ,CAAC,kFAAkF,EAAEyT,OAAO;YAG7K,MAAM1N;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAImY,+BAA+B,MAAM;YACvC,MAAMnY;QACR;QAEA,IAAI6D;QAEJ,IAAIoQ,IAAAA,6CAAyB,EAACjU,MAAM;YAClC9D,IAAIC,UAAU,GAAG+X,IAAAA,+CAA2B,EAAClU;YAC7C6D,YAAYsQ,IAAAA,sDAAkC,EAACjY,IAAIC,UAAU;QAC/D,OAAO,IAAIiY,IAAAA,8BAAe,EAACpU,MAAM;YAC/B6D,YAAY;YACZ3H,IAAIC,UAAU,GAAGkY,IAAAA,wCAA8B,EAACrU;YAEhD,MAAMsU,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACxU,MACxBnC,WAAW+U,QAAQ;YAGrBzF,UAAU,YAAYmH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B1X,IAAIC,UAAU,GAAG;QACnB;QAEA,MAAM,CAAC0Y,qBAAqBC,qBAAqB,GAAGrE,IAAAA,mCAAkB,EACpE5S,WAAWgS,aAAa,EACxB9T,IAAI+I,WAAW,EACfjH,WAAWyS,WAAW,EACtBzS,WAAWwS,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACpU,KAAK,QACzBA,IAAIxC,KAAK,EACT;QAGF,MAAMohB,uBAAwCvY,iBAAiB;YAC7DlH,MAAM;YACNmH,OAAO;YACPC,cAAcA;YACdK,YAAYC,0BAAc;YAC1BC,QAAQD,0BAAc;YACtBE,OAAOF,0BAAc;YACrBG,MAAM;mBAAIT;aAAa;QACzB;QACA,MAAMyS,kBAAkB,MAAMzU,kDAAoB,CAACC,GAAG,CACpDoa,sBACArV,oBACA5I,MACAX,KACA8H;QAGF,MAAMmR,oBAAoB1U,kDAAoB,CAACC,GAAG,CAChDoa,sBACA7S,aAAaxG,sBAAsB,EACnCyT,iBACA7T,wBAAwBK,aAAa,EACrC;YACErB,SAAS4Q;QACX;QAGF,IAAI;YACF,MAAMmE,aAAa,MAAMC,IAAAA,+CAAyB,EAAC;gBACjDC,gBAAgBrO,QAAQ;gBACxBsO,uBACE,qBAACjO;oBACCrB,mBAAmBkP;oBACnBjP,gBAAgB8O;oBAChB3T,yBAAyBA;oBACzB3H,OAAOwC,IAAIxC,KAAK;;gBAGpB8b,eAAe;oBACb9b,OAAOwC,IAAIxC,KAAK;oBAChB,wCAAwC;oBACxC4Z,kBAAkB;wBAAC2B;qBAAqB;oBACxCvG;gBACF;YACF;YAEA,IAAI2J,+BAA+B/a,YAAY;gBAC7C,MAAMX,aAAa,MAAMyc,IAAAA,oCAAc,EACrCd,2BAA2BM,QAAQ;gBAErCzO,SAASxN,UAAU,GAAGA;gBACtBwN,SAASkP,WAAW,GAAG,MAAMC,mBAC3B3c,YACAme,sBACA7S,cACAjK;YAEJ;YAEA,MAAM0V,qBAAqB1V,WAAWuC,GAAG;YAEzC,oEAAoE;YACpE,gEAAgE;YAChE,MAAMya,eACJ1C,sCAAsC2C,oDAA2B,GAC7D3C,2BAA2BM,QAAQ,KACnCN,2BAA2B+B,eAAe;YAEhD,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9BtO,iBAAiB8E;gBACjBzE,WAAWiF;gBACXpD,QAAQ,MAAM0F,IAAAA,wCAAkB,EAACyB,YAAY;oBAC3CnC,mBAAmBZ,IAAAA,kDAA+B,EAChD2I,cACA9e,IAAIxC,KAAK,EACTgV;oBAEFpP,oBAAoB;oBACpBsT,uBAAuBC,IAAAA,oDAAyB,EAAC;wBAC/C9C;wBACAN;wBACAqD,sBAAsB,EAAE;wBACxBC,UAAU/U,WAAW+U,QAAQ;wBAC7BpD,iBAAiBA;oBACnB;oBACAiE,0BAA0B;oBAC1BF;gBACF;gBACAjI,eAAe;gBACfgC,qBACElL,mBAAmB,OAAOA,eAAeO,UAAU,GAAGC,0BAAc;gBACtE4W,iBACEpX,mBAAmB,OAAOA,eAAeS,MAAM,GAAGD,0BAAc;gBAClEsK,gBACE9K,mBAAmB,OAAOA,eAAeU,KAAK,GAAGF,0BAAc;gBACjEkK,eAAe1K,mBAAmB,OAAOA,eAAeW,IAAI,GAAG;YACjE;QACF,EAAE,OAAOuS,UAAe;YACtB,IACE9U,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBuT,IAAAA,6CAAyB,EAACqB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BzO,QAAQ;gBACVyO;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMyF,gBAAuC,IAAIvc;AACjD,MAAMwc,iBAA+C,EAAE;AAEvD,SAASrS,kBAAkBsS,IAAsB;IAC/CF,cAAcG,GAAG,CAACD;IAClBA,KAAKE,OAAO,CAAC;QACX,IAAIJ,cAAcrgB,GAAG,CAACugB,OAAO;YAC3BF,cAAcK,MAAM,CAACH;YACrB,IAAIF,cAAclP,IAAI,KAAK,GAAG;gBAC5B,uEAAuE;gBACvE,IAAK,IAAI9Q,IAAI,GAAGA,IAAIigB,eAAe9O,MAAM,EAAEnR,IAAK;oBAC9CigB,cAAc,CAACjgB,EAAE;gBACnB;gBACAigB,eAAe9O,MAAM,GAAG;YAC1B;QACF;IACF;AACF;AAEO,eAAehU,mBACpB2iB,YAAwC,EACxC3Z,uBAA8D;IAE9D,IAAIma;IACJ,IAAI7a,QAAQC,GAAG,CAAC6a,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DvU,QAAQ,0CAA0CuU,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DvU,QAAQ,wCAAwCuU,wBAAwB;IAC5E;IAEA,IAAI;QACFA,yBAAyBR,cAAc;YACrCU,wBAAwB;gBACtBC,eAAeta,wBAAwBsa,aAAa;gBACpDC,WAAWva,wBAAwBwa,gBAAgB;gBACnDxR,iBAAiB;YACnB;QACF;IACF,EAAE,OAAM;IACN,8DAA8D;IAC9D,gEAAgE;IAChE,oCAAoC;IACtC;IAEA,0EAA0E;IAC1E,2EAA2E;IAC3EvB,kBAAkBmJ,IAAAA,wCAA6B;IAC/C,OAAO,IAAIrF,QAAQ,CAACkP;QAClBX,eAAeY,IAAI,CAACD;IACtB;AACF;AAEA,MAAMlX,uBAAuB,OAC3B/H,MACAX;IAEA,MAAM,EACJ8f,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGC,IAAAA,gCAAe,EAACrf;IAEpB,IAAI8H;IACJ,IAAIsX,mBAAmB;QACrB,MAAM,GAAGE,OAAO,GAAG,MAAMC,IAAAA,gEAA+B,EAAC;YACvDlgB;YACAmgB,UAAUJ,iBAAiB,CAAC,EAAE;YAC9BK,cAAcL,iBAAiB,CAAC,EAAE;YAClCvd,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAgG,oBAAoBwX;IACtB;IAEA,OAAOxX;AACT;AAEA,eAAe2U,mBACbiD,kBAA0B,EAC1Bha,cAA8B,EAC9B0F,YAA2B,EAC3BjK,UAAsB;IAEtB,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAMqD,0BAA0BrD,WAAWqD,uBAAuB;IAClE,IACE,CAACA,2BACDrD,WAAW8C,YAAY,CAACzH,iBAAiB,KAAK,MAC9C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMmjB,gBAAgB7b,QAAQC,GAAG,CAACqI,YAAY,KAAK;IACnD,MAAMyS,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWY,gBACPnb,wBAAwBob,oBAAoB,GAC5Cpb,wBAAwBqb,gBAAgB;QAC5CrS,iBAAiB;IACnB;IAEA,MAAMsS,YAAYpa,eAAeU,KAAK;IACtC,OAAO,MAAMgF,aAAaqR,kBAAkB,CAC1CiD,oBACAI,WACAtb,wBAAwBK,aAAa,EACrCga;AAEJ"}