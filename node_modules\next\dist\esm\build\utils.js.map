{"version": 3, "sources": ["../../src/build/utils.ts"], "sourcesContent": ["import type { NextConfig, NextConfigComplete } from '../server/config-shared'\nimport type { ExperimentalPPRConfig } from '../server/lib/experimental/ppr'\nimport type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { AssetBinding } from './webpack/loaders/get-module-build-info'\nimport type {\n  GetStaticPaths,\n  GetStaticPathsResult,\n  PageConfig,\n  ServerRuntime,\n} from '../types'\nimport type { BuildManifest } from '../server/get-page-files'\nimport type {\n  Redirect,\n  Rewrite,\n  Header,\n  CustomRoutes,\n} from '../lib/load-custom-routes'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from './webpack/plugins/middleware-plugin'\nimport type { WebpackLayerName } from '../lib/constants'\nimport type { AppPageModule } from '../server/route-modules/app-page/module'\nimport type { RouteModule } from '../server/route-modules/route-module'\nimport type { NextComponentType } from '../shared/lib/utils'\n\nimport '../server/require-hook'\nimport '../server/node-polyfill-crypto'\nimport '../server/node-environment'\n\nimport {\n  green,\n  yellow,\n  red,\n  cyan,\n  white,\n  bold,\n  underline,\n} from '../lib/picocolors'\nimport getGzipSize from 'next/dist/compiled/gzip-size'\nimport textTable from 'next/dist/compiled/text-table'\nimport path from 'path'\nimport { promises as fs } from 'fs'\nimport { isValidElementType } from 'next/dist/compiled/react-is'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport browserslist from 'next/dist/compiled/browserslist'\nimport {\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  INSTRUMENTATION_HOOK_FILENAME,\n  WEBPACK_LAYERS,\n} from '../lib/constants'\nimport {\n  MODERN_BROWSERSLIST_TARGET,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../shared/lib/constants'\nimport prettyBytes from '../lib/pretty-bytes'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport escapePathDelimiters from '../shared/lib/router/utils/escape-path-delimiters'\nimport { findPageFile } from '../server/lib/find-page-file'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport * as Log from './output/log'\nimport { loadComponents } from '../server/load-components'\nimport type { LoadComponentsReturnType } from '../server/load-components'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getRuntimeContext } from '../server/web/sandbox'\nimport { isClientReference } from '../lib/client-reference'\nimport { createWorkStore } from '../server/async-storage/work-store'\nimport type { CacheHandler } from '../server/lib/incremental-cache'\nimport { IncrementalCache } from '../server/lib/incremental-cache'\nimport { nodeFs } from '../server/lib/node-fs-methods'\nimport * as ciEnvironment from '../server/ci-info'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { denormalizeAppPagePath } from '../shared/lib/page-path/denormalize-app-path'\nimport { RouteKind } from '../server/route-kind'\nimport { interopDefault } from '../lib/interop-default'\nimport type { PageExtensions } from './page-extensions-type'\nimport { formatDynamicImportPath } from '../lib/format-dynamic-import-path'\nimport { isInterceptionRouteAppPath } from '../server/lib/interception-routes'\nimport { checkIsRoutePPREnabled } from '../server/lib/experimental/ppr'\nimport type { Params } from '../server/request/params'\nimport { FallbackMode } from '../lib/fallback'\nimport {\n  fallbackModeToStaticPathsResult,\n  parseStaticPathsResult,\n} from '../lib/fallback'\nimport { getParamKeys } from '../server/request/fallback-params'\nimport type { OutgoingHttpHeaders } from 'http'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport type { AppSegment } from './segment-config/app/app-segments'\nimport { collectSegments } from './segment-config/app/app-segments'\nimport { createIncrementalCache } from '../export/helpers/create-incremental-cache'\nimport { AfterRunner } from '../server/after/run-with-after'\n\nexport type ROUTER_TYPE = 'pages' | 'app'\n\n// Use `print()` for expected console output\nconst print = console.log\n\nconst RESERVED_PAGE = /^\\/(_app|_error|_document|api(\\/|$))/\nconst fileGzipStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStatGzip = (file: string) => {\n  const cached = fileGzipStats[file]\n  if (cached) return cached\n  return (fileGzipStats[file] = getGzipSize.file(file))\n}\n\nconst fileSize = async (file: string) => (await fs.stat(file)).size\n\nconst fileStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStat = (file: string) => {\n  const cached = fileStats[file]\n  if (cached) return cached\n  return (fileStats[file] = fileSize(file))\n}\n\nexport function unique<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  return [...new Set([...main, ...sub])]\n}\n\nexport function difference<T>(\n  main: ReadonlyArray<T> | ReadonlySet<T>,\n  sub: ReadonlyArray<T> | ReadonlySet<T>\n): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...a].filter((x) => !b.has(x))\n}\n\n/**\n * Return an array of the items shared by both arrays.\n */\nfunction intersect<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...new Set([...a].filter((x) => b.has(x)))]\n}\n\nfunction sum(a: ReadonlyArray<number>): number {\n  return a.reduce((size, stat) => size + stat, 0)\n}\n\ntype ComputeFilesGroup = {\n  files: ReadonlyArray<string>\n  size: {\n    total: number\n  }\n}\n\ntype ComputeFilesManifest = {\n  unique: ComputeFilesGroup\n  common: ComputeFilesGroup\n}\n\ntype ComputeFilesManifestResult = {\n  router: {\n    pages: ComputeFilesManifest\n    app?: ComputeFilesManifest\n  }\n  sizes: Map<string, number>\n}\n\nlet cachedBuildManifest: BuildManifest | undefined\nlet cachedAppBuildManifest: AppBuildManifest | undefined\n\nlet lastCompute: ComputeFilesManifestResult | undefined\nlet lastComputePageInfo: boolean | undefined\n\nexport async function computeFromManifest(\n  manifests: {\n    build: BuildManifest\n    app?: AppBuildManifest\n  },\n  distPath: string,\n  gzipSize: boolean = true,\n  pageInfos?: Map<string, PageInfo>\n): Promise<ComputeFilesManifestResult> {\n  if (\n    Object.is(cachedBuildManifest, manifests.build) &&\n    lastComputePageInfo === !!pageInfos &&\n    Object.is(cachedAppBuildManifest, manifests.app)\n  ) {\n    return lastCompute!\n  }\n\n  // Determine the files that are in pages and app and count them, this will\n  // tell us if they are unique or common.\n\n  const countBuildFiles = (\n    map: Map<string, number>,\n    key: string,\n    manifest: Record<string, ReadonlyArray<string>>\n  ) => {\n    for (const file of manifest[key]) {\n      if (key === '/_app') {\n        map.set(file, Infinity)\n      } else if (map.has(file)) {\n        map.set(file, map.get(file)! + 1)\n      } else {\n        map.set(file, 1)\n      }\n    }\n  }\n\n  const files: {\n    pages: {\n      each: Map<string, number>\n      expected: number\n    }\n    app?: {\n      each: Map<string, number>\n      expected: number\n    }\n  } = {\n    pages: { each: new Map(), expected: 0 },\n  }\n\n  for (const key in manifests.build.pages) {\n    if (pageInfos) {\n      const pageInfo = pageInfos.get(key)\n      // don't include AMP pages since they don't rely on shared bundles\n      // AMP First pages are not under the pageInfos key\n      if (pageInfo?.isHybridAmp) {\n        continue\n      }\n    }\n\n    files.pages.expected++\n    countBuildFiles(files.pages.each, key, manifests.build.pages)\n  }\n\n  // Collect the build files form the app manifest.\n  if (manifests.app?.pages) {\n    files.app = { each: new Map<string, number>(), expected: 0 }\n\n    for (const key in manifests.app.pages) {\n      files.app.expected++\n      countBuildFiles(files.app.each, key, manifests.app.pages)\n    }\n  }\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n  const stats = new Map<string, number>()\n\n  // For all of the files in the pages and app manifests, compute the file size\n  // at once.\n\n  await Promise.all(\n    [\n      ...new Set<string>([\n        ...files.pages.each.keys(),\n        ...(files.app?.each.keys() ?? []),\n      ]),\n    ].map(async (f) => {\n      try {\n        // Add the file size to the stats.\n        stats.set(f, await getSize(path.join(distPath, f)))\n      } catch {}\n    })\n  )\n\n  const groupFiles = async (listing: {\n    each: Map<string, number>\n    expected: number\n  }): Promise<ComputeFilesManifest> => {\n    const entries = [...listing.each.entries()]\n\n    const shapeGroup = (group: [string, number][]): ComputeFilesGroup =>\n      group.reduce(\n        (acc, [f]) => {\n          acc.files.push(f)\n\n          const size = stats.get(f)\n          if (typeof size === 'number') {\n            acc.size.total += size\n          }\n\n          return acc\n        },\n        {\n          files: [] as string[],\n          size: {\n            total: 0,\n          },\n        }\n      )\n\n    return {\n      unique: shapeGroup(entries.filter(([, len]) => len === 1)),\n      common: shapeGroup(\n        entries.filter(\n          ([, len]) => len === listing.expected || len === Infinity\n        )\n      ),\n    }\n  }\n\n  lastCompute = {\n    router: {\n      pages: await groupFiles(files.pages),\n      app: files.app ? await groupFiles(files.app) : undefined,\n    },\n    sizes: stats,\n  }\n\n  cachedBuildManifest = manifests.build\n  cachedAppBuildManifest = manifests.app\n  lastComputePageInfo = !!pageInfos\n  return lastCompute!\n}\n\nexport function isMiddlewareFilename(file?: string | null) {\n  return file === MIDDLEWARE_FILENAME || file === `src/${MIDDLEWARE_FILENAME}`\n}\n\nexport function isInstrumentationHookFilename(file?: string | null) {\n  return (\n    file === INSTRUMENTATION_HOOK_FILENAME ||\n    file === `src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nconst filterAndSortList = (\n  list: ReadonlyArray<string>,\n  routeType: ROUTER_TYPE,\n  hasCustomApp: boolean\n) => {\n  let pages: string[]\n  if (routeType === 'app') {\n    // filter out static app route of /favicon.ico\n    pages = list.filter((e) => e !== '/favicon.ico')\n  } else {\n    // filter built-in pages\n    pages = list\n      .slice()\n      .filter(\n        (e) =>\n          !(\n            e === '/_document' ||\n            e === '/_error' ||\n            (!hasCustomApp && e === '/_app')\n          )\n      )\n  }\n  return pages.sort((a, b) => a.localeCompare(b))\n}\n\nexport interface PageInfo {\n  isHybridAmp?: boolean\n  size: number\n  totalSize: number\n  isStatic: boolean\n  isSSG: boolean\n  /**\n   * If true, it means that the route has partial prerendering enabled.\n   */\n  isRoutePPREnabled: boolean\n  ssgPageRoutes: string[] | null\n  initialRevalidateSeconds: number | false\n  pageDuration: number | undefined\n  ssgPageDurations: number[] | undefined\n  runtime: ServerRuntime\n  hasEmptyPrelude?: boolean\n  hasPostponed?: boolean\n  isDynamicAppRoute?: boolean\n}\n\nexport type PageInfos = Map<string, PageInfo>\n\nexport interface RoutesUsingEdgeRuntime {\n  [route: string]: 0\n}\n\nexport function collectRoutesUsingEdgeRuntime(\n  input: PageInfos\n): RoutesUsingEdgeRuntime {\n  const routesUsingEdgeRuntime: RoutesUsingEdgeRuntime = {}\n  for (const [route, info] of input.entries()) {\n    if (isEdgeRuntime(info.runtime)) {\n      routesUsingEdgeRuntime[route] = 0\n    }\n  }\n\n  return routesUsingEdgeRuntime\n}\n\nexport async function printTreeView(\n  lists: {\n    pages: ReadonlyArray<string>\n    app: ReadonlyArray<string> | undefined\n  },\n  pageInfos: Map<string, PageInfo>,\n  {\n    distPath,\n    buildId,\n    pagesDir,\n    pageExtensions,\n    buildManifest,\n    appBuildManifest,\n    middlewareManifest,\n    useStaticPages404,\n    gzipSize = true,\n  }: {\n    distPath: string\n    buildId: string\n    pagesDir?: string\n    pageExtensions: PageExtensions\n    buildManifest: BuildManifest\n    appBuildManifest?: AppBuildManifest\n    middlewareManifest: MiddlewareManifest\n    useStaticPages404: boolean\n    gzipSize?: boolean\n  }\n) {\n  const getPrettySize = (_size: number): string => {\n    const size = prettyBytes(_size)\n    return white(bold(size))\n  }\n\n  const MIN_DURATION = 300\n  const getPrettyDuration = (_duration: number): string => {\n    const duration = `${_duration} ms`\n    // green for 300-1000ms\n    if (_duration < 1000) return green(duration)\n    // yellow for 1000-2000ms\n    if (_duration < 2000) return yellow(duration)\n    // red for >= 2000ms\n    return red(bold(duration))\n  }\n\n  const getCleanName = (fileName: string) =>\n    fileName\n      // Trim off `static/`\n      .replace(/^static\\//, '')\n      // Re-add `static/` for root files\n      .replace(/^<buildId>/, 'static')\n      // Remove file hash\n      .replace(/(?:^|[.-])([0-9a-z]{6})[0-9a-z]{14}(?=\\.)/, '.$1')\n\n  // Check if we have a custom app.\n  const hasCustomApp = !!(\n    pagesDir && (await findPageFile(pagesDir, '/_app', pageExtensions, false))\n  )\n\n  // Collect all the symbols we use so we can print the icons out.\n  const usedSymbols = new Set()\n\n  const messages: [string, string, string][] = []\n\n  const stats = await computeFromManifest(\n    { build: buildManifest, app: appBuildManifest },\n    distPath,\n    gzipSize,\n    pageInfos\n  )\n\n  const printFileTree = async ({\n    list,\n    routerType,\n  }: {\n    list: ReadonlyArray<string>\n    routerType: ROUTER_TYPE\n  }) => {\n    const filteredPages = filterAndSortList(list, routerType, hasCustomApp)\n    if (filteredPages.length === 0) {\n      return\n    }\n\n    messages.push(\n      [\n        routerType === 'app' ? 'Route (app)' : 'Route (pages)',\n        'Size',\n        'First Load JS',\n      ].map((entry) => underline(entry)) as [string, string, string]\n    )\n\n    filteredPages.forEach((item, i, arr) => {\n      const border =\n        i === 0\n          ? arr.length === 1\n            ? '─'\n            : '┌'\n          : i === arr.length - 1\n            ? '└'\n            : '├'\n\n      const pageInfo = pageInfos.get(item)\n      const ampFirst = buildManifest.ampFirstPages.includes(item)\n      const totalDuration =\n        (pageInfo?.pageDuration || 0) +\n        (pageInfo?.ssgPageDurations?.reduce((a, b) => a + (b || 0), 0) || 0)\n\n      let symbol: string\n\n      if (item === '/_app' || item === '/_app.server') {\n        symbol = ' '\n      } else if (isEdgeRuntime(pageInfo?.runtime)) {\n        symbol = 'ƒ'\n      } else if (pageInfo?.isRoutePPREnabled) {\n        if (\n          // If the page has an empty prelude, then it's equivalent to a dynamic page\n          pageInfo?.hasEmptyPrelude ||\n          // ensure we don't mark dynamic paths that postponed as being dynamic\n          // since in this case we're able to partially prerender it\n          (pageInfo.isDynamicAppRoute && !pageInfo.hasPostponed)\n        ) {\n          symbol = 'ƒ'\n        } else if (!pageInfo?.hasPostponed) {\n          symbol = '○'\n        } else {\n          symbol = '◐'\n        }\n      } else if (pageInfo?.isStatic) {\n        symbol = '○'\n      } else if (pageInfo?.isSSG) {\n        symbol = '●'\n      } else {\n        symbol = 'ƒ'\n      }\n\n      usedSymbols.add(symbol)\n\n      if (pageInfo?.initialRevalidateSeconds) usedSymbols.add('ISR')\n\n      messages.push([\n        `${border} ${symbol} ${\n          pageInfo?.initialRevalidateSeconds\n            ? `${item} (ISR: ${pageInfo?.initialRevalidateSeconds} Seconds)`\n            : item\n        }${\n          totalDuration > MIN_DURATION\n            ? ` (${getPrettyDuration(totalDuration)})`\n            : ''\n        }`,\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? prettyBytes(pageInfo.size)\n              : ''\n          : '',\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? getPrettySize(pageInfo.totalSize)\n              : ''\n          : '',\n      ])\n\n      const uniqueCssFiles =\n        buildManifest.pages[item]?.filter(\n          (file) =>\n            file.endsWith('.css') &&\n            stats.router[routerType]?.unique.files.includes(file)\n        ) || []\n\n      if (uniqueCssFiles.length > 0) {\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        uniqueCssFiles.forEach((file, index, { length }) => {\n          const innerSymbol = index === length - 1 ? '└' : '├'\n          const size = stats.sizes.get(file)\n          messages.push([\n            `${contSymbol}   ${innerSymbol} ${getCleanName(file)}`,\n            typeof size === 'number' ? prettyBytes(size) : '',\n            '',\n          ])\n        })\n      }\n\n      if (pageInfo?.ssgPageRoutes?.length) {\n        const totalRoutes = pageInfo.ssgPageRoutes.length\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        let routes: { route: string; duration: number; avgDuration?: number }[]\n        if (\n          pageInfo.ssgPageDurations &&\n          pageInfo.ssgPageDurations.some((d) => d > MIN_DURATION)\n        ) {\n          const previewPages = totalRoutes === 8 ? 8 : Math.min(totalRoutes, 7)\n          const routesWithDuration = pageInfo.ssgPageRoutes\n            .map((route, idx) => ({\n              route,\n              duration: pageInfo.ssgPageDurations![idx] || 0,\n            }))\n            .sort(({ duration: a }, { duration: b }) =>\n              // Sort by duration\n              // keep too small durations in original order at the end\n              a <= MIN_DURATION && b <= MIN_DURATION ? 0 : b - a\n            )\n          routes = routesWithDuration.slice(0, previewPages)\n          const remainingRoutes = routesWithDuration.slice(previewPages)\n          if (remainingRoutes.length) {\n            const remaining = remainingRoutes.length\n            const avgDuration = Math.round(\n              remainingRoutes.reduce(\n                (total, { duration }) => total + duration,\n                0\n              ) / remainingRoutes.length\n            )\n            routes.push({\n              route: `[+${remaining} more paths]`,\n              duration: 0,\n              avgDuration,\n            })\n          }\n        } else {\n          const previewPages = totalRoutes === 4 ? 4 : Math.min(totalRoutes, 3)\n          routes = pageInfo.ssgPageRoutes\n            .slice(0, previewPages)\n            .map((route) => ({ route, duration: 0 }))\n          if (totalRoutes > previewPages) {\n            const remaining = totalRoutes - previewPages\n            routes.push({ route: `[+${remaining} more paths]`, duration: 0 })\n          }\n        }\n\n        routes.forEach(\n          ({ route, duration, avgDuration }, index, { length }) => {\n            const innerSymbol = index === length - 1 ? '└' : '├'\n            messages.push([\n              `${contSymbol}   ${innerSymbol} ${route}${\n                duration > MIN_DURATION\n                  ? ` (${getPrettyDuration(duration)})`\n                  : ''\n              }${\n                avgDuration && avgDuration > MIN_DURATION\n                  ? ` (avg ${getPrettyDuration(avgDuration)})`\n                  : ''\n              }`,\n              '',\n              '',\n            ])\n          }\n        )\n      }\n    })\n\n    const sharedFilesSize = stats.router[routerType]?.common.size.total\n    const sharedFiles = stats.router[routerType]?.common.files ?? []\n\n    messages.push([\n      '+ First Load JS shared by all',\n      typeof sharedFilesSize === 'number' ? getPrettySize(sharedFilesSize) : '',\n      '',\n    ])\n    const sharedCssFiles: string[] = []\n    const sharedJsChunks = [\n      ...sharedFiles\n        .filter((file) => {\n          if (file.endsWith('.css')) {\n            sharedCssFiles.push(file)\n            return false\n          }\n          return true\n        })\n        .map((e) => e.replace(buildId, '<buildId>'))\n        .sort(),\n      ...sharedCssFiles.map((e) => e.replace(buildId, '<buildId>')).sort(),\n    ]\n\n    // if the some chunk are less than 10kb or we don't know the size, we only show the total size of the rest\n    const tenKbLimit = 10 * 1000\n    let restChunkSize = 0\n    let restChunkCount = 0\n    sharedJsChunks.forEach((fileName, index, { length }) => {\n      const innerSymbol = index + restChunkCount === length - 1 ? '└' : '├'\n\n      const originalName = fileName.replace('<buildId>', buildId)\n      const cleanName = getCleanName(fileName)\n      const size = stats.sizes.get(originalName)\n\n      if (!size || size < tenKbLimit) {\n        restChunkCount++\n        restChunkSize += size || 0\n        return\n      }\n\n      messages.push([`  ${innerSymbol} ${cleanName}`, prettyBytes(size), ''])\n    })\n\n    if (restChunkCount > 0) {\n      messages.push([\n        `  └ other shared chunks (total)`,\n        prettyBytes(restChunkSize),\n        '',\n      ])\n    }\n  }\n\n  // If enabled, then print the tree for the app directory.\n  if (lists.app && stats.router.app) {\n    await printFileTree({\n      routerType: 'app',\n      list: lists.app,\n    })\n\n    messages.push(['', '', ''])\n  }\n\n  pageInfos.set('/404', {\n    ...(pageInfos.get('/404') || pageInfos.get('/_error'))!,\n    isStatic: useStaticPages404,\n  })\n\n  // If there's no app /_notFound page present, then the 404 is still using the pages/404\n  if (\n    !lists.pages.includes('/404') &&\n    !lists.app?.includes(UNDERSCORE_NOT_FOUND_ROUTE)\n  ) {\n    lists.pages = [...lists.pages, '/404']\n  }\n\n  // Print the tree view for the pages directory.\n  await printFileTree({\n    routerType: 'pages',\n    list: lists.pages,\n  })\n\n  const middlewareInfo = middlewareManifest.middleware?.['/']\n  if (middlewareInfo?.files.length > 0) {\n    const middlewareSizes = await Promise.all(\n      middlewareInfo.files\n        .map((dep) => `${distPath}/${dep}`)\n        .map(gzipSize ? fsStatGzip : fsStat)\n    )\n\n    messages.push(['', '', ''])\n    messages.push(['ƒ Middleware', getPrettySize(sum(middlewareSizes)), ''])\n  }\n\n  print(\n    textTable(messages, {\n      align: ['l', 'l', 'r'],\n      stringLength: (str) => stripAnsi(str).length,\n    })\n  )\n\n  const staticFunctionInfo =\n    lists.app && stats.router.app ? 'generateStaticParams' : 'getStaticProps'\n  print()\n  print(\n    textTable(\n      [\n        usedSymbols.has('○') && [\n          '○',\n          '(Static)',\n          'prerendered as static content',\n        ],\n        usedSymbols.has('●') && [\n          '●',\n          '(SSG)',\n          `prerendered as static HTML (uses ${cyan(staticFunctionInfo)})`,\n        ],\n        usedSymbols.has('ISR') && [\n          '',\n          '(ISR)',\n          `incremental static regeneration (uses revalidate in ${cyan(\n            staticFunctionInfo\n          )})`,\n        ],\n        usedSymbols.has('◐') && [\n          '◐',\n          '(Partial Prerender)',\n          'prerendered as static HTML with dynamic server-streamed content',\n        ],\n        usedSymbols.has('ƒ') && ['ƒ', '(Dynamic)', `server-rendered on demand`],\n      ].filter((x) => x) as [string, string, string][],\n      {\n        align: ['l', 'l', 'l'],\n        stringLength: (str) => stripAnsi(str).length,\n      }\n    )\n  )\n\n  print()\n}\n\nexport function printCustomRoutes({\n  redirects,\n  rewrites,\n  headers,\n}: CustomRoutes) {\n  const printRoutes = (\n    routes: Redirect[] | Rewrite[] | Header[],\n    type: 'Redirects' | 'Rewrites' | 'Headers'\n  ) => {\n    const isRedirects = type === 'Redirects'\n    const isHeaders = type === 'Headers'\n    print(underline(type))\n\n    /*\n        ┌ source\n        ├ permanent/statusCode\n        └ destination\n     */\n    const routesStr = (routes as any[])\n      .map((route: { source: string }) => {\n        let routeStr = `┌ source: ${route.source}\\n`\n\n        if (!isHeaders) {\n          const r = route as Rewrite\n          routeStr += `${isRedirects ? '├' : '└'} destination: ${\n            r.destination\n          }\\n`\n        }\n        if (isRedirects) {\n          const r = route as Redirect\n          routeStr += `└ ${\n            r.statusCode\n              ? `status: ${r.statusCode}`\n              : `permanent: ${r.permanent}`\n          }\\n`\n        }\n\n        if (isHeaders) {\n          const r = route as Header\n          routeStr += `└ headers:\\n`\n\n          for (let i = 0; i < r.headers.length; i++) {\n            const header = r.headers[i]\n            const last = i === headers.length - 1\n\n            routeStr += `  ${last ? '└' : '├'} ${header.key}: ${header.value}\\n`\n          }\n        }\n\n        return routeStr\n      })\n      .join('\\n')\n\n    print(`${routesStr}\\n`)\n  }\n\n  print()\n  if (redirects.length) {\n    printRoutes(redirects, 'Redirects')\n  }\n  if (headers.length) {\n    printRoutes(headers, 'Headers')\n  }\n\n  const combinedRewrites = [\n    ...rewrites.beforeFiles,\n    ...rewrites.afterFiles,\n    ...rewrites.fallback,\n  ]\n  if (combinedRewrites.length) {\n    printRoutes(combinedRewrites, 'Rewrites')\n  }\n}\n\nexport async function getJsPageSizeInKb(\n  routerType: ROUTER_TYPE,\n  page: string,\n  distPath: string,\n  buildManifest: BuildManifest,\n  appBuildManifest?: AppBuildManifest,\n  gzipSize: boolean = true,\n  cachedStats?: ComputeFilesManifestResult\n): Promise<[number, number]> {\n  const pageManifest = routerType === 'pages' ? buildManifest : appBuildManifest\n  if (!pageManifest) {\n    throw new Error('expected appBuildManifest with an \"app\" pageType')\n  }\n\n  // Normalize appBuildManifest keys\n  if (routerType === 'app') {\n    pageManifest.pages = Object.entries(pageManifest.pages).reduce(\n      (acc: Record<string, string[]>, [key, value]) => {\n        const newKey = normalizeAppPath(key)\n        acc[newKey] = value as string[]\n        return acc\n      },\n      {}\n    )\n  }\n\n  // If stats was not provided, then compute it again.\n  const stats =\n    cachedStats ??\n    (await computeFromManifest(\n      { build: buildManifest, app: appBuildManifest },\n      distPath,\n      gzipSize\n    ))\n\n  const pageData = stats.router[routerType]\n  if (!pageData) {\n    // This error shouldn't happen and represents an error in Next.js.\n    throw new Error('expected \"app\" manifest data with an \"app\" pageType')\n  }\n\n  const pagePath =\n    routerType === 'pages'\n      ? denormalizePagePath(page)\n      : denormalizeAppPagePath(page)\n\n  const fnFilterJs = (entry: string) => entry.endsWith('.js')\n\n  const pageFiles = (pageManifest.pages[pagePath] ?? []).filter(fnFilterJs)\n  const appFiles = (pageManifest.pages['/_app'] ?? []).filter(fnFilterJs)\n\n  const fnMapRealPath = (dep: string) => `${distPath}/${dep}`\n\n  const allFilesReal = unique(pageFiles, appFiles).map(fnMapRealPath)\n  const selfFilesReal = difference(\n    // Find the files shared by the pages files and the unique files...\n    intersect(pageFiles, pageData.unique.files),\n    // but without the common files.\n    pageData.common.files\n  ).map(fnMapRealPath)\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  // Try to get the file size from the page data if available, otherwise do a\n  // raw compute.\n  const getCachedSize = async (file: string) => {\n    const key = file.slice(distPath.length + 1)\n    const size: number | undefined = stats.sizes.get(key)\n\n    // If the size wasn't in the stats bundle, then get it from the file\n    // directly.\n    if (typeof size !== 'number') {\n      return getSize(file)\n    }\n\n    return size\n  }\n\n  try {\n    // Doesn't use `Promise.all`, as we'd double compute duplicate files. This\n    // function is memoized, so the second one will instantly resolve.\n    const allFilesSize = sum(await Promise.all(allFilesReal.map(getCachedSize)))\n    const selfFilesSize = sum(\n      await Promise.all(selfFilesReal.map(getCachedSize))\n    )\n\n    return [selfFilesSize, allFilesSize]\n  } catch {}\n  return [-1, -1]\n}\n\ntype StaticPrerenderedRoute = {\n  path: string\n  encoded: string\n  fallbackRouteParams: undefined\n}\n\ntype FallbackPrerenderedRoute = {\n  path: string\n  encoded: string\n  fallbackRouteParams: readonly string[]\n}\n\nexport type PrerenderedRoute = StaticPrerenderedRoute | FallbackPrerenderedRoute\n\nexport type StaticPathsResult = {\n  fallbackMode: FallbackMode\n  prerenderedRoutes: PrerenderedRoute[]\n}\n\nexport async function buildStaticPaths({\n  page,\n  getStaticPaths,\n  staticPathsResult,\n  configFileName,\n  locales,\n  defaultLocale,\n  appDir,\n}: {\n  page: string\n  getStaticPaths?: GetStaticPaths\n  staticPathsResult?: GetStaticPathsResult\n  configFileName: string\n  locales?: string[]\n  defaultLocale?: string\n  appDir?: boolean\n}): Promise<StaticPathsResult> {\n  const prerenderedRoutes: PrerenderedRoute[] = []\n  const _routeRegex = getRouteRegex(page)\n  const _routeMatcher = getRouteMatcher(_routeRegex)\n\n  // Get the default list of allowed params.\n  const routeParameterKeys = Object.keys(_routeMatcher(page))\n\n  if (!staticPathsResult) {\n    if (getStaticPaths) {\n      staticPathsResult = await getStaticPaths({ locales, defaultLocale })\n    } else {\n      throw new Error(\n        `invariant: attempted to buildStaticPaths without \"staticPathsResult\" or \"getStaticPaths\" ${page}`\n      )\n    }\n  }\n\n  const expectedReturnVal =\n    `Expected: { paths: [], fallback: boolean }\\n` +\n    `See here for more info: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n\n  if (\n    !staticPathsResult ||\n    typeof staticPathsResult !== 'object' ||\n    Array.isArray(staticPathsResult)\n  ) {\n    throw new Error(\n      `Invalid value returned from getStaticPaths in ${page}. Received ${typeof staticPathsResult} ${expectedReturnVal}`\n    )\n  }\n\n  const invalidStaticPathKeys = Object.keys(staticPathsResult).filter(\n    (key) => !(key === 'paths' || key === 'fallback')\n  )\n\n  if (invalidStaticPathKeys.length > 0) {\n    throw new Error(\n      `Extra keys returned from getStaticPaths in ${page} (${invalidStaticPathKeys.join(\n        ', '\n      )}) ${expectedReturnVal}`\n    )\n  }\n\n  if (\n    !(\n      typeof staticPathsResult.fallback === 'boolean' ||\n      staticPathsResult.fallback === 'blocking'\n    )\n  ) {\n    throw new Error(\n      `The \\`fallback\\` key must be returned from getStaticPaths in ${page}.\\n` +\n        expectedReturnVal\n    )\n  }\n\n  const toPrerender = staticPathsResult.paths\n\n  if (!Array.isArray(toPrerender)) {\n    throw new Error(\n      `Invalid \\`paths\\` value returned from getStaticPaths in ${page}.\\n` +\n        `\\`paths\\` must be an array of strings or objects of shape { params: [key: string]: string }`\n    )\n  }\n\n  toPrerender.forEach((entry) => {\n    // For a string-provided path, we must make sure it matches the dynamic\n    // route.\n    if (typeof entry === 'string') {\n      entry = removeTrailingSlash(entry)\n\n      const localePathResult = normalizeLocalePath(entry, locales)\n      let cleanedEntry = entry\n\n      if (localePathResult.detectedLocale) {\n        cleanedEntry = entry.slice(localePathResult.detectedLocale.length + 1)\n      } else if (defaultLocale) {\n        entry = `/${defaultLocale}${entry}`\n      }\n\n      const result = _routeMatcher(cleanedEntry)\n      if (!result) {\n        throw new Error(\n          `The provided path \\`${cleanedEntry}\\` does not match the page: \\`${page}\\`.`\n        )\n      }\n\n      // If leveraging the string paths variant the entry should already be\n      // encoded so we decode the segments ensuring we only escape path\n      // delimiters\n      prerenderedRoutes.push({\n        path: entry\n          .split('/')\n          .map((segment) =>\n            escapePathDelimiters(decodeURIComponent(segment), true)\n          )\n          .join('/'),\n        encoded: entry,\n        fallbackRouteParams: undefined,\n      })\n    }\n    // For the object-provided path, we must make sure it specifies all\n    // required keys.\n    else {\n      const invalidKeys = Object.keys(entry).filter(\n        (key) => key !== 'params' && key !== 'locale'\n      )\n\n      if (invalidKeys.length) {\n        throw new Error(\n          `Additional keys were returned from \\`getStaticPaths\\` in page \"${page}\". ` +\n            `URL Parameters intended for this dynamic route must be nested under the \\`params\\` key, i.e.:` +\n            `\\n\\n\\treturn { params: { ${routeParameterKeys\n              .map((k) => `${k}: ...`)\n              .join(', ')} } }` +\n            `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.\\n`\n        )\n      }\n\n      const { params = {} } = entry\n      let builtPage = page\n      let encodedBuiltPage = page\n\n      routeParameterKeys.forEach((validParamKey) => {\n        const { repeat, optional } = _routeRegex.groups[validParamKey]\n        let paramValue = params[validParamKey]\n        if (\n          optional &&\n          params.hasOwnProperty(validParamKey) &&\n          (paramValue === null ||\n            paramValue === undefined ||\n            (paramValue as any) === false)\n        ) {\n          paramValue = []\n        }\n        if (\n          (repeat && !Array.isArray(paramValue)) ||\n          (!repeat && typeof paramValue !== 'string')\n        ) {\n          // If this is from app directory, and not all params were provided,\n          // then filter this out.\n          if (appDir && typeof paramValue === 'undefined') {\n            builtPage = ''\n            encodedBuiltPage = ''\n            return\n          }\n\n          throw new Error(\n            `A required parameter (${validParamKey}) was not provided as ${\n              repeat ? 'an array' : 'a string'\n            } received ${typeof paramValue} in ${\n              appDir ? 'generateStaticParams' : 'getStaticPaths'\n            } for ${page}`\n          )\n        }\n        let replaced = `[${repeat ? '...' : ''}${validParamKey}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n        builtPage = builtPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[])\n                  .map((segment) => escapePathDelimiters(segment, true))\n                  .join('/')\n              : escapePathDelimiters(paramValue as string, true)\n          )\n          .replace(/\\\\/g, '/')\n          .replace(/(?!^)\\/$/, '')\n\n        encodedBuiltPage = encodedBuiltPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[]).map(encodeURIComponent).join('/')\n              : encodeURIComponent(paramValue as string)\n          )\n          .replace(/\\\\/g, '/')\n          .replace(/(?!^)\\/$/, '')\n      })\n\n      if (!builtPage && !encodedBuiltPage) {\n        return\n      }\n\n      if (entry.locale && !locales?.includes(entry.locale)) {\n        throw new Error(\n          `Invalid locale returned from getStaticPaths for ${page}, the locale ${entry.locale} is not specified in ${configFileName}`\n        )\n      }\n      const curLocale = entry.locale || defaultLocale || ''\n\n      prerenderedRoutes.push({\n        path: `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && builtPage === '/' ? '' : builtPage\n        }`,\n        encoded: `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && encodedBuiltPage === '/' ? '' : encodedBuiltPage\n        }`,\n        fallbackRouteParams: undefined,\n      })\n    }\n  })\n\n  const seen = new Set<string>()\n\n  return {\n    fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n    prerenderedRoutes: prerenderedRoutes.filter((route) => {\n      if (seen.has(route.path)) return false\n\n      // Filter out duplicate paths.\n      seen.add(route.path)\n      return true\n    }),\n  }\n}\n\nexport type PartialStaticPathsResult = {\n  [P in keyof StaticPathsResult]: StaticPathsResult[P] | undefined\n}\n\nexport async function buildAppStaticPaths({\n  dir,\n  page,\n  distDir,\n  dynamicIO,\n  authInterrupts,\n  configFileName,\n  segments,\n  isrFlushToDisk,\n  cacheHandler,\n  cacheLifeProfiles,\n  requestHeaders,\n  maxMemoryCacheSize,\n  fetchCacheKeyPrefix,\n  nextConfigOutput,\n  ComponentMod,\n  isRoutePPREnabled,\n  buildId,\n}: {\n  dir: string\n  page: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  configFileName: string\n  segments: AppSegment[]\n  distDir: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  cacheHandler?: string\n  cacheLifeProfiles?: {\n    [profile: string]: import('../server/use-cache/cache-life').CacheLife\n  }\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  ComponentMod: AppPageModule\n  isRoutePPREnabled: boolean | undefined\n  buildId: string\n}): Promise<PartialStaticPathsResult> {\n  if (\n    segments.some((generate) => generate.config?.dynamicParams === true) &&\n    nextConfigOutput === 'export'\n  ) {\n    throw new Error(\n      '\"dynamicParams: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/app/building-your-application/deploying/static-exports'\n    )\n  }\n\n  ComponentMod.patchFetch()\n\n  let CurCacheHandler: typeof CacheHandler | undefined\n  if (cacheHandler) {\n    CurCacheHandler = interopDefault(\n      await import(formatDynamicImportPath(dir, cacheHandler)).then(\n        (mod) => mod.default || mod\n      )\n    )\n  }\n\n  const incrementalCache = new IncrementalCache({\n    fs: nodeFs,\n    dev: true,\n    dynamicIO,\n    flushToDisk: isrFlushToDisk,\n    serverDistDir: path.join(distDir, 'server'),\n    fetchCacheKeyPrefix,\n    maxMemoryCacheSize,\n    getPrerenderManifest: () => ({\n      version: -1 as any, // letting us know this doesn't conform to spec\n      routes: {},\n      dynamicRoutes: {},\n      notFoundRoutes: [],\n      preview: null as any, // `preview` is special case read in next-dev-server\n    }),\n    CurCacheHandler,\n    requestHeaders,\n    minimalMode: ciEnvironment.hasNextSupport,\n  })\n\n  const paramKeys = new Set<string>()\n\n  const staticParamKeys = new Set<string>()\n  for (const segment of segments) {\n    if (segment.param) {\n      paramKeys.add(segment.param)\n\n      if (segment.config?.dynamicParams === false) {\n        staticParamKeys.add(segment.param)\n      }\n    }\n  }\n\n  const afterRunner = new AfterRunner()\n\n  const store = createWorkStore({\n    page,\n    // We're discovering the parameters here, so we don't have any unknown\n    // ones.\n    fallbackRouteParams: null,\n    renderOpts: {\n      incrementalCache,\n      cacheLifeProfiles,\n      supportsDynamicResponse: true,\n      isRevalidate: false,\n      experimental: {\n        dynamicIO,\n        authInterrupts,\n      },\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n      buildId,\n    },\n  })\n\n  const routeParams = await ComponentMod.workAsyncStorage.run(\n    store,\n    async () => {\n      async function builtRouteParams(\n        parentsParams: Params[] = [],\n        idx = 0\n      ): Promise<Params[]> {\n        // If we don't have any more to process, then we're done.\n        if (idx === segments.length) return parentsParams\n\n        const current = segments[idx]\n\n        if (\n          typeof current.generateStaticParams !== 'function' &&\n          idx < segments.length\n        ) {\n          return builtRouteParams(parentsParams, idx + 1)\n        }\n\n        const params: Params[] = []\n\n        if (current.generateStaticParams) {\n          // fetchCache can be used to inform the fetch() defaults used inside\n          // of generateStaticParams. revalidate and dynamic options don't come into\n          // play within generateStaticParams.\n          if (typeof current.config?.fetchCache !== 'undefined') {\n            store.fetchCache = current.config.fetchCache\n          }\n\n          if (parentsParams.length > 0) {\n            for (const parentParams of parentsParams) {\n              const result = await current.generateStaticParams({\n                params: parentParams,\n              })\n\n              for (const item of result) {\n                params.push({ ...parentParams, ...item })\n              }\n            }\n          } else {\n            const result = await current.generateStaticParams({ params: {} })\n\n            params.push(...result)\n          }\n        }\n\n        if (idx < segments.length) {\n          return builtRouteParams(params, idx + 1)\n        }\n\n        return params\n      }\n\n      return builtRouteParams()\n    }\n  )\n\n  let lastDynamicSegmentHadGenerateStaticParams = false\n  for (const segment of segments) {\n    // Check to see if there are any missing params for segments that have\n    // dynamicParams set to false.\n    if (\n      segment.param &&\n      segment.isDynamicSegment &&\n      segment.config?.dynamicParams === false\n    ) {\n      for (const params of routeParams) {\n        if (segment.param in params) continue\n\n        const relative = segment.filePath\n          ? path.relative(dir, segment.filePath)\n          : undefined\n\n        throw new Error(\n          `Segment \"${relative}\" exports \"dynamicParams: false\" but the param \"${segment.param}\" is missing from the generated route params.`\n        )\n      }\n    }\n\n    if (\n      segment.isDynamicSegment &&\n      typeof segment.generateStaticParams !== 'function'\n    ) {\n      lastDynamicSegmentHadGenerateStaticParams = false\n    } else if (typeof segment.generateStaticParams === 'function') {\n      lastDynamicSegmentHadGenerateStaticParams = true\n    }\n  }\n\n  // Determine if all the segments have had their parameters provided. If there\n  // was no dynamic parameters, then we've collected all the params.\n  const hadAllParamsGenerated =\n    paramKeys.size === 0 ||\n    (routeParams.length > 0 &&\n      routeParams.every((params) => {\n        for (const key of paramKeys) {\n          if (key in params) continue\n          return false\n        }\n        return true\n      }))\n\n  // TODO: dynamic params should be allowed to be granular per segment but\n  // we need additional information stored/leveraged in the prerender\n  // manifest to allow this behavior.\n  const dynamicParams = segments.every(\n    (segment) => segment.config?.dynamicParams !== false\n  )\n\n  const supportsRoutePreGeneration =\n    hadAllParamsGenerated || process.env.NODE_ENV === 'production'\n\n  const fallbackMode = dynamicParams\n    ? supportsRoutePreGeneration\n      ? isRoutePPREnabled\n        ? FallbackMode.PRERENDER\n        : FallbackMode.BLOCKING_STATIC_RENDER\n      : undefined\n    : FallbackMode.NOT_FOUND\n\n  let result: PartialStaticPathsResult = {\n    fallbackMode,\n    prerenderedRoutes: lastDynamicSegmentHadGenerateStaticParams\n      ? []\n      : undefined,\n  }\n\n  if (hadAllParamsGenerated && fallbackMode) {\n    result = await buildStaticPaths({\n      staticPathsResult: {\n        fallback: fallbackModeToStaticPathsResult(fallbackMode),\n        paths: routeParams.map((params) => ({ params })),\n      },\n      page,\n      configFileName,\n      appDir: true,\n    })\n  }\n\n  // If the fallback mode is a prerender, we want to include the dynamic\n  // route in the prerendered routes too.\n  if (isRoutePPREnabled) {\n    result.prerenderedRoutes ??= []\n    result.prerenderedRoutes.unshift({\n      path: page,\n      encoded: page,\n      fallbackRouteParams: getParamKeys(page),\n    })\n  }\n\n  await afterRunner.executeAfter()\n\n  return result\n}\n\ntype PageIsStaticResult = {\n  isRoutePPREnabled?: boolean\n  isStatic?: boolean\n  isAmpOnly?: boolean\n  isHybridAmp?: boolean\n  hasServerProps?: boolean\n  hasStaticProps?: boolean\n  prerenderedRoutes: PrerenderedRoute[] | undefined\n  prerenderFallbackMode: FallbackMode | undefined\n  isNextImageImported?: boolean\n  traceIncludes?: string[]\n  traceExcludes?: string[]\n  appConfig?: AppSegmentConfig\n}\n\nexport async function isPageStatic({\n  dir,\n  page,\n  distDir,\n  configFileName,\n  runtimeEnvConfig,\n  httpAgentOptions,\n  locales,\n  defaultLocale,\n  parentId,\n  pageRuntime,\n  edgeInfo,\n  pageType,\n  dynamicIO,\n  authInterrupts,\n  originalAppPath,\n  isrFlushToDisk,\n  maxMemoryCacheSize,\n  nextConfigOutput,\n  cacheHandler,\n  cacheHandlers,\n  cacheLifeProfiles,\n  pprConfig,\n  buildId,\n}: {\n  dir: string\n  page: string\n  distDir: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  configFileName: string\n  runtimeEnvConfig: any\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  locales?: string[]\n  defaultLocale?: string\n  parentId?: any\n  edgeInfo?: any\n  pageType?: 'pages' | 'app'\n  pageRuntime?: ServerRuntime\n  originalAppPath?: string\n  isrFlushToDisk?: boolean\n  maxMemoryCacheSize?: number\n  cacheHandler?: string\n  cacheHandlers?: Record<string, string | undefined>\n  cacheLifeProfiles?: {\n    [profile: string]: import('../server/use-cache/cache-life').CacheLife\n  }\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  pprConfig: ExperimentalPPRConfig | undefined\n  buildId: string\n}): Promise<PageIsStaticResult> {\n  await createIncrementalCache({\n    cacheHandler,\n    cacheHandlers,\n    distDir,\n    dir,\n    dynamicIO,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const isPageStaticSpan = trace('is-page-static-utils', parentId)\n  return isPageStaticSpan\n    .traceAsyncFn(async (): Promise<PageIsStaticResult> => {\n      require('../shared/lib/runtime-config.external').setConfig(\n        runtimeEnvConfig\n      )\n      setHttpClientAndAgentOptions({\n        httpAgentOptions,\n      })\n\n      let componentsResult: LoadComponentsReturnType\n      let prerenderedRoutes: PrerenderedRoute[] | undefined\n      let prerenderFallbackMode: FallbackMode | undefined\n      let appConfig: AppSegmentConfig = {}\n      let isClientComponent: boolean = false\n      const pathIsEdgeRuntime = isEdgeRuntime(pageRuntime)\n\n      if (pathIsEdgeRuntime) {\n        const runtime = await getRuntimeContext({\n          paths: edgeInfo.files.map((file: string) => path.join(distDir, file)),\n          edgeFunctionEntry: {\n            ...edgeInfo,\n            wasm: (edgeInfo.wasm ?? []).map((binding: AssetBinding) => ({\n              ...binding,\n              filePath: path.join(distDir, binding.filePath),\n            })),\n          },\n          name: edgeInfo.name,\n          useCache: true,\n          distDir,\n        })\n        const mod = (\n          await runtime.context._ENTRIES[`middleware_${edgeInfo.name}`]\n        ).ComponentMod\n\n        // This is not needed during require.\n        const buildManifest = {} as BuildManifest\n\n        isClientComponent = isClientReference(mod)\n        componentsResult = {\n          Component: mod.default,\n          Document: mod.Document,\n          App: mod.App,\n          routeModule: mod.routeModule,\n          page,\n          ComponentMod: mod,\n          pageConfig: mod.config || {},\n          buildManifest,\n          reactLoadableManifest: {},\n          getServerSideProps: mod.getServerSideProps,\n          getStaticPaths: mod.getStaticPaths,\n          getStaticProps: mod.getStaticProps,\n        }\n      } else {\n        componentsResult = await loadComponents({\n          distDir,\n          page: originalAppPath || page,\n          isAppPath: pageType === 'app',\n          isDev: false,\n        })\n      }\n      const Comp = componentsResult.Component as NextComponentType | undefined\n      let staticPathsResult: GetStaticPathsResult | undefined\n\n      const routeModule: RouteModule = componentsResult.routeModule\n\n      let isRoutePPREnabled: boolean = false\n\n      if (pageType === 'app') {\n        const ComponentMod: AppPageModule = componentsResult.ComponentMod\n\n        isClientComponent = isClientReference(componentsResult.ComponentMod)\n\n        let segments\n        try {\n          segments = await collectSegments(componentsResult)\n        } catch (err) {\n          throw new Error(`Failed to collect configuration for ${page}`, {\n            cause: err,\n          })\n        }\n\n        appConfig = reduceAppConfig(await collectSegments(componentsResult))\n\n        if (appConfig.dynamic === 'force-static' && pathIsEdgeRuntime) {\n          Log.warn(\n            `Page \"${page}\" is using runtime = 'edge' which is currently incompatible with dynamic = 'force-static'. Please remove either \"runtime\" or \"force-static\" for correct behavior`\n          )\n        }\n\n        // A page supports partial prerendering if it is an app page and either\n        // the whole app has PPR enabled or this page has PPR enabled when we're\n        // in incremental mode.\n        isRoutePPREnabled =\n          routeModule.definition.kind === RouteKind.APP_PAGE &&\n          !isInterceptionRouteAppPath(page) &&\n          checkIsRoutePPREnabled(pprConfig, appConfig)\n\n        // If force dynamic was set and we don't have PPR enabled, then set the\n        // revalidate to 0.\n        // TODO: (PPR) remove this once PPR is enabled by default\n        if (appConfig.dynamic === 'force-dynamic' && !isRoutePPREnabled) {\n          appConfig.revalidate = 0\n        }\n\n        if (isDynamicRoute(page)) {\n          ;({ fallbackMode: prerenderFallbackMode, prerenderedRoutes } =\n            await buildAppStaticPaths({\n              dir,\n              page,\n              dynamicIO,\n              authInterrupts,\n              configFileName,\n              segments,\n              distDir,\n              requestHeaders: {},\n              isrFlushToDisk,\n              maxMemoryCacheSize,\n              cacheHandler,\n              cacheLifeProfiles,\n              ComponentMod,\n              nextConfigOutput,\n              isRoutePPREnabled,\n              buildId,\n            }))\n        }\n      } else {\n        if (!Comp || !isValidElementType(Comp) || typeof Comp === 'string') {\n          throw new Error('INVALID_DEFAULT_EXPORT')\n        }\n      }\n\n      const hasGetInitialProps = !!Comp?.getInitialProps\n      const hasStaticProps = !!componentsResult.getStaticProps\n      const hasStaticPaths = !!componentsResult.getStaticPaths\n      const hasServerProps = !!componentsResult.getServerSideProps\n\n      // A page cannot be prerendered _and_ define a data requirement. That's\n      // contradictory!\n      if (hasGetInitialProps && hasStaticProps) {\n        throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT)\n      }\n\n      if (hasGetInitialProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT)\n      }\n\n      if (hasStaticProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n\n      const pageIsDynamic = isDynamicRoute(page)\n      // A page cannot have static parameters if it is not a dynamic page.\n      if (hasStaticProps && hasStaticPaths && !pageIsDynamic) {\n        throw new Error(\n          `getStaticPaths can only be used with dynamic pages, not '${page}'.` +\n            `\\nLearn more: https://nextjs.org/docs/routing/dynamic-routes`\n        )\n      }\n\n      if (hasStaticProps && pageIsDynamic && !hasStaticPaths) {\n        throw new Error(\n          `getStaticPaths is required for dynamic SSG pages and is missing for '${page}'.` +\n            `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n        )\n      }\n\n      if ((hasStaticProps && hasStaticPaths) || staticPathsResult) {\n        ;({ fallbackMode: prerenderFallbackMode, prerenderedRoutes } =\n          await buildStaticPaths({\n            page,\n            locales,\n            defaultLocale,\n            configFileName,\n            staticPathsResult,\n            getStaticPaths: componentsResult.getStaticPaths!,\n          }))\n      }\n\n      const isNextImageImported = (globalThis as any).__NEXT_IMAGE_IMPORTED\n      const config: PageConfig = isClientComponent\n        ? {}\n        : componentsResult.pageConfig\n\n      let isStatic = false\n      if (!hasStaticProps && !hasGetInitialProps && !hasServerProps) {\n        isStatic = true\n      }\n\n      // When PPR is enabled, any route may be completely static, so\n      // mark this route as static.\n      if (isRoutePPREnabled) {\n        isStatic = true\n      }\n\n      return {\n        isStatic,\n        isRoutePPREnabled,\n        isHybridAmp: config.amp === 'hybrid',\n        isAmpOnly: config.amp === true,\n        prerenderFallbackMode,\n        prerenderedRoutes,\n        hasStaticProps,\n        hasServerProps,\n        isNextImageImported,\n        appConfig,\n      }\n    })\n    .catch((err) => {\n      if (err.message === 'INVALID_DEFAULT_EXPORT') {\n        throw err\n      }\n      console.error(err)\n      throw new Error(`Failed to collect page data for ${page}`)\n    })\n}\n\ntype ReducedAppConfig = Pick<\n  AppSegmentConfig,\n  | 'revalidate'\n  | 'dynamic'\n  | 'fetchCache'\n  | 'preferredRegion'\n  | 'experimental_ppr'\n  | 'runtime'\n  | 'maxDuration'\n>\n\n/**\n * Collect the app config from the generate param segments. This only gets a\n * subset of the config options.\n *\n * @param segments the generate param segments\n * @returns the reduced app config\n */\nexport function reduceAppConfig(\n  segments: Pick<AppSegment, 'config'>[]\n): ReducedAppConfig {\n  const config: ReducedAppConfig = {}\n\n  for (const segment of segments) {\n    const {\n      dynamic,\n      fetchCache,\n      preferredRegion,\n      revalidate,\n      experimental_ppr,\n      runtime,\n      maxDuration,\n    } = segment.config || {}\n\n    // TODO: should conflicting configs here throw an error\n    // e.g. if layout defines one region but page defines another\n\n    if (typeof preferredRegion !== 'undefined') {\n      config.preferredRegion = preferredRegion\n    }\n\n    if (typeof dynamic !== 'undefined') {\n      config.dynamic = dynamic\n    }\n\n    if (typeof fetchCache !== 'undefined') {\n      config.fetchCache = fetchCache\n    }\n\n    if (typeof revalidate !== 'undefined') {\n      config.revalidate = revalidate\n    }\n\n    // Any revalidate number overrides false, and shorter revalidate overrides\n    // longer (initially).\n    if (\n      typeof revalidate === 'number' &&\n      (typeof config.revalidate !== 'number' || revalidate < config.revalidate)\n    ) {\n      config.revalidate = revalidate\n    }\n\n    // If partial prerendering has been set, only override it if the current\n    // value is provided as it's resolved from root layout to leaf page.\n    if (typeof experimental_ppr !== 'undefined') {\n      config.experimental_ppr = experimental_ppr\n    }\n\n    if (typeof runtime !== 'undefined') {\n      config.runtime = runtime\n    }\n\n    if (typeof maxDuration !== 'undefined') {\n      config.maxDuration = maxDuration\n    }\n  }\n\n  return config\n}\n\nexport async function hasCustomGetInitialProps({\n  page,\n  distDir,\n  runtimeEnvConfig,\n  checkingApp,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n  checkingApp: boolean\n}): Promise<boolean> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n  })\n  let mod = components.ComponentMod\n\n  if (checkingApp) {\n    mod = (await mod._app) || mod.default || mod\n  } else {\n    mod = mod.default || mod\n  }\n  mod = await mod\n  return mod.getInitialProps !== mod.origGetInitialProps\n}\n\nexport async function getDefinedNamedExports({\n  page,\n  distDir,\n  runtimeEnvConfig,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n}): Promise<ReadonlyArray<string>> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n  })\n\n  return Object.keys(components.ComponentMod).filter((key) => {\n    return typeof components.ComponentMod[key] !== 'undefined'\n  })\n}\n\nexport function detectConflictingPaths(\n  combinedPages: string[],\n  ssgPages: Set<string>,\n  additionalGeneratedSSGPaths: Map<string, string[]>\n) {\n  const conflictingPaths = new Map<\n    string,\n    Array<{\n      path: string\n      page: string\n    }>\n  >()\n\n  const dynamicSsgPages = [...ssgPages].filter((page) => isDynamicRoute(page))\n  const additionalSsgPathsByPath: {\n    [page: string]: { [path: string]: string }\n  } = {}\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    additionalSsgPathsByPath[pathsPage] ||= {}\n    paths.forEach((curPath) => {\n      const currentPath = curPath.toLowerCase()\n      additionalSsgPathsByPath[pathsPage][currentPath] = curPath\n    })\n  })\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    paths.forEach((curPath) => {\n      const lowerPath = curPath.toLowerCase()\n      let conflictingPage = combinedPages.find(\n        (page) => page.toLowerCase() === lowerPath\n      )\n\n      if (conflictingPage) {\n        conflictingPaths.set(lowerPath, [\n          { path: curPath, page: pathsPage },\n          { path: conflictingPage, page: conflictingPage },\n        ])\n      } else {\n        let conflictingPath: string | undefined\n\n        conflictingPage = dynamicSsgPages.find((page) => {\n          if (page === pathsPage) return false\n\n          conflictingPath =\n            additionalGeneratedSSGPaths.get(page) == null\n              ? undefined\n              : additionalSsgPathsByPath[page][lowerPath]\n          return conflictingPath\n        })\n\n        if (conflictingPage && conflictingPath) {\n          conflictingPaths.set(lowerPath, [\n            { path: curPath, page: pathsPage },\n            { path: conflictingPath, page: conflictingPage },\n          ])\n        }\n      }\n    })\n  })\n\n  if (conflictingPaths.size > 0) {\n    let conflictingPathsOutput = ''\n\n    conflictingPaths.forEach((pathItems) => {\n      pathItems.forEach((pathItem, idx) => {\n        const isDynamic = pathItem.page !== pathItem.path\n\n        if (idx > 0) {\n          conflictingPathsOutput += 'conflicts with '\n        }\n\n        conflictingPathsOutput += `path: \"${pathItem.path}\"${\n          isDynamic ? ` from page: \"${pathItem.page}\" ` : ' '\n        }`\n      })\n      conflictingPathsOutput += '\\n'\n    })\n\n    Log.error(\n      'Conflicting paths returned from getStaticPaths, paths must be unique per page.\\n' +\n        'See more info here: https://nextjs.org/docs/messages/conflicting-ssg-paths\\n\\n' +\n        conflictingPathsOutput\n    )\n    process.exit(1)\n  }\n}\n\nexport async function copyTracedFiles(\n  dir: string,\n  distDir: string,\n  pageKeys: readonly string[],\n  appPageKeys: readonly string[] | undefined,\n  tracingRoot: string,\n  serverConfig: NextConfig,\n  middlewareManifest: MiddlewareManifest,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>\n) {\n  const outputPath = path.join(distDir, 'standalone')\n  let moduleType = false\n  const nextConfig = {\n    ...serverConfig,\n    distDir: `./${path.relative(dir, distDir)}`,\n  }\n  try {\n    const packageJsonPath = path.join(distDir, '../package.json')\n    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'))\n    moduleType = packageJson.type === 'module'\n  } catch {}\n  const copiedFiles = new Set()\n  await fs.rm(outputPath, { recursive: true, force: true })\n\n  async function handleTraceFiles(traceFilePath: string) {\n    const traceData = JSON.parse(await fs.readFile(traceFilePath, 'utf8')) as {\n      files: string[]\n    }\n    const copySema = new Sema(10, { capacity: traceData.files.length })\n    const traceFileDir = path.dirname(traceFilePath)\n\n    await Promise.all(\n      traceData.files.map(async (relativeFile) => {\n        await copySema.acquire()\n\n        const tracedFilePath = path.join(traceFileDir, relativeFile)\n        const fileOutputPath = path.join(\n          outputPath,\n          path.relative(tracingRoot, tracedFilePath)\n        )\n\n        if (!copiedFiles.has(fileOutputPath)) {\n          copiedFiles.add(fileOutputPath)\n\n          await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n          const symlink = await fs.readlink(tracedFilePath).catch(() => null)\n\n          if (symlink) {\n            try {\n              await fs.symlink(symlink, fileOutputPath)\n            } catch (e: any) {\n              if (e.code !== 'EEXIST') {\n                throw e\n              }\n            }\n          } else {\n            await fs.copyFile(tracedFilePath, fileOutputPath)\n          }\n        }\n\n        await copySema.release()\n      })\n    )\n  }\n\n  async function handleEdgeFunction(page: EdgeFunctionDefinition) {\n    async function handleFile(file: string) {\n      const originalPath = path.join(distDir, file)\n      const fileOutputPath = path.join(\n        outputPath,\n        path.relative(tracingRoot, distDir),\n        file\n      )\n      await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n      await fs.copyFile(originalPath, fileOutputPath)\n    }\n    await Promise.all([\n      page.files.map(handleFile),\n      page.wasm?.map((file) => handleFile(file.filePath)),\n      page.assets?.map((file) => handleFile(file.filePath)),\n    ])\n  }\n\n  const edgeFunctionHandlers: Promise<any>[] = []\n\n  for (const middleware of Object.values(middlewareManifest.middleware)) {\n    if (isMiddlewareFilename(middleware.name)) {\n      edgeFunctionHandlers.push(handleEdgeFunction(middleware))\n    }\n  }\n\n  for (const page of Object.values(middlewareManifest.functions)) {\n    edgeFunctionHandlers.push(handleEdgeFunction(page))\n  }\n\n  await Promise.all(edgeFunctionHandlers)\n\n  for (const page of pageKeys) {\n    if (middlewareManifest.functions.hasOwnProperty(page)) {\n      continue\n    }\n    const route = normalizePagePath(page)\n\n    if (staticPages.has(route)) {\n      continue\n    }\n\n    const pageFile = path.join(\n      distDir,\n      'server',\n      'pages',\n      `${normalizePagePath(page)}.js`\n    )\n    const pageTraceFile = `${pageFile}.nft.json`\n    await handleTraceFiles(pageTraceFile).catch((err) => {\n      if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      }\n    })\n  }\n\n  if (appPageKeys) {\n    for (const page of appPageKeys) {\n      if (middlewareManifest.functions.hasOwnProperty(page)) {\n        continue\n      }\n      const pageFile = path.join(distDir, 'server', 'app', `${page}.js`)\n      const pageTraceFile = `${pageFile}.nft.json`\n      await handleTraceFiles(pageTraceFile).catch((err) => {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      })\n    }\n  }\n\n  if (hasInstrumentationHook) {\n    await handleTraceFiles(\n      path.join(distDir, 'server', 'instrumentation.js.nft.json')\n    )\n  }\n\n  await handleTraceFiles(path.join(distDir, 'next-server.js.nft.json'))\n  const serverOutputPath = path.join(\n    outputPath,\n    path.relative(tracingRoot, dir),\n    'server.js'\n  )\n  await fs.mkdir(path.dirname(serverOutputPath), { recursive: true })\n\n  await fs.writeFile(\n    serverOutputPath,\n    `${\n      moduleType\n        ? `performance.mark('next-start');\nimport path from 'path'\nimport { fileURLToPath } from 'url'\nimport module from 'module'\nconst require = module.createRequire(import.meta.url)\nconst __dirname = fileURLToPath(new URL('.', import.meta.url))\n`\n        : `const path = require('path')`\n    }\n\nconst dir = path.join(__dirname)\n\nprocess.env.NODE_ENV = 'production'\nprocess.chdir(__dirname)\n\nconst currentPort = parseInt(process.env.PORT, 10) || 3000\nconst hostname = process.env.HOSTNAME || '0.0.0.0'\n\nlet keepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT, 10)\nconst nextConfig = ${JSON.stringify(nextConfig)}\n\nprocess.env.__NEXT_PRIVATE_STANDALONE_CONFIG = JSON.stringify(nextConfig)\n\nrequire('next')\nconst { startServer } = require('next/dist/server/lib/start-server')\n\nif (\n  Number.isNaN(keepAliveTimeout) ||\n  !Number.isFinite(keepAliveTimeout) ||\n  keepAliveTimeout < 0\n) {\n  keepAliveTimeout = undefined\n}\n\nstartServer({\n  dir,\n  isDev: false,\n  config: nextConfig,\n  hostname,\n  port: currentPort,\n  allowRetry: false,\n  keepAliveTimeout,\n}).catch((err) => {\n  console.error(err);\n  process.exit(1);\n});`\n  )\n}\n\nexport function isReservedPage(page: string) {\n  return RESERVED_PAGE.test(page)\n}\n\nexport function isAppBuiltinNotFoundPage(page: string) {\n  return /next[\\\\/]dist[\\\\/]client[\\\\/]components[\\\\/]not-found-error/.test(\n    page\n  )\n}\n\nexport function isCustomErrorPage(page: string) {\n  return page === '/404' || page === '/500'\n}\n\nexport function isMiddlewareFile(file: string) {\n  return (\n    file === `/${MIDDLEWARE_FILENAME}` || file === `/src/${MIDDLEWARE_FILENAME}`\n  )\n}\n\nexport function isInstrumentationHookFile(file: string) {\n  return (\n    file === `/${INSTRUMENTATION_HOOK_FILENAME}` ||\n    file === `/src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nexport function getPossibleInstrumentationHookFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  const files = []\n  for (const extension of extensions) {\n    files.push(\n      path.join(folder, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`),\n      path.join(folder, `src`, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`)\n    )\n  }\n\n  return files\n}\n\nexport function getPossibleMiddlewareFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  return extensions.map((extension) =>\n    path.join(folder, `${MIDDLEWARE_FILENAME}.${extension}`)\n  )\n}\n\nexport class NestedMiddlewareError extends Error {\n  constructor(\n    nestedFileNames: string[],\n    mainDir: string,\n    pagesOrAppDir: string\n  ) {\n    super(\n      `Nested Middleware is not allowed, found:\\n` +\n        `${nestedFileNames.map((file) => `pages${file}`).join('\\n')}\\n` +\n        `Please move your code to a single file at ${path.join(\n          path.posix.sep,\n          path.relative(mainDir, path.resolve(pagesOrAppDir, '..')),\n          'middleware'\n        )} instead.\\n` +\n        `Read More - https://nextjs.org/docs/messages/nested-middleware`\n    )\n  }\n}\n\nexport function getSupportedBrowsers(\n  dir: string,\n  isDevelopment: boolean\n): string[] {\n  let browsers: any\n  try {\n    const browsersListConfig = browserslist.loadConfig({\n      path: dir,\n      env: isDevelopment ? 'development' : 'production',\n    })\n    // Running `browserslist` resolves `extends` and other config features into a list of browsers\n    if (browsersListConfig && browsersListConfig.length > 0) {\n      browsers = browserslist(browsersListConfig)\n    }\n  } catch {}\n\n  // When user has browserslist use that target\n  if (browsers && browsers.length > 0) {\n    return browsers\n  }\n\n  // Uses modern browsers as the default.\n  return MODERN_BROWSERSLIST_TARGET\n}\n\nexport function isWebpackServerOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.serverOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackClientOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.clientOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackDefaultLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return layer === null || layer === undefined\n}\n\nexport function isWebpackBundledLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.bundled.includes(layer as any))\n}\n\nexport function isWebpackAppPagesLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.appPages.includes(layer as any))\n}\n\nexport function collectMeta({\n  status,\n  headers,\n}: {\n  status?: number\n  headers?: OutgoingHttpHeaders\n}): {\n  status?: number\n  headers?: Record<string, string>\n} {\n  const meta: {\n    status?: number\n    headers?: Record<string, string>\n  } = {}\n\n  if (status !== 200) {\n    meta.status = status\n  }\n\n  if (headers && Object.keys(headers).length) {\n    meta.headers = {}\n\n    // normalize header values as initialHeaders\n    // must be Record<string, string>\n    for (const key in headers) {\n      // set-cookie is already handled - the middleware cookie setting case\n      // isn't needed for the prerender manifest since it can't read cookies\n      if (key === 'x-middleware-set-cookie') continue\n\n      let value = headers[key]\n\n      if (Array.isArray(value)) {\n        if (key === 'set-cookie') {\n          value = value.join(',')\n        } else {\n          value = value[value.length - 1]\n        }\n      }\n\n      if (typeof value === 'string') {\n        meta.headers[key] = value\n      }\n    }\n  }\n\n  return meta\n}\n"], "names": ["green", "yellow", "red", "cyan", "white", "bold", "underline", "getGzipSize", "textTable", "path", "promises", "fs", "isValidElementType", "stripAnsi", "browserslist", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "MIDDLEWARE_FILENAME", "INSTRUMENTATION_HOOK_FILENAME", "WEBPACK_LAYERS", "MODERN_BROWSERSLIST_TARGET", "UNDERSCORE_NOT_FOUND_ROUTE", "prettyBytes", "getRouteRegex", "getRouteMatcher", "isDynamicRoute", "escapePathDelimiters", "findPageFile", "removeTrailingSlash", "isEdgeRuntime", "normalizeLocalePath", "Log", "loadComponents", "trace", "setHttpClientAndAgentOptions", "<PERSON><PERSON>", "denormalizePagePath", "normalizePagePath", "getRuntimeContext", "isClientReference", "createWorkStore", "IncrementalCache", "nodeFs", "ciEnvironment", "normalizeAppPath", "denormalizeAppPagePath", "RouteKind", "interopDefault", "formatDynamicImportPath", "isInterceptionRouteAppPath", "checkIsRoutePPREnabled", "FallbackMode", "fallbackModeToStaticPathsResult", "parseStaticPathsResult", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "collectSegments", "createIncrementalCache", "After<PERSON><PERSON>ner", "print", "console", "log", "RESERVED_PAGE", "fileGzipStats", "fsStatGzip", "file", "cached", "fileSize", "stat", "size", "fileStats", "fsStat", "unique", "main", "sub", "Set", "difference", "a", "b", "filter", "x", "has", "intersect", "sum", "reduce", "cachedBuildManifest", "cachedAppBuildManifest", "lastCompute", "lastComputePageInfo", "computeFromManifest", "manifests", "distPath", "gzipSize", "pageInfos", "files", "Object", "is", "build", "app", "countBuildFiles", "map", "key", "manifest", "set", "Infinity", "get", "pages", "each", "Map", "expected", "pageInfo", "isHybridAmp", "getSize", "stats", "Promise", "all", "keys", "f", "join", "groupFiles", "listing", "entries", "shapeGroup", "group", "acc", "push", "total", "len", "common", "router", "undefined", "sizes", "isMiddlewareFilename", "isInstrumentationHookFilename", "filterAndSortList", "list", "routeType", "hasCustomApp", "e", "slice", "sort", "localeCompare", "collectRoutesUsingEdgeRuntime", "input", "routesUsingEdgeRuntime", "route", "info", "runtime", "printTreeView", "lists", "buildId", "pagesDir", "pageExtensions", "buildManifest", "appBuildManifest", "middlewareManifest", "useStaticPages404", "getPrettySize", "_size", "MIN_DURATION", "getPrettyDuration", "_duration", "duration", "getCleanName", "fileName", "replace", "usedSymbols", "messages", "printFileTree", "routerType", "filteredPages", "length", "entry", "for<PERSON>ach", "item", "i", "arr", "border", "ampFirs<PERSON>", "ampFirstPages", "includes", "totalDuration", "pageDuration", "ssgPageDurations", "symbol", "isRoutePPREnabled", "hasEmptyPrelude", "isDynamicAppRoute", "hasPostponed", "isStatic", "isSSG", "add", "initialRevalidateSeconds", "totalSize", "uniqueCssFiles", "endsWith", "contSymbol", "index", "innerSymbol", "ssgPageRoutes", "totalRoutes", "routes", "some", "d", "previewPages", "Math", "min", "routesWithDuration", "idx", "remainingRoutes", "remaining", "avgDuration", "round", "sharedFilesSize", "sharedFiles", "sharedCssFiles", "sharedJsChunks", "tenKbLimit", "restChunkSize", "restChunkCount", "originalName", "cleanName", "middlewareInfo", "middleware", "middlewareSizes", "dep", "align", "stringLength", "str", "staticFunctionInfo", "printCustomRoutes", "redirects", "rewrites", "headers", "printRoutes", "type", "isRedirects", "isHeaders", "routesStr", "routeStr", "source", "r", "destination", "statusCode", "permanent", "header", "last", "value", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "getJsPageSizeInKb", "page", "cachedStats", "pageManifest", "Error", "new<PERSON>ey", "pageData", "pagePath", "fnFilterJs", "pageFiles", "appFiles", "fnMapRealPath", "allFilesReal", "selfFilesReal", "getCachedSize", "allFilesSize", "selfFilesSize", "buildStaticPaths", "getStaticPaths", "staticPathsResult", "configFileName", "locales", "defaultLocale", "appDir", "prerenderedRoutes", "_routeRegex", "_routeMatcher", "routeParameterKeys", "expectedReturnVal", "Array", "isArray", "invalidStatic<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "paths", "localePathResult", "cleanedEntry", "detectedLocale", "result", "split", "segment", "decodeURIComponent", "encoded", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON>", "k", "params", "builtPage", "encodedBuiltPage", "validParamKey", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "replaced", "encodeURIComponent", "locale", "cur<PERSON><PERSON><PERSON>", "seen", "fallbackMode", "buildAppStaticPaths", "dir", "distDir", "dynamicIO", "authInterrupts", "segments", "isrFlushToDisk", "cache<PERSON><PERSON><PERSON>", "cacheLifeProfiles", "requestHeaders", "maxMemoryCacheSize", "fetchCacheKeyPrefix", "nextConfigOutput", "ComponentMod", "generate", "config", "dynamicParams", "patchFetch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "mod", "default", "incrementalCache", "dev", "flushToDisk", "serverDistDir", "getPrerenderManifest", "version", "dynamicRoutes", "notFoundRoutes", "preview", "minimalMode", "hasNextSupport", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "staticParamKeys", "param", "after<PERSON><PERSON>ner", "store", "renderOpts", "supportsDynamicResponse", "isRevalidate", "experimental", "waitUntil", "context", "onClose", "onAfterTaskError", "onTaskError", "routeParams", "workAsyncStorage", "run", "builtRouteParams", "parents<PERSON><PERSON><PERSON>", "current", "generateStaticParams", "fetchCache", "parentParams", "lastDynamicSegmentHadGenerateStaticParams", "isDynamicSegment", "relative", "filePath", "hadAllParamsGenerated", "every", "supportsRoutePreGeneration", "process", "env", "NODE_ENV", "PRERENDER", "BLOCKING_STATIC_RENDER", "NOT_FOUND", "unshift", "executeAfter", "isPageStatic", "runtimeEnvConfig", "httpAgentOptions", "parentId", "pageRuntime", "edgeInfo", "pageType", "originalAppPath", "cacheHandlers", "pprConfig", "cacheMaxMemorySize", "isPageStaticSpan", "traceAsyncFn", "require", "setConfig", "componentsResult", "prerenderFallbackMode", "appConfig", "isClientComponent", "pathIsEdgeRuntime", "edgeFunctionEntry", "wasm", "binding", "name", "useCache", "_ENTRIES", "Component", "Document", "App", "routeModule", "pageConfig", "reactLoadableManifest", "getServerSideProps", "getStaticProps", "isAppPath", "isDev", "Comp", "err", "cause", "reduceAppConfig", "dynamic", "warn", "definition", "kind", "APP_PAGE", "revalidate", "hasGetInitialProps", "getInitialProps", "hasStaticProps", "hasStaticPaths", "hasServerProps", "pageIsDynamic", "isNextImageImported", "globalThis", "__NEXT_IMAGE_IMPORTED", "amp", "isAmpOnly", "catch", "message", "error", "preferredRegion", "experimental_ppr", "maxDuration", "hasCustomGetInitialProps", "checkingApp", "components", "_app", "origGetInitialProps", "getDefinedNamedExports", "detectConflictingPaths", "combinedPages", "ssgPages", "additionalGeneratedSSGPaths", "conflictingPaths", "dynamicSsgPages", "additionalSsgPathsByPath", "pathsPage", "curPath", "currentPath", "toLowerCase", "lowerPath", "conflictingPage", "find", "conflicting<PERSON><PERSON>", "conflictingPathsOutput", "pathItems", "pathItem", "isDynamic", "exit", "copyTracedFiles", "pageKeys", "appPageKeys", "tracingRoot", "serverConfig", "hasInstrumentationHook", "staticPages", "outputPath", "moduleType", "nextConfig", "packageJsonPath", "packageJson", "JSON", "parse", "readFile", "copiedFiles", "rm", "recursive", "force", "handleTraceFiles", "traceFilePath", "traceData", "copySema", "capacity", "traceFileDir", "dirname", "relativeFile", "acquire", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileOutputPath", "mkdir", "symlink", "readlink", "code", "copyFile", "release", "handleEdgeFunction", "handleFile", "originalPath", "assets", "edgeFunctionHandlers", "values", "functions", "pageFile", "pageTraceFile", "serverOutputPath", "writeFile", "stringify", "isReservedPage", "test", "isAppBuiltinNotFoundPage", "isCustomErrorPage", "isMiddlewareFile", "isInstrumentationHookFile", "getPossibleInstrumentationHookFilenames", "folder", "extensions", "extension", "getPossibleMiddlewareFilenames", "NestedMiddlewareError", "constructor", "nestedFileNames", "mainDir", "pagesOrAppDir", "posix", "sep", "resolve", "getSupportedBrowsers", "isDevelopment", "browsers", "browsersListConfig", "loadConfig", "isWebpackServerOnlyLayer", "layer", "Boolean", "GROUP", "serverOnly", "isWebpackClientOnlyLayer", "clientOnly", "isWebpackDefaultLayer", "isWebpackBundledLayer", "bundled", "isWebpackAppPagesLayer", "appPages", "collectMeta", "status", "meta"], "mappings": "AA0BA,OAAO,yBAAwB;AAC/B,OAAO,iCAAgC;AACvC,OAAO,6BAA4B;AAEnC,SACEA,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,SAAS,QACJ,oBAAmB;AAC1B,OAAOC,iBAAiB,+BAA8B;AACtD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,UAAU,OAAM;AACvB,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,kBAAkB,kCAAiC;AAC1D,SACEC,8BAA8B,EAC9BC,oCAAoC,EACpCC,yBAAyB,EACzBC,mBAAmB,EACnBC,6BAA6B,EAC7BC,cAAc,QACT,mBAAkB;AACzB,SACEC,0BAA0B,EAC1BC,0BAA0B,QACrB,0BAAyB;AAChC,OAAOC,iBAAiB,sBAAqB;AAC7C,SAASC,aAAa,QAAQ,yCAAwC;AACtE,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,cAAc,QAAQ,wCAAuC;AACtE,OAAOC,0BAA0B,oDAAmD;AACpF,SAASC,YAAY,QAAQ,+BAA8B;AAC3D,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,YAAYC,SAAS,eAAc;AACnC,SAASC,cAAc,QAAQ,4BAA2B;AAE1D,SAASC,KAAK,QAAQ,WAAU;AAChC,SAASC,4BAA4B,QAAQ,iCAAgC;AAC7E,SAASC,IAAI,QAAQ,gCAA+B;AACpD,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,eAAe,QAAQ,qCAAoC;AAEpE,SAASC,gBAAgB,QAAQ,kCAAiC;AAClE,SAASC,MAAM,QAAQ,gCAA+B;AACtD,YAAYC,mBAAmB,oBAAmB;AAClD,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,sBAAsB,QAAQ,+CAA8C;AACrF,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,cAAc,QAAQ,yBAAwB;AAEvD,SAASC,uBAAuB,QAAQ,oCAAmC;AAC3E,SAASC,0BAA0B,QAAQ,oCAAmC;AAC9E,SAASC,sBAAsB,QAAQ,iCAAgC;AAEvE,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SACEC,+BAA+B,EAC/BC,sBAAsB,QACjB,kBAAiB;AACxB,SAASC,YAAY,QAAQ,oCAAmC;AAIhE,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SAASC,sBAAsB,QAAQ,6CAA4C;AACnF,SAASC,WAAW,QAAQ,iCAAgC;AAI5D,4CAA4C;AAC5C,MAAMC,QAAQC,QAAQC,GAAG;AAEzB,MAAMC,gBAAgB;AACtB,MAAMC,gBAA8D,CAAC;AACrE,MAAMC,aAAa,CAACC;IAClB,MAAMC,SAASH,aAAa,CAACE,KAAK;IAClC,IAAIC,QAAQ,OAAOA;IACnB,OAAQH,aAAa,CAACE,KAAK,GAAG1D,YAAY0D,IAAI,CAACA;AACjD;AAEA,MAAME,WAAW,OAAOF,OAAiB,AAAC,CAAA,MAAMtD,GAAGyD,IAAI,CAACH,KAAI,EAAGI,IAAI;AAEnE,MAAMC,YAA0D,CAAC;AACjE,MAAMC,SAAS,CAACN;IACd,MAAMC,SAASI,SAAS,CAACL,KAAK;IAC9B,IAAIC,QAAQ,OAAOA;IACnB,OAAQI,SAAS,CAACL,KAAK,GAAGE,SAASF;AACrC;AAEA,OAAO,SAASO,OAAUC,IAAsB,EAAEC,GAAqB;IACrE,OAAO;WAAI,IAAIC,IAAI;eAAIF;eAASC;SAAI;KAAE;AACxC;AAEA,OAAO,SAASE,WACdH,IAAuC,EACvCC,GAAsC;IAEtC,MAAMG,IAAI,IAAIF,IAAIF;IAClB,MAAMK,IAAI,IAAIH,IAAID;IAClB,OAAO;WAAIG;KAAE,CAACE,MAAM,CAAC,CAACC,IAAM,CAACF,EAAEG,GAAG,CAACD;AACrC;AAEA;;CAEC,GACD,SAASE,UAAaT,IAAsB,EAAEC,GAAqB;IACjE,MAAMG,IAAI,IAAIF,IAAIF;IAClB,MAAMK,IAAI,IAAIH,IAAID;IAClB,OAAO;WAAI,IAAIC,IAAI;eAAIE;SAAE,CAACE,MAAM,CAAC,CAACC,IAAMF,EAAEG,GAAG,CAACD;KAAK;AACrD;AAEA,SAASG,IAAIN,CAAwB;IACnC,OAAOA,EAAEO,MAAM,CAAC,CAACf,MAAMD,OAASC,OAAOD,MAAM;AAC/C;AAsBA,IAAIiB;AACJ,IAAIC;AAEJ,IAAIC;AACJ,IAAIC;AAEJ,OAAO,eAAeC,oBACpBC,SAGC,EACDC,QAAgB,EAChBC,WAAoB,IAAI,EACxBC,SAAiC;QAyD7BH,gBAmBMI;IA1EV,IACEC,OAAOC,EAAE,CAACX,qBAAqBK,UAAUO,KAAK,KAC9CT,wBAAwB,CAAC,CAACK,aAC1BE,OAAOC,EAAE,CAACV,wBAAwBI,UAAUQ,GAAG,GAC/C;QACA,OAAOX;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IAExC,MAAMY,kBAAkB,CACtBC,KACAC,KACAC;QAEA,KAAK,MAAMrC,QAAQqC,QAAQ,CAACD,IAAI,CAAE;YAChC,IAAIA,QAAQ,SAAS;gBACnBD,IAAIG,GAAG,CAACtC,MAAMuC;YAChB,OAAO,IAAIJ,IAAInB,GAAG,CAAChB,OAAO;gBACxBmC,IAAIG,GAAG,CAACtC,MAAMmC,IAAIK,GAAG,CAACxC,QAAS;YACjC,OAAO;gBACLmC,IAAIG,GAAG,CAACtC,MAAM;YAChB;QACF;IACF;IAEA,MAAM6B,QASF;QACFY,OAAO;YAAEC,MAAM,IAAIC;YAAOC,UAAU;QAAE;IACxC;IAEA,IAAK,MAAMR,OAAOX,UAAUO,KAAK,CAACS,KAAK,CAAE;QACvC,IAAIb,WAAW;YACb,MAAMiB,WAAWjB,UAAUY,GAAG,CAACJ;YAC/B,kEAAkE;YAClE,kDAAkD;YAClD,IAAIS,4BAAAA,SAAUC,WAAW,EAAE;gBACzB;YACF;QACF;QAEAjB,MAAMY,KAAK,CAACG,QAAQ;QACpBV,gBAAgBL,MAAMY,KAAK,CAACC,IAAI,EAAEN,KAAKX,UAAUO,KAAK,CAACS,KAAK;IAC9D;IAEA,iDAAiD;IACjD,KAAIhB,iBAAAA,UAAUQ,GAAG,qBAAbR,eAAegB,KAAK,EAAE;QACxBZ,MAAMI,GAAG,GAAG;YAAES,MAAM,IAAIC;YAAuBC,UAAU;QAAE;QAE3D,IAAK,MAAMR,OAAOX,UAAUQ,GAAG,CAACQ,KAAK,CAAE;YACrCZ,MAAMI,GAAG,CAACW,QAAQ;YAClBV,gBAAgBL,MAAMI,GAAG,CAACS,IAAI,EAAEN,KAAKX,UAAUQ,GAAG,CAACQ,KAAK;QAC1D;IACF;IAEA,MAAMM,UAAUpB,WAAW5B,aAAaO;IACxC,MAAM0C,QAAQ,IAAIL;IAElB,6EAA6E;IAC7E,WAAW;IAEX,MAAMM,QAAQC,GAAG,CACf;WACK,IAAIxC,IAAY;eACdmB,MAAMY,KAAK,CAACC,IAAI,CAACS,IAAI;eACpBtB,EAAAA,aAAAA,MAAMI,GAAG,qBAATJ,WAAWa,IAAI,CAACS,IAAI,OAAM,EAAE;SACjC;KACF,CAAChB,GAAG,CAAC,OAAOiB;QACX,IAAI;YACF,kCAAkC;YAClCJ,MAAMV,GAAG,CAACc,GAAG,MAAML,QAAQvG,KAAK6G,IAAI,CAAC3B,UAAU0B;QACjD,EAAE,OAAM,CAAC;IACX;IAGF,MAAME,aAAa,OAAOC;QAIxB,MAAMC,UAAU;eAAID,QAAQb,IAAI,CAACc,OAAO;SAAG;QAE3C,MAAMC,aAAa,CAACC,QAClBA,MAAMvC,MAAM,CACV,CAACwC,KAAK,CAACP,EAAE;gBACPO,IAAI9B,KAAK,CAAC+B,IAAI,CAACR;gBAEf,MAAMhD,OAAO4C,MAAMR,GAAG,CAACY;gBACvB,IAAI,OAAOhD,SAAS,UAAU;oBAC5BuD,IAAIvD,IAAI,CAACyD,KAAK,IAAIzD;gBACpB;gBAEA,OAAOuD;YACT,GACA;gBACE9B,OAAO,EAAE;gBACTzB,MAAM;oBACJyD,OAAO;gBACT;YACF;QAGJ,OAAO;YACLtD,QAAQkD,WAAWD,QAAQ1C,MAAM,CAAC,CAAC,GAAGgD,IAAI,GAAKA,QAAQ;YACvDC,QAAQN,WACND,QAAQ1C,MAAM,CACZ,CAAC,GAAGgD,IAAI,GAAKA,QAAQP,QAAQX,QAAQ,IAAIkB,QAAQvB;QAGvD;IACF;IAEAjB,cAAc;QACZ0C,QAAQ;YACNvB,OAAO,MAAMa,WAAWzB,MAAMY,KAAK;YACnCR,KAAKJ,MAAMI,GAAG,GAAG,MAAMqB,WAAWzB,MAAMI,GAAG,IAAIgC;QACjD;QACAC,OAAOlB;IACT;IAEA5B,sBAAsBK,UAAUO,KAAK;IACrCX,yBAAyBI,UAAUQ,GAAG;IACtCV,sBAAsB,CAAC,CAACK;IACxB,OAAON;AACT;AAEA,OAAO,SAAS6C,qBAAqBnE,IAAoB;IACvD,OAAOA,SAAS/C,uBAAuB+C,SAAS,CAAC,IAAI,EAAE/C,qBAAqB;AAC9E;AAEA,OAAO,SAASmH,8BAA8BpE,IAAoB;IAChE,OACEA,SAAS9C,iCACT8C,SAAS,CAAC,IAAI,EAAE9C,+BAA+B;AAEnD;AAEA,MAAMmH,oBAAoB,CACxBC,MACAC,WACAC;IAEA,IAAI/B;IACJ,IAAI8B,cAAc,OAAO;QACvB,8CAA8C;QAC9C9B,QAAQ6B,KAAKxD,MAAM,CAAC,CAAC2D,IAAMA,MAAM;IACnC,OAAO;QACL,wBAAwB;QACxBhC,QAAQ6B,KACLI,KAAK,GACL5D,MAAM,CACL,CAAC2D,IACC,CACEA,CAAAA,MAAM,gBACNA,MAAM,aACL,CAACD,gBAAgBC,MAAM,OAAO;IAGzC;IACA,OAAOhC,MAAMkC,IAAI,CAAC,CAAC/D,GAAGC,IAAMD,EAAEgE,aAAa,CAAC/D;AAC9C;AA4BA,OAAO,SAASgE,8BACdC,KAAgB;IAEhB,MAAMC,yBAAiD,CAAC;IACxD,KAAK,MAAM,CAACC,OAAOC,KAAK,IAAIH,MAAMtB,OAAO,GAAI;QAC3C,IAAI3F,cAAcoH,KAAKC,OAAO,GAAG;YAC/BH,sBAAsB,CAACC,MAAM,GAAG;QAClC;IACF;IAEA,OAAOD;AACT;AAEA,OAAO,eAAeI,cACpBC,KAGC,EACDxD,SAAgC,EAChC,EACEF,QAAQ,EACR2D,OAAO,EACPC,QAAQ,EACRC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBhE,WAAW,IAAI,EAWhB;QAySEyD,YAWoBM;IAlTvB,MAAME,gBAAgB,CAACC;QACrB,MAAMzF,OAAO9C,YAAYuI;QACzB,OAAO1J,MAAMC,KAAKgE;IACpB;IAEA,MAAM0F,eAAe;IACrB,MAAMC,oBAAoB,CAACC;QACzB,MAAMC,WAAW,GAAGD,UAAU,GAAG,CAAC;QAClC,uBAAuB;QACvB,IAAIA,YAAY,MAAM,OAAOjK,MAAMkK;QACnC,yBAAyB;QACzB,IAAID,YAAY,MAAM,OAAOhK,OAAOiK;QACpC,oBAAoB;QACpB,OAAOhK,IAAIG,KAAK6J;IAClB;IAEA,MAAMC,eAAe,CAACC,WACpBA,QACE,qBAAqB;SACpBC,OAAO,CAAC,aAAa,GACtB,kCAAkC;SACjCA,OAAO,CAAC,cAAc,SACvB,mBAAmB;SAClBA,OAAO,CAAC,6CAA6C;IAE1D,iCAAiC;IACjC,MAAM5B,eAAe,CAAC,CACpBc,CAAAA,YAAa,MAAM3H,aAAa2H,UAAU,SAASC,gBAAgB,MAAM;IAG3E,gEAAgE;IAChE,MAAMc,cAAc,IAAI3F;IAExB,MAAM4F,WAAuC,EAAE;IAE/C,MAAMtD,QAAQ,MAAMxB,oBAClB;QAAEQ,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC,UACAC;IAGF,MAAM2E,gBAAgB,OAAO,EAC3BjC,IAAI,EACJkC,UAAU,EAIX;YAiLyBxD,0BACJA;QAjLpB,MAAMyD,gBAAgBpC,kBAAkBC,MAAMkC,YAAYhC;QAC1D,IAAIiC,cAAcC,MAAM,KAAK,GAAG;YAC9B;QACF;QAEAJ,SAAS1C,IAAI,CACX;YACE4C,eAAe,QAAQ,gBAAgB;YACvC;YACA;SACD,CAACrE,GAAG,CAAC,CAACwE,QAAUtK,UAAUsK;QAG7BF,cAAcG,OAAO,CAAC,CAACC,MAAMC,GAAGC;gBAc3BlE,4BA6DD2C,2BAoBE3C;YA9FJ,MAAMmE,SACJF,MAAM,IACFC,IAAIL,MAAM,KAAK,IACb,MACA,MACFI,MAAMC,IAAIL,MAAM,GAAG,IACjB,MACA;YAER,MAAM7D,WAAWjB,UAAUY,GAAG,CAACqE;YAC/B,MAAMI,WAAWzB,cAAc0B,aAAa,CAACC,QAAQ,CAACN;YACtD,MAAMO,gBACJ,AAACvE,CAAAA,CAAAA,4BAAAA,SAAUwE,YAAY,KAAI,CAAA,IAC1BxE,CAAAA,CAAAA,6BAAAA,6BAAAA,SAAUyE,gBAAgB,qBAA1BzE,2BAA4B1B,MAAM,CAAC,CAACP,GAAGC,IAAMD,IAAKC,CAAAA,KAAK,CAAA,GAAI,OAAM,CAAA;YAEpE,IAAI0G;YAEJ,IAAIV,SAAS,WAAWA,SAAS,gBAAgB;gBAC/CU,SAAS;YACX,OAAO,IAAI1J,cAAcgF,4BAAAA,SAAUqC,OAAO,GAAG;gBAC3CqC,SAAS;YACX,OAAO,IAAI1E,4BAAAA,SAAU2E,iBAAiB,EAAE;gBACtC,IACE,2EAA2E;gBAC3E3E,CAAAA,4BAAAA,SAAU4E,eAAe,KACzB,qEAAqE;gBACrE,0DAA0D;gBACzD5E,SAAS6E,iBAAiB,IAAI,CAAC7E,SAAS8E,YAAY,EACrD;oBACAJ,SAAS;gBACX,OAAO,IAAI,EAAC1E,4BAAAA,SAAU8E,YAAY,GAAE;oBAClCJ,SAAS;gBACX,OAAO;oBACLA,SAAS;gBACX;YACF,OAAO,IAAI1E,4BAAAA,SAAU+E,QAAQ,EAAE;gBAC7BL,SAAS;YACX,OAAO,IAAI1E,4BAAAA,SAAUgF,KAAK,EAAE;gBAC1BN,SAAS;YACX,OAAO;gBACLA,SAAS;YACX;YAEAlB,YAAYyB,GAAG,CAACP;YAEhB,IAAI1E,4BAAAA,SAAUkF,wBAAwB,EAAE1B,YAAYyB,GAAG,CAAC;YAExDxB,SAAS1C,IAAI,CAAC;gBACZ,GAAGoD,OAAO,CAAC,EAAEO,OAAO,CAAC,EACnB1E,CAAAA,4BAAAA,SAAUkF,wBAAwB,IAC9B,GAAGlB,KAAK,OAAO,EAAEhE,4BAAAA,SAAUkF,wBAAwB,CAAC,SAAS,CAAC,GAC9DlB,OAEJO,gBAAgBtB,eACZ,CAAC,EAAE,EAAEC,kBAAkBqB,eAAe,CAAC,CAAC,GACxC,IACJ;gBACFvE,WACIoE,WACE/K,KAAK,SACL2G,SAASzC,IAAI,IAAI,IACf9C,YAAYuF,SAASzC,IAAI,IACzB,KACJ;gBACJyC,WACIoE,WACE/K,KAAK,SACL2G,SAASzC,IAAI,IAAI,IACfwF,cAAc/C,SAASmF,SAAS,IAChC,KACJ;aACL;YAED,MAAMC,iBACJzC,EAAAA,4BAAAA,cAAc/C,KAAK,CAACoE,KAAK,qBAAzBrB,0BAA2B1E,MAAM,CAC/B,CAACd;oBAECgD;uBADAhD,KAAKkI,QAAQ,CAAC,aACdlF,2BAAAA,MAAMgB,MAAM,CAACwC,WAAW,qBAAxBxD,yBAA0BzC,MAAM,CAACsB,KAAK,CAACsF,QAAQ,CAACnH;mBAC/C,EAAE;YAET,IAAIiI,eAAevB,MAAM,GAAG,GAAG;gBAC7B,MAAMyB,aAAarB,MAAMC,IAAIL,MAAM,GAAG,IAAI,MAAM;gBAEhDuB,eAAerB,OAAO,CAAC,CAAC5G,MAAMoI,OAAO,EAAE1B,MAAM,EAAE;oBAC7C,MAAM2B,cAAcD,UAAU1B,SAAS,IAAI,MAAM;oBACjD,MAAMtG,OAAO4C,MAAMkB,KAAK,CAAC1B,GAAG,CAACxC;oBAC7BsG,SAAS1C,IAAI,CAAC;wBACZ,GAAGuE,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAEnC,aAAalG,OAAO;wBACtD,OAAOI,SAAS,WAAW9C,YAAY8C,QAAQ;wBAC/C;qBACD;gBACH;YACF;YAEA,IAAIyC,6BAAAA,0BAAAA,SAAUyF,aAAa,qBAAvBzF,wBAAyB6D,MAAM,EAAE;gBACnC,MAAM6B,cAAc1F,SAASyF,aAAa,CAAC5B,MAAM;gBACjD,MAAMyB,aAAarB,MAAMC,IAAIL,MAAM,GAAG,IAAI,MAAM;gBAEhD,IAAI8B;gBACJ,IACE3F,SAASyE,gBAAgB,IACzBzE,SAASyE,gBAAgB,CAACmB,IAAI,CAAC,CAACC,IAAMA,IAAI5C,eAC1C;oBACA,MAAM6C,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnE,MAAMO,qBAAqBjG,SAASyF,aAAa,CAC9CnG,GAAG,CAAC,CAAC6C,OAAO+D,MAAS,CAAA;4BACpB/D;4BACAiB,UAAUpD,SAASyE,gBAAgB,AAAC,CAACyB,IAAI,IAAI;wBAC/C,CAAA,GACCpE,IAAI,CAAC,CAAC,EAAEsB,UAAUrF,CAAC,EAAE,EAAE,EAAEqF,UAAUpF,CAAC,EAAE,GACrC,mBAAmB;wBACnB,wDAAwD;wBACxDD,KAAKkF,gBAAgBjF,KAAKiF,eAAe,IAAIjF,IAAID;oBAErD4H,SAASM,mBAAmBpE,KAAK,CAAC,GAAGiE;oBACrC,MAAMK,kBAAkBF,mBAAmBpE,KAAK,CAACiE;oBACjD,IAAIK,gBAAgBtC,MAAM,EAAE;wBAC1B,MAAMuC,YAAYD,gBAAgBtC,MAAM;wBACxC,MAAMwC,cAAcN,KAAKO,KAAK,CAC5BH,gBAAgB7H,MAAM,CACpB,CAAC0C,OAAO,EAAEoC,QAAQ,EAAE,GAAKpC,QAAQoC,UACjC,KACE+C,gBAAgBtC,MAAM;wBAE5B8B,OAAO5E,IAAI,CAAC;4BACVoB,OAAO,CAAC,EAAE,EAAEiE,UAAU,YAAY,CAAC;4BACnChD,UAAU;4BACViD;wBACF;oBACF;gBACF,OAAO;oBACL,MAAMP,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnEC,SAAS3F,SAASyF,aAAa,CAC5B5D,KAAK,CAAC,GAAGiE,cACTxG,GAAG,CAAC,CAAC6C,QAAW,CAAA;4BAAEA;4BAAOiB,UAAU;wBAAE,CAAA;oBACxC,IAAIsC,cAAcI,cAAc;wBAC9B,MAAMM,YAAYV,cAAcI;wBAChCH,OAAO5E,IAAI,CAAC;4BAAEoB,OAAO,CAAC,EAAE,EAAEiE,UAAU,YAAY,CAAC;4BAAEhD,UAAU;wBAAE;oBACjE;gBACF;gBAEAuC,OAAO5B,OAAO,CACZ,CAAC,EAAE5B,KAAK,EAAEiB,QAAQ,EAAEiD,WAAW,EAAE,EAAEd,OAAO,EAAE1B,MAAM,EAAE;oBAClD,MAAM2B,cAAcD,UAAU1B,SAAS,IAAI,MAAM;oBACjDJ,SAAS1C,IAAI,CAAC;wBACZ,GAAGuE,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAErD,QAChCiB,WAAWH,eACP,CAAC,EAAE,EAAEC,kBAAkBE,UAAU,CAAC,CAAC,GACnC,KAEJiD,eAAeA,cAAcpD,eACzB,CAAC,MAAM,EAAEC,kBAAkBmD,aAAa,CAAC,CAAC,GAC1C,IACJ;wBACF;wBACA;qBACD;gBACH;YAEJ;QACF;QAEA,MAAME,mBAAkBpG,2BAAAA,MAAMgB,MAAM,CAACwC,WAAW,qBAAxBxD,yBAA0Be,MAAM,CAAC3D,IAAI,CAACyD,KAAK;QACnE,MAAMwF,cAAcrG,EAAAA,4BAAAA,MAAMgB,MAAM,CAACwC,WAAW,qBAAxBxD,0BAA0Be,MAAM,CAAClC,KAAK,KAAI,EAAE;QAEhEyE,SAAS1C,IAAI,CAAC;YACZ;YACA,OAAOwF,oBAAoB,WAAWxD,cAAcwD,mBAAmB;YACvE;SACD;QACD,MAAME,iBAA2B,EAAE;QACnC,MAAMC,iBAAiB;eAClBF,YACAvI,MAAM,CAAC,CAACd;gBACP,IAAIA,KAAKkI,QAAQ,CAAC,SAAS;oBACzBoB,eAAe1F,IAAI,CAAC5D;oBACpB,OAAO;gBACT;gBACA,OAAO;YACT,GACCmC,GAAG,CAAC,CAACsC,IAAMA,EAAE2B,OAAO,CAACf,SAAS,cAC9BV,IAAI;eACJ2E,eAAenH,GAAG,CAAC,CAACsC,IAAMA,EAAE2B,OAAO,CAACf,SAAS,cAAcV,IAAI;SACnE;QAED,0GAA0G;QAC1G,MAAM6E,aAAa,KAAK;QACxB,IAAIC,gBAAgB;QACpB,IAAIC,iBAAiB;QACrBH,eAAe3C,OAAO,CAAC,CAACT,UAAUiC,OAAO,EAAE1B,MAAM,EAAE;YACjD,MAAM2B,cAAcD,QAAQsB,mBAAmBhD,SAAS,IAAI,MAAM;YAElE,MAAMiD,eAAexD,SAASC,OAAO,CAAC,aAAaf;YACnD,MAAMuE,YAAY1D,aAAaC;YAC/B,MAAM/F,OAAO4C,MAAMkB,KAAK,CAAC1B,GAAG,CAACmH;YAE7B,IAAI,CAACvJ,QAAQA,OAAOoJ,YAAY;gBAC9BE;gBACAD,iBAAiBrJ,QAAQ;gBACzB;YACF;YAEAkG,SAAS1C,IAAI,CAAC;gBAAC,CAAC,EAAE,EAAEyE,YAAY,CAAC,EAAEuB,WAAW;gBAAEtM,YAAY8C;gBAAO;aAAG;QACxE;QAEA,IAAIsJ,iBAAiB,GAAG;YACtBpD,SAAS1C,IAAI,CAAC;gBACZ,CAAC,+BAA+B,CAAC;gBACjCtG,YAAYmM;gBACZ;aACD;QACH;IACF;IAEA,yDAAyD;IACzD,IAAIrE,MAAMnD,GAAG,IAAIe,MAAMgB,MAAM,CAAC/B,GAAG,EAAE;QACjC,MAAMsE,cAAc;YAClBC,YAAY;YACZlC,MAAMc,MAAMnD,GAAG;QACjB;QAEAqE,SAAS1C,IAAI,CAAC;YAAC;YAAI;YAAI;SAAG;IAC5B;IAEAhC,UAAUU,GAAG,CAAC,QAAQ;QACpB,GAAIV,UAAUY,GAAG,CAAC,WAAWZ,UAAUY,GAAG,CAAC,UAAU;QACrDoF,UAAUjC;IACZ;IAEA,uFAAuF;IACvF,IACE,CAACP,MAAM3C,KAAK,CAAC0E,QAAQ,CAAC,WACtB,GAAC/B,aAAAA,MAAMnD,GAAG,qBAATmD,WAAW+B,QAAQ,CAAC9J,8BACrB;QACA+H,MAAM3C,KAAK,GAAG;eAAI2C,MAAM3C,KAAK;YAAE;SAAO;IACxC;IAEA,+CAA+C;IAC/C,MAAM8D,cAAc;QAClBC,YAAY;QACZlC,MAAMc,MAAM3C,KAAK;IACnB;IAEA,MAAMoH,kBAAiBnE,iCAAAA,mBAAmBoE,UAAU,qBAA7BpE,8BAA+B,CAAC,IAAI;IAC3D,IAAImE,CAAAA,kCAAAA,eAAgBhI,KAAK,CAAC6E,MAAM,IAAG,GAAG;QACpC,MAAMqD,kBAAkB,MAAM9G,QAAQC,GAAG,CACvC2G,eAAehI,KAAK,CACjBM,GAAG,CAAC,CAAC6H,MAAQ,GAAGtI,SAAS,CAAC,EAAEsI,KAAK,EACjC7H,GAAG,CAACR,WAAW5B,aAAaO;QAGjCgG,SAAS1C,IAAI,CAAC;YAAC;YAAI;YAAI;SAAG;QAC1B0C,SAAS1C,IAAI,CAAC;YAAC;YAAgBgC,cAAc1E,IAAI6I;YAAmB;SAAG;IACzE;IAEArK,MACEnD,UAAU+J,UAAU;QAClB2D,OAAO;YAAC;YAAK;YAAK;SAAI;QACtBC,cAAc,CAACC,MAAQvN,UAAUuN,KAAKzD,MAAM;IAC9C;IAGF,MAAM0D,qBACJhF,MAAMnD,GAAG,IAAIe,MAAMgB,MAAM,CAAC/B,GAAG,GAAG,yBAAyB;IAC3DvC;IACAA,MACEnD,UACE;QACE8J,YAAYrF,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACDqF,YAAYrF,GAAG,CAAC,QAAQ;YACtB;YACA;YACA,CAAC,iCAAiC,EAAE9E,KAAKkO,oBAAoB,CAAC,CAAC;SAChE;QACD/D,YAAYrF,GAAG,CAAC,UAAU;YACxB;YACA;YACA,CAAC,oDAAoD,EAAE9E,KACrDkO,oBACA,CAAC,CAAC;SACL;QACD/D,YAAYrF,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACDqF,YAAYrF,GAAG,CAAC,QAAQ;YAAC;YAAK;YAAa,CAAC,yBAAyB,CAAC;SAAC;KACxE,CAACF,MAAM,CAAC,CAACC,IAAMA,IAChB;QACEkJ,OAAO;YAAC;YAAK;YAAK;SAAI;QACtBC,cAAc,CAACC,MAAQvN,UAAUuN,KAAKzD,MAAM;IAC9C;IAIJhH;AACF;AAEA,OAAO,SAAS2K,kBAAkB,EAChCC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACM;IACb,MAAMC,cAAc,CAClBjC,QACAkC;QAEA,MAAMC,cAAcD,SAAS;QAC7B,MAAME,YAAYF,SAAS;QAC3BhL,MAAMrD,UAAUqO;QAEhB;;;;KAIC,GACD,MAAMG,YAAY,AAACrC,OAChBrG,GAAG,CAAC,CAAC6C;YACJ,IAAI8F,WAAW,CAAC,UAAU,EAAE9F,MAAM+F,MAAM,CAAC,EAAE,CAAC;YAE5C,IAAI,CAACH,WAAW;gBACd,MAAMI,IAAIhG;gBACV8F,YAAY,GAAGH,cAAc,MAAM,IAAI,cAAc,EACnDK,EAAEC,WAAW,CACd,EAAE,CAAC;YACN;YACA,IAAIN,aAAa;gBACf,MAAMK,IAAIhG;gBACV8F,YAAY,CAAC,EAAE,EACbE,EAAEE,UAAU,GACR,CAAC,QAAQ,EAAEF,EAAEE,UAAU,EAAE,GACzB,CAAC,WAAW,EAAEF,EAAEG,SAAS,EAAE,CAChC,EAAE,CAAC;YACN;YAEA,IAAIP,WAAW;gBACb,MAAMI,IAAIhG;gBACV8F,YAAY,CAAC,YAAY,CAAC;gBAE1B,IAAK,IAAIhE,IAAI,GAAGA,IAAIkE,EAAER,OAAO,CAAC9D,MAAM,EAAEI,IAAK;oBACzC,MAAMsE,SAASJ,EAAER,OAAO,CAAC1D,EAAE;oBAC3B,MAAMuE,OAAOvE,MAAM0D,QAAQ9D,MAAM,GAAG;oBAEpCoE,YAAY,CAAC,EAAE,EAAEO,OAAO,MAAM,IAAI,CAAC,EAAED,OAAOhJ,GAAG,CAAC,EAAE,EAAEgJ,OAAOE,KAAK,CAAC,EAAE,CAAC;gBACtE;YACF;YAEA,OAAOR;QACT,GACCzH,IAAI,CAAC;QAER3D,MAAM,GAAGmL,UAAU,EAAE,CAAC;IACxB;IAEAnL;IACA,IAAI4K,UAAU5D,MAAM,EAAE;QACpB+D,YAAYH,WAAW;IACzB;IACA,IAAIE,QAAQ9D,MAAM,EAAE;QAClB+D,YAAYD,SAAS;IACvB;IAEA,MAAMe,mBAAmB;WACpBhB,SAASiB,WAAW;WACpBjB,SAASkB,UAAU;WACnBlB,SAASmB,QAAQ;KACrB;IACD,IAAIH,iBAAiB7E,MAAM,EAAE;QAC3B+D,YAAYc,kBAAkB;IAChC;AACF;AAEA,OAAO,eAAeI,kBACpBnF,UAAuB,EACvBoF,IAAY,EACZlK,QAAgB,EAChB8D,aAA4B,EAC5BC,gBAAmC,EACnC9D,WAAoB,IAAI,EACxBkK,WAAwC;IAExC,MAAMC,eAAetF,eAAe,UAAUhB,gBAAgBC;IAC9D,IAAI,CAACqG,cAAc;QACjB,MAAM,IAAIC,MAAM;IAClB;IAEA,kCAAkC;IAClC,IAAIvF,eAAe,OAAO;QACxBsF,aAAarJ,KAAK,GAAGX,OAAO0B,OAAO,CAACsI,aAAarJ,KAAK,EAAEtB,MAAM,CAC5D,CAACwC,KAA+B,CAACvB,KAAKkJ,MAAM;YAC1C,MAAMU,SAASpN,iBAAiBwD;YAChCuB,GAAG,CAACqI,OAAO,GAAGV;YACd,OAAO3H;QACT,GACA,CAAC;IAEL;IAEA,oDAAoD;IACpD,MAAMX,QACJ6I,eACC,MAAMrK,oBACL;QAAEQ,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC;IAGJ,MAAMsK,WAAWjJ,MAAMgB,MAAM,CAACwC,WAAW;IACzC,IAAI,CAACyF,UAAU;QACb,kEAAkE;QAClE,MAAM,IAAIF,MAAM;IAClB;IAEA,MAAMG,WACJ1F,eAAe,UACXpI,oBAAoBwN,QACpB/M,uBAAuB+M;IAE7B,MAAMO,aAAa,CAACxF,QAAkBA,MAAMuB,QAAQ,CAAC;IAErD,MAAMkE,YAAY,AAACN,CAAAA,aAAarJ,KAAK,CAACyJ,SAAS,IAAI,EAAE,AAAD,EAAGpL,MAAM,CAACqL;IAC9D,MAAME,WAAW,AAACP,CAAAA,aAAarJ,KAAK,CAAC,QAAQ,IAAI,EAAE,AAAD,EAAG3B,MAAM,CAACqL;IAE5D,MAAMG,gBAAgB,CAACtC,MAAgB,GAAGtI,SAAS,CAAC,EAAEsI,KAAK;IAE3D,MAAMuC,eAAehM,OAAO6L,WAAWC,UAAUlK,GAAG,CAACmK;IACrD,MAAME,gBAAgB7L,WACpB,mEAAmE;IACnEM,UAAUmL,WAAWH,SAAS1L,MAAM,CAACsB,KAAK,GAC1C,gCAAgC;IAChCoK,SAASlI,MAAM,CAAClC,KAAK,EACrBM,GAAG,CAACmK;IAEN,MAAMvJ,UAAUpB,WAAW5B,aAAaO;IAExC,2EAA2E;IAC3E,eAAe;IACf,MAAMmM,gBAAgB,OAAOzM;QAC3B,MAAMoC,MAAMpC,KAAK0E,KAAK,CAAChD,SAASgF,MAAM,GAAG;QACzC,MAAMtG,OAA2B4C,MAAMkB,KAAK,CAAC1B,GAAG,CAACJ;QAEjD,oEAAoE;QACpE,YAAY;QACZ,IAAI,OAAOhC,SAAS,UAAU;YAC5B,OAAO2C,QAAQ/C;QACjB;QAEA,OAAOI;IACT;IAEA,IAAI;QACF,0EAA0E;QAC1E,kEAAkE;QAClE,MAAMsM,eAAexL,IAAI,MAAM+B,QAAQC,GAAG,CAACqJ,aAAapK,GAAG,CAACsK;QAC5D,MAAME,gBAAgBzL,IACpB,MAAM+B,QAAQC,GAAG,CAACsJ,cAAcrK,GAAG,CAACsK;QAGtC,OAAO;YAACE;YAAeD;SAAa;IACtC,EAAE,OAAM,CAAC;IACT,OAAO;QAAC,CAAC;QAAG,CAAC;KAAE;AACjB;AAqBA,OAAO,eAAeE,iBAAiB,EACrChB,IAAI,EACJiB,cAAc,EACdC,iBAAiB,EACjBC,cAAc,EACdC,OAAO,EACPC,aAAa,EACbC,MAAM,EASP;IACC,MAAMC,oBAAwC,EAAE;IAChD,MAAMC,cAAc7P,cAAcqO;IAClC,MAAMyB,gBAAgB7P,gBAAgB4P;IAEtC,0CAA0C;IAC1C,MAAME,qBAAqBxL,OAAOqB,IAAI,CAACkK,cAAczB;IAErD,IAAI,CAACkB,mBAAmB;QACtB,IAAID,gBAAgB;YAClBC,oBAAoB,MAAMD,eAAe;gBAAEG;gBAASC;YAAc;QACpE,OAAO;YACL,MAAM,IAAIlB,MACR,CAAC,yFAAyF,EAAEH,MAAM;QAEtG;IACF;IAEA,MAAM2B,oBACJ,CAAC,4CAA4C,CAAC,GAC9C,CAAC,qFAAqF,CAAC;IAEzF,IACE,CAACT,qBACD,OAAOA,sBAAsB,YAC7BU,MAAMC,OAAO,CAACX,oBACd;QACA,MAAM,IAAIf,MACR,CAAC,8CAA8C,EAAEH,KAAK,WAAW,EAAE,OAAOkB,kBAAkB,CAAC,EAAES,mBAAmB;IAEtH;IAEA,MAAMG,wBAAwB5L,OAAOqB,IAAI,CAAC2J,mBAAmBhM,MAAM,CACjE,CAACsB,MAAQ,CAAEA,CAAAA,QAAQ,WAAWA,QAAQ,UAAS;IAGjD,IAAIsL,sBAAsBhH,MAAM,GAAG,GAAG;QACpC,MAAM,IAAIqF,MACR,CAAC,2CAA2C,EAAEH,KAAK,EAAE,EAAE8B,sBAAsBrK,IAAI,CAC/E,MACA,EAAE,EAAEkK,mBAAmB;IAE7B;IAEA,IACE,CACE,CAAA,OAAOT,kBAAkBpB,QAAQ,KAAK,aACtCoB,kBAAkBpB,QAAQ,KAAK,UAAS,GAE1C;QACA,MAAM,IAAIK,MACR,CAAC,6DAA6D,EAAEH,KAAK,GAAG,CAAC,GACvE2B;IAEN;IAEA,MAAMI,cAAcb,kBAAkBc,KAAK;IAE3C,IAAI,CAACJ,MAAMC,OAAO,CAACE,cAAc;QAC/B,MAAM,IAAI5B,MACR,CAAC,wDAAwD,EAAEH,KAAK,GAAG,CAAC,GAClE,CAAC,2FAA2F,CAAC;IAEnG;IAEA+B,YAAY/G,OAAO,CAAC,CAACD;QACnB,uEAAuE;QACvE,SAAS;QACT,IAAI,OAAOA,UAAU,UAAU;YAC7BA,QAAQ/I,oBAAoB+I;YAE5B,MAAMkH,mBAAmB/P,oBAAoB6I,OAAOqG;YACpD,IAAIc,eAAenH;YAEnB,IAAIkH,iBAAiBE,cAAc,EAAE;gBACnCD,eAAenH,MAAMjC,KAAK,CAACmJ,iBAAiBE,cAAc,CAACrH,MAAM,GAAG;YACtE,OAAO,IAAIuG,eAAe;gBACxBtG,QAAQ,CAAC,CAAC,EAAEsG,gBAAgBtG,OAAO;YACrC;YAEA,MAAMqH,SAASX,cAAcS;YAC7B,IAAI,CAACE,QAAQ;gBACX,MAAM,IAAIjC,MACR,CAAC,oBAAoB,EAAE+B,aAAa,8BAA8B,EAAElC,KAAK,GAAG,CAAC;YAEjF;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,aAAa;YACbuB,kBAAkBvJ,IAAI,CAAC;gBACrBpH,MAAMmK,MACHsH,KAAK,CAAC,KACN9L,GAAG,CAAC,CAAC+L,UACJxQ,qBAAqByQ,mBAAmBD,UAAU,OAEnD7K,IAAI,CAAC;gBACR+K,SAASzH;gBACT0H,qBAAqBpK;YACvB;QACF,OAGK;YACH,MAAMqK,cAAcxM,OAAOqB,IAAI,CAACwD,OAAO7F,MAAM,CAC3C,CAACsB,MAAQA,QAAQ,YAAYA,QAAQ;YAGvC,IAAIkM,YAAY5H,MAAM,EAAE;gBACtB,MAAM,IAAIqF,MACR,CAAC,+DAA+D,EAAEH,KAAK,GAAG,CAAC,GACzE,CAAC,6FAA6F,CAAC,GAC/F,CAAC,yBAAyB,EAAE0B,mBACzBnL,GAAG,CAAC,CAACoM,IAAM,GAAGA,EAAE,KAAK,CAAC,EACtBlL,IAAI,CAAC,MAAM,IAAI,CAAC,GACnB,CAAC,gCAAgC,EAAEiL,YAAYjL,IAAI,CAAC,MAAM,GAAG,CAAC;YAEpE;YAEA,MAAM,EAAEmL,SAAS,CAAC,CAAC,EAAE,GAAG7H;YACxB,IAAI8H,YAAY7C;YAChB,IAAI8C,mBAAmB9C;YAEvB0B,mBAAmB1G,OAAO,CAAC,CAAC+H;gBAC1B,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGzB,YAAY0B,MAAM,CAACH,cAAc;gBAC9D,IAAII,aAAaP,MAAM,CAACG,cAAc;gBACtC,IACEE,YACAL,OAAOQ,cAAc,CAACL,kBACrBI,CAAAA,eAAe,QACdA,eAAe9K,aACf,AAAC8K,eAAuB,KAAI,GAC9B;oBACAA,aAAa,EAAE;gBACjB;gBACA,IACE,AAACH,UAAU,CAACpB,MAAMC,OAAO,CAACsB,eACzB,CAACH,UAAU,OAAOG,eAAe,UAClC;oBACA,mEAAmE;oBACnE,wBAAwB;oBACxB,IAAI7B,UAAU,OAAO6B,eAAe,aAAa;wBAC/CN,YAAY;wBACZC,mBAAmB;wBACnB;oBACF;oBAEA,MAAM,IAAI3C,MACR,CAAC,sBAAsB,EAAE4C,cAAc,sBAAsB,EAC3DC,SAAS,aAAa,WACvB,UAAU,EAAE,OAAOG,WAAW,IAAI,EACjC7B,SAAS,yBAAyB,iBACnC,KAAK,EAAEtB,MAAM;gBAElB;gBACA,IAAIqD,WAAW,CAAC,CAAC,EAAEL,SAAS,QAAQ,KAAKD,cAAc,CAAC,CAAC;gBACzD,IAAIE,UAAU;oBACZI,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC;gBAC5B;gBACAR,YAAYA,UACTrI,OAAO,CACN6I,UACAL,SACI,AAACG,WACE5M,GAAG,CAAC,CAAC+L,UAAYxQ,qBAAqBwQ,SAAS,OAC/C7K,IAAI,CAAC,OACR3F,qBAAqBqR,YAAsB,OAEhD3I,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,YAAY;gBAEvBsI,mBAAmBA,iBAChBtI,OAAO,CACN6I,UACAL,SACI,AAACG,WAAwB5M,GAAG,CAAC+M,oBAAoB7L,IAAI,CAAC,OACtD6L,mBAAmBH,aAExB3I,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,YAAY;YACzB;YAEA,IAAI,CAACqI,aAAa,CAACC,kBAAkB;gBACnC;YACF;YAEA,IAAI/H,MAAMwI,MAAM,IAAI,EAACnC,2BAAAA,QAAS7F,QAAQ,CAACR,MAAMwI,MAAM,IAAG;gBACpD,MAAM,IAAIpD,MACR,CAAC,gDAAgD,EAAEH,KAAK,aAAa,EAAEjF,MAAMwI,MAAM,CAAC,qBAAqB,EAAEpC,gBAAgB;YAE/H;YACA,MAAMqC,YAAYzI,MAAMwI,MAAM,IAAIlC,iBAAiB;YAEnDE,kBAAkBvJ,IAAI,CAAC;gBACrBpH,MAAM,GAAG4S,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KACrCA,aAAaX,cAAc,MAAM,KAAKA,WACtC;gBACFL,SAAS,GAAGgB,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KACxCA,aAAaV,qBAAqB,MAAM,KAAKA,kBAC7C;gBACFL,qBAAqBpK;YACvB;QACF;IACF;IAEA,MAAMoL,OAAO,IAAI3O;IAEjB,OAAO;QACL4O,cAAcjQ,uBAAuByN,kBAAkBpB,QAAQ;QAC/DyB,mBAAmBA,kBAAkBrM,MAAM,CAAC,CAACkE;YAC3C,IAAIqK,KAAKrO,GAAG,CAACgE,MAAMxI,IAAI,GAAG,OAAO;YAEjC,8BAA8B;YAC9B6S,KAAKvH,GAAG,CAAC9C,MAAMxI,IAAI;YACnB,OAAO;QACT;IACF;AACF;AAMA,OAAO,eAAe+S,oBAAoB,EACxCC,GAAG,EACH5D,IAAI,EACJ6D,OAAO,EACPC,SAAS,EACTC,cAAc,EACd5C,cAAc,EACd6C,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZ5I,iBAAiB,EACjBnC,OAAO,EAqBR;IACC,IACEuK,SAASnH,IAAI,CAAC,CAAC4H;YAAaA;eAAAA,EAAAA,mBAAAA,SAASC,MAAM,qBAAfD,iBAAiBE,aAAa,MAAK;UAC/DJ,qBAAqB,UACrB;QACA,MAAM,IAAIpE,MACR;IAEJ;IAEAqE,aAAaI,UAAU;IAEvB,IAAIC;IACJ,IAAIX,cAAc;QAChBW,kBAAkB1R,eAChB,MAAM,MAAM,CAACC,wBAAwBwQ,KAAKM,eAAeY,IAAI,CAC3D,CAACC,MAAQA,IAAIC,OAAO,IAAID;IAG9B;IAEA,MAAME,mBAAmB,IAAIpS,iBAAiB;QAC5C/B,IAAIgC;QACJoS,KAAK;QACLpB;QACAqB,aAAalB;QACbmB,eAAexU,KAAK6G,IAAI,CAACoM,SAAS;QAClCS;QACAD;QACAgB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS,CAAC;gBACV1I,QAAQ,CAAC;gBACT2I,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;YACX,CAAA;QACAZ;QACAT;QACAsB,aAAa3S,cAAc4S,cAAc;IAC3C;IAEA,MAAMC,YAAY,IAAI9Q;IAEtB,MAAM+Q,kBAAkB,IAAI/Q;IAC5B,KAAK,MAAMwN,WAAW0B,SAAU;QAC9B,IAAI1B,QAAQwD,KAAK,EAAE;gBAGbxD;YAFJsD,UAAU1J,GAAG,CAACoG,QAAQwD,KAAK;YAE3B,IAAIxD,EAAAA,kBAAAA,QAAQoC,MAAM,qBAAdpC,gBAAgBqC,aAAa,MAAK,OAAO;gBAC3CkB,gBAAgB3J,GAAG,CAACoG,QAAQwD,KAAK;YACnC;QACF;IACF;IAEA,MAAMC,cAAc,IAAIlS;IAExB,MAAMmS,QAAQpT,gBAAgB;QAC5BoN;QACA,sEAAsE;QACtE,QAAQ;QACRyC,qBAAqB;QACrBwD,YAAY;YACVhB;YACAd;YACA+B,yBAAyB;YACzBC,cAAc;YACdC,cAAc;gBACZtC;gBACAC;YACF;YACAsC,WAAWN,YAAYO,OAAO,CAACD,SAAS;YACxCE,SAASR,YAAYO,OAAO,CAACC,OAAO;YACpCC,kBAAkBT,YAAYO,OAAO,CAACG,WAAW;YACjDhN;QACF;IACF;IAEA,MAAMiN,cAAc,MAAMlC,aAAamC,gBAAgB,CAACC,GAAG,CACzDZ,OACA;QACE,eAAea,iBACbC,gBAA0B,EAAE,EAC5B3J,MAAM,CAAC;YAEP,yDAAyD;YACzD,IAAIA,QAAQ6G,SAASlJ,MAAM,EAAE,OAAOgM;YAEpC,MAAMC,UAAU/C,QAAQ,CAAC7G,IAAI;YAE7B,IACE,OAAO4J,QAAQC,oBAAoB,KAAK,cACxC7J,MAAM6G,SAASlJ,MAAM,EACrB;gBACA,OAAO+L,iBAAiBC,eAAe3J,MAAM;YAC/C;YAEA,MAAMyF,SAAmB,EAAE;YAE3B,IAAImE,QAAQC,oBAAoB,EAAE;oBAIrBD;gBAHX,oEAAoE;gBACpE,0EAA0E;gBAC1E,oCAAoC;gBACpC,IAAI,SAAOA,kBAAAA,QAAQrC,MAAM,qBAAdqC,gBAAgBE,UAAU,MAAK,aAAa;oBACrDjB,MAAMiB,UAAU,GAAGF,QAAQrC,MAAM,CAACuC,UAAU;gBAC9C;gBAEA,IAAIH,cAAchM,MAAM,GAAG,GAAG;oBAC5B,KAAK,MAAMoM,gBAAgBJ,cAAe;wBACxC,MAAM1E,SAAS,MAAM2E,QAAQC,oBAAoB,CAAC;4BAChDpE,QAAQsE;wBACV;wBAEA,KAAK,MAAMjM,QAAQmH,OAAQ;4BACzBQ,OAAO5K,IAAI,CAAC;gCAAE,GAAGkP,YAAY;gCAAE,GAAGjM,IAAI;4BAAC;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAMmH,SAAS,MAAM2E,QAAQC,oBAAoB,CAAC;wBAAEpE,QAAQ,CAAC;oBAAE;oBAE/DA,OAAO5K,IAAI,IAAIoK;gBACjB;YACF;YAEA,IAAIjF,MAAM6G,SAASlJ,MAAM,EAAE;gBACzB,OAAO+L,iBAAiBjE,QAAQzF,MAAM;YACxC;YAEA,OAAOyF;QACT;QAEA,OAAOiE;IACT;IAGF,IAAIM,4CAA4C;IAChD,KAAK,MAAM7E,WAAW0B,SAAU;YAM5B1B;QALF,sEAAsE;QACtE,8BAA8B;QAC9B,IACEA,QAAQwD,KAAK,IACbxD,QAAQ8E,gBAAgB,IACxB9E,EAAAA,mBAAAA,QAAQoC,MAAM,qBAAdpC,iBAAgBqC,aAAa,MAAK,OAClC;YACA,KAAK,MAAM/B,UAAU8D,YAAa;gBAChC,IAAIpE,QAAQwD,KAAK,IAAIlD,QAAQ;gBAE7B,MAAMyE,WAAW/E,QAAQgF,QAAQ,GAC7B1W,KAAKyW,QAAQ,CAACzD,KAAKtB,QAAQgF,QAAQ,IACnCjP;gBAEJ,MAAM,IAAI8H,MACR,CAAC,SAAS,EAAEkH,SAAS,gDAAgD,EAAE/E,QAAQwD,KAAK,CAAC,6CAA6C,CAAC;YAEvI;QACF;QAEA,IACExD,QAAQ8E,gBAAgB,IACxB,OAAO9E,QAAQ0E,oBAAoB,KAAK,YACxC;YACAG,4CAA4C;QAC9C,OAAO,IAAI,OAAO7E,QAAQ0E,oBAAoB,KAAK,YAAY;YAC7DG,4CAA4C;QAC9C;IACF;IAEA,6EAA6E;IAC7E,kEAAkE;IAClE,MAAMI,wBACJ3B,UAAUpR,IAAI,KAAK,KAClBkS,YAAY5L,MAAM,GAAG,KACpB4L,YAAYc,KAAK,CAAC,CAAC5E;QACjB,KAAK,MAAMpM,OAAOoP,UAAW;YAC3B,IAAIpP,OAAOoM,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEJ,wEAAwE;IACxE,mEAAmE;IACnE,mCAAmC;IACnC,MAAM+B,gBAAgBX,SAASwD,KAAK,CAClC,CAAClF;YAAYA;eAAAA,EAAAA,kBAAAA,QAAQoC,MAAM,qBAAdpC,gBAAgBqC,aAAa,MAAK;;IAGjD,MAAM8C,6BACJF,yBAAyBG,QAAQC,GAAG,CAACC,QAAQ,KAAK;IAEpD,MAAMlE,eAAeiB,gBACjB8C,6BACE7L,oBACErI,aAAasU,SAAS,GACtBtU,aAAauU,sBAAsB,GACrCzP,YACF9E,aAAawU,SAAS;IAE1B,IAAI3F,SAAmC;QACrCsB;QACAnC,mBAAmB4F,4CACf,EAAE,GACF9O;IACN;IAEA,IAAIkP,yBAAyB7D,cAAc;QACzCtB,SAAS,MAAMpB,iBAAiB;YAC9BE,mBAAmB;gBACjBpB,UAAUtM,gCAAgCkQ;gBAC1C1B,OAAO0E,YAAYnQ,GAAG,CAAC,CAACqM,SAAY,CAAA;wBAAEA;oBAAO,CAAA;YAC/C;YACA5C;YACAmB;YACAG,QAAQ;QACV;IACF;IAEA,sEAAsE;IACtE,uCAAuC;IACvC,IAAI1F,mBAAmB;QACrBwG,OAAOb,iBAAiB,KAAK,EAAE;QAC/Ba,OAAOb,iBAAiB,CAACyG,OAAO,CAAC;YAC/BpX,MAAMoP;YACNwC,SAASxC;YACTyC,qBAAqB/O,aAAasM;QACpC;IACF;IAEA,MAAM+F,YAAYkC,YAAY;IAE9B,OAAO7F;AACT;AAiBA,OAAO,eAAe8F,aAAa,EACjCtE,GAAG,EACH5D,IAAI,EACJ6D,OAAO,EACP1C,cAAc,EACdgH,gBAAgB,EAChBC,gBAAgB,EAChBhH,OAAO,EACPC,aAAa,EACbgH,QAAQ,EACRC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACR1E,SAAS,EACTC,cAAc,EACd0E,eAAe,EACfxE,cAAc,EACdI,kBAAkB,EAClBE,gBAAgB,EAChBL,YAAY,EACZwE,aAAa,EACbvE,iBAAiB,EACjBwE,SAAS,EACTlP,OAAO,EA2BR;IACC,MAAM7F,uBAAuB;QAC3BsQ;QACAwE;QACA7E;QACAD;QACAE;QACAqB,aAAalB;QACb2E,oBAAoBvE;IACtB;IAEA,MAAMwE,mBAAmBxW,MAAM,wBAAwBgW;IACvD,OAAOQ,iBACJC,YAAY,CAAC;QACZC,QAAQ,yCAAyCC,SAAS,CACxDb;QAEF7V,6BAA6B;YAC3B8V;QACF;QAEA,IAAIa;QACJ,IAAI1H;QACJ,IAAI2H;QACJ,IAAIC,YAA8B,CAAC;QACnC,IAAIC,oBAA6B;QACjC,MAAMC,oBAAoBpX,cAAcqW;QAExC,IAAIe,mBAAmB;YACrB,MAAM/P,UAAU,MAAM5G,kBAAkB;gBACtCsP,OAAOuG,SAAStS,KAAK,CAACM,GAAG,CAAC,CAACnC,OAAiBxD,KAAK6G,IAAI,CAACoM,SAASzP;gBAC/DkV,mBAAmB;oBACjB,GAAGf,QAAQ;oBACXgB,MAAM,AAAChB,CAAAA,SAASgB,IAAI,IAAI,EAAE,AAAD,EAAGhT,GAAG,CAAC,CAACiT,UAA2B,CAAA;4BAC1D,GAAGA,OAAO;4BACVlC,UAAU1W,KAAK6G,IAAI,CAACoM,SAAS2F,QAAQlC,QAAQ;wBAC/C,CAAA;gBACF;gBACAmC,MAAMlB,SAASkB,IAAI;gBACnBC,UAAU;gBACV7F;YACF;YACA,MAAMkB,MAAM,AACV,CAAA,MAAMzL,QAAQgN,OAAO,CAACqD,QAAQ,CAAC,CAAC,WAAW,EAAEpB,SAASkB,IAAI,EAAE,CAAC,AAAD,EAC5DjF,YAAY;YAEd,qCAAqC;YACrC,MAAM5K,gBAAgB,CAAC;YAEvBwP,oBAAoBzW,kBAAkBoS;YACtCkE,mBAAmB;gBACjBW,WAAW7E,IAAIC,OAAO;gBACtB6E,UAAU9E,IAAI8E,QAAQ;gBACtBC,KAAK/E,IAAI+E,GAAG;gBACZC,aAAahF,IAAIgF,WAAW;gBAC5B/J;gBACAwE,cAAcO;gBACdiF,YAAYjF,IAAIL,MAAM,IAAI,CAAC;gBAC3B9K;gBACAqQ,uBAAuB,CAAC;gBACxBC,oBAAoBnF,IAAImF,kBAAkB;gBAC1CjJ,gBAAgB8D,IAAI9D,cAAc;gBAClCkJ,gBAAgBpF,IAAIoF,cAAc;YACpC;QACF,OAAO;YACLlB,mBAAmB,MAAM7W,eAAe;gBACtCyR;gBACA7D,MAAMyI,mBAAmBzI;gBACzBoK,WAAW5B,aAAa;gBACxB6B,OAAO;YACT;QACF;QACA,MAAMC,OAAOrB,iBAAiBW,SAAS;QACvC,IAAI1I;QAEJ,MAAM6I,cAA2Bd,iBAAiBc,WAAW;QAE7D,IAAInO,oBAA6B;QAEjC,IAAI4M,aAAa,OAAO;YACtB,MAAMhE,eAA8ByE,iBAAiBzE,YAAY;YAEjE4E,oBAAoBzW,kBAAkBsW,iBAAiBzE,YAAY;YAEnE,IAAIR;YACJ,IAAI;gBACFA,WAAW,MAAMrQ,gBAAgBsV;YACnC,EAAE,OAAOsB,KAAK;gBACZ,MAAM,IAAIpK,MAAM,CAAC,oCAAoC,EAAEH,MAAM,EAAE;oBAC7DwK,OAAOD;gBACT;YACF;YAEApB,YAAYsB,gBAAgB,MAAM9W,gBAAgBsV;YAElD,IAAIE,UAAUuB,OAAO,KAAK,kBAAkBrB,mBAAmB;gBAC7DlX,IAAIwY,IAAI,CACN,CAAC,MAAM,EAAE3K,KAAK,gKAAgK,CAAC;YAEnL;YAEA,uEAAuE;YACvE,wEAAwE;YACxE,uBAAuB;YACvBpE,oBACEmO,YAAYa,UAAU,CAACC,IAAI,KAAK3X,UAAU4X,QAAQ,IAClD,CAACzX,2BAA2B2M,SAC5B1M,uBAAuBqV,WAAWQ;YAEpC,uEAAuE;YACvE,mBAAmB;YACnB,yDAAyD;YACzD,IAAIA,UAAUuB,OAAO,KAAK,mBAAmB,CAAC9O,mBAAmB;gBAC/DuN,UAAU4B,UAAU,GAAG;YACzB;YAEA,IAAIlZ,eAAemO,OAAO;;gBACtB,CAAA,EAAE0D,cAAcwF,qBAAqB,EAAE3H,iBAAiB,EAAE,GAC1D,MAAMoC,oBAAoB;oBACxBC;oBACA5D;oBACA8D;oBACAC;oBACA5C;oBACA6C;oBACAH;oBACAO,gBAAgB,CAAC;oBACjBH;oBACAI;oBACAH;oBACAC;oBACAK;oBACAD;oBACA3I;oBACAnC;gBACF,EAAC;YACL;QACF,OAAO;YACL,IAAI,CAAC6Q,QAAQ,CAACvZ,mBAAmBuZ,SAAS,OAAOA,SAAS,UAAU;gBAClE,MAAM,IAAInK,MAAM;YAClB;QACF;QAEA,MAAM6K,qBAAqB,CAAC,EAACV,wBAAAA,KAAMW,eAAe;QAClD,MAAMC,iBAAiB,CAAC,CAACjC,iBAAiBkB,cAAc;QACxD,MAAMgB,iBAAiB,CAAC,CAAClC,iBAAiBhI,cAAc;QACxD,MAAMmK,iBAAiB,CAAC,CAACnC,iBAAiBiB,kBAAkB;QAE5D,uEAAuE;QACvE,iBAAiB;QACjB,IAAIc,sBAAsBE,gBAAgB;YACxC,MAAM,IAAI/K,MAAMjP;QAClB;QAEA,IAAI8Z,sBAAsBI,gBAAgB;YACxC,MAAM,IAAIjL,MAAMhP;QAClB;QAEA,IAAI+Z,kBAAkBE,gBAAgB;YACpC,MAAM,IAAIjL,MAAM/O;QAClB;QAEA,MAAMia,gBAAgBxZ,eAAemO;QACrC,oEAAoE;QACpE,IAAIkL,kBAAkBC,kBAAkB,CAACE,eAAe;YACtD,MAAM,IAAIlL,MACR,CAAC,yDAAyD,EAAEH,KAAK,EAAE,CAAC,GAClE,CAAC,4DAA4D,CAAC;QAEpE;QAEA,IAAIkL,kBAAkBG,iBAAiB,CAACF,gBAAgB;YACtD,MAAM,IAAIhL,MACR,CAAC,qEAAqE,EAAEH,KAAK,EAAE,CAAC,GAC9E,CAAC,0EAA0E,CAAC;QAElF;QAEA,IAAI,AAACkL,kBAAkBC,kBAAmBjK,mBAAmB;;YACzD,CAAA,EAAEwC,cAAcwF,qBAAqB,EAAE3H,iBAAiB,EAAE,GAC1D,MAAMP,iBAAiB;gBACrBhB;gBACAoB;gBACAC;gBACAF;gBACAD;gBACAD,gBAAgBgI,iBAAiBhI,cAAc;YACjD,EAAC;QACL;QAEA,MAAMqK,sBAAsB,AAACC,WAAmBC,qBAAqB;QACrE,MAAM9G,SAAqB0E,oBACvB,CAAC,IACDH,iBAAiBe,UAAU;QAE/B,IAAIhO,WAAW;QACf,IAAI,CAACkP,kBAAkB,CAACF,sBAAsB,CAACI,gBAAgB;YAC7DpP,WAAW;QACb;QAEA,8DAA8D;QAC9D,6BAA6B;QAC7B,IAAIJ,mBAAmB;YACrBI,WAAW;QACb;QAEA,OAAO;YACLA;YACAJ;YACA1E,aAAawN,OAAO+G,GAAG,KAAK;YAC5BC,WAAWhH,OAAO+G,GAAG,KAAK;YAC1BvC;YACA3H;YACA2J;YACAE;YACAE;YACAnC;QACF;IACF,GACCwC,KAAK,CAAC,CAACpB;QACN,IAAIA,IAAIqB,OAAO,KAAK,0BAA0B;YAC5C,MAAMrB;QACR;QACAxW,QAAQ8X,KAAK,CAACtB;QACd,MAAM,IAAIpK,MAAM,CAAC,gCAAgC,EAAEH,MAAM;IAC3D;AACJ;AAaA;;;;;;CAMC,GACD,OAAO,SAASyK,gBACdzG,QAAsC;IAEtC,MAAMU,SAA2B,CAAC;IAElC,KAAK,MAAMpC,WAAW0B,SAAU;QAC9B,MAAM,EACJ0G,OAAO,EACPzD,UAAU,EACV6E,eAAe,EACff,UAAU,EACVgB,gBAAgB,EAChBzS,OAAO,EACP0S,WAAW,EACZ,GAAG1J,QAAQoC,MAAM,IAAI,CAAC;QAEvB,uDAAuD;QACvD,6DAA6D;QAE7D,IAAI,OAAOoH,oBAAoB,aAAa;YAC1CpH,OAAOoH,eAAe,GAAGA;QAC3B;QAEA,IAAI,OAAOpB,YAAY,aAAa;YAClChG,OAAOgG,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAOzD,eAAe,aAAa;YACrCvC,OAAOuC,UAAU,GAAGA;QACtB;QAEA,IAAI,OAAO8D,eAAe,aAAa;YACrCrG,OAAOqG,UAAU,GAAGA;QACtB;QAEA,0EAA0E;QAC1E,sBAAsB;QACtB,IACE,OAAOA,eAAe,YACrB,CAAA,OAAOrG,OAAOqG,UAAU,KAAK,YAAYA,aAAarG,OAAOqG,UAAU,AAAD,GACvE;YACArG,OAAOqG,UAAU,GAAGA;QACtB;QAEA,wEAAwE;QACxE,oEAAoE;QACpE,IAAI,OAAOgB,qBAAqB,aAAa;YAC3CrH,OAAOqH,gBAAgB,GAAGA;QAC5B;QAEA,IAAI,OAAOzS,YAAY,aAAa;YAClCoL,OAAOpL,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAO0S,gBAAgB,aAAa;YACtCtH,OAAOsH,WAAW,GAAGA;QACvB;IACF;IAEA,OAAOtH;AACT;AAEA,OAAO,eAAeuH,yBAAyB,EAC7CjM,IAAI,EACJ6D,OAAO,EACPsE,gBAAgB,EAChB+D,WAAW,EAMZ;IACCnD,QAAQ,yCAAyCC,SAAS,CAACb;IAE3D,MAAMgE,aAAa,MAAM/Z,eAAe;QACtCyR;QACA7D,MAAMA;QACNoK,WAAW;QACXC,OAAO;IACT;IACA,IAAItF,MAAMoH,WAAW3H,YAAY;IAEjC,IAAI0H,aAAa;QACfnH,MAAM,AAAC,MAAMA,IAAIqH,IAAI,IAAKrH,IAAIC,OAAO,IAAID;IAC3C,OAAO;QACLA,MAAMA,IAAIC,OAAO,IAAID;IACvB;IACAA,MAAM,MAAMA;IACZ,OAAOA,IAAIkG,eAAe,KAAKlG,IAAIsH,mBAAmB;AACxD;AAEA,OAAO,eAAeC,uBAAuB,EAC3CtM,IAAI,EACJ6D,OAAO,EACPsE,gBAAgB,EAKjB;IACCY,QAAQ,yCAAyCC,SAAS,CAACb;IAC3D,MAAMgE,aAAa,MAAM/Z,eAAe;QACtCyR;QACA7D,MAAMA;QACNoK,WAAW;QACXC,OAAO;IACT;IAEA,OAAOnU,OAAOqB,IAAI,CAAC4U,WAAW3H,YAAY,EAAEtP,MAAM,CAAC,CAACsB;QAClD,OAAO,OAAO2V,WAAW3H,YAAY,CAAChO,IAAI,KAAK;IACjD;AACF;AAEA,OAAO,SAAS+V,uBACdC,aAAuB,EACvBC,QAAqB,EACrBC,2BAAkD;IAElD,MAAMC,mBAAmB,IAAI5V;IAQ7B,MAAM6V,kBAAkB;WAAIH;KAAS,CAACvX,MAAM,CAAC,CAAC8K,OAASnO,eAAemO;IACtE,MAAM6M,2BAEF,CAAC;IAELH,4BAA4B1R,OAAO,CAAC,CAACgH,OAAO8K;QAC1CD,wBAAwB,CAACC,UAAU,KAAK,CAAC;QACzC9K,MAAMhH,OAAO,CAAC,CAAC+R;YACb,MAAMC,cAAcD,QAAQE,WAAW;YACvCJ,wBAAwB,CAACC,UAAU,CAACE,YAAY,GAAGD;QACrD;IACF;IAEAL,4BAA4B1R,OAAO,CAAC,CAACgH,OAAO8K;QAC1C9K,MAAMhH,OAAO,CAAC,CAAC+R;YACb,MAAMG,YAAYH,QAAQE,WAAW;YACrC,IAAIE,kBAAkBX,cAAcY,IAAI,CACtC,CAACpN,OAASA,KAAKiN,WAAW,OAAOC;YAGnC,IAAIC,iBAAiB;gBACnBR,iBAAiBjW,GAAG,CAACwW,WAAW;oBAC9B;wBAAEtc,MAAMmc;wBAAS/M,MAAM8M;oBAAU;oBACjC;wBAAElc,MAAMuc;wBAAiBnN,MAAMmN;oBAAgB;iBAChD;YACH,OAAO;gBACL,IAAIE;gBAEJF,kBAAkBP,gBAAgBQ,IAAI,CAAC,CAACpN;oBACtC,IAAIA,SAAS8M,WAAW,OAAO;oBAE/BO,kBACEX,4BAA4B9V,GAAG,CAACoJ,SAAS,OACrC3H,YACAwU,wBAAwB,CAAC7M,KAAK,CAACkN,UAAU;oBAC/C,OAAOG;gBACT;gBAEA,IAAIF,mBAAmBE,iBAAiB;oBACtCV,iBAAiBjW,GAAG,CAACwW,WAAW;wBAC9B;4BAAEtc,MAAMmc;4BAAS/M,MAAM8M;wBAAU;wBACjC;4BAAElc,MAAMyc;4BAAiBrN,MAAMmN;wBAAgB;qBAChD;gBACH;YACF;QACF;IACF;IAEA,IAAIR,iBAAiBnY,IAAI,GAAG,GAAG;QAC7B,IAAI8Y,yBAAyB;QAE7BX,iBAAiB3R,OAAO,CAAC,CAACuS;YACxBA,UAAUvS,OAAO,CAAC,CAACwS,UAAUrQ;gBAC3B,MAAMsQ,YAAYD,SAASxN,IAAI,KAAKwN,SAAS5c,IAAI;gBAEjD,IAAIuM,MAAM,GAAG;oBACXmQ,0BAA0B;gBAC5B;gBAEAA,0BAA0B,CAAC,OAAO,EAAEE,SAAS5c,IAAI,CAAC,CAAC,EACjD6c,YAAY,CAAC,aAAa,EAAED,SAASxN,IAAI,CAAC,EAAE,CAAC,GAAG,KAChD;YACJ;YACAsN,0BAA0B;QAC5B;QAEAnb,IAAI0Z,KAAK,CACP,qFACE,mFACAyB;QAEJ5F,QAAQgG,IAAI,CAAC;IACf;AACF;AAEA,OAAO,eAAeC,gBACpB/J,GAAW,EACXC,OAAe,EACf+J,QAA2B,EAC3BC,WAA0C,EAC1CC,WAAmB,EACnBC,YAAwB,EACxBjU,kBAAsC,EACtCkU,sBAA+B,EAC/BC,WAAwB;IAExB,MAAMC,aAAatd,KAAK6G,IAAI,CAACoM,SAAS;IACtC,IAAIsK,aAAa;IACjB,MAAMC,aAAa;QACjB,GAAGL,YAAY;QACflK,SAAS,CAAC,EAAE,EAAEjT,KAAKyW,QAAQ,CAACzD,KAAKC,UAAU;IAC7C;IACA,IAAI;QACF,MAAMwK,kBAAkBzd,KAAK6G,IAAI,CAACoM,SAAS;QAC3C,MAAMyK,cAAcC,KAAKC,KAAK,CAAC,MAAM1d,GAAG2d,QAAQ,CAACJ,iBAAiB;QAClEF,aAAaG,YAAYxP,IAAI,KAAK;IACpC,EAAE,OAAM,CAAC;IACT,MAAM4P,cAAc,IAAI5Z;IACxB,MAAMhE,GAAG6d,EAAE,CAACT,YAAY;QAAEU,WAAW;QAAMC,OAAO;IAAK;IAEvD,eAAeC,iBAAiBC,aAAqB;QACnD,MAAMC,YAAYT,KAAKC,KAAK,CAAC,MAAM1d,GAAG2d,QAAQ,CAACM,eAAe;QAG9D,MAAME,WAAW,IAAI1c,KAAK,IAAI;YAAE2c,UAAUF,UAAU/Y,KAAK,CAAC6E,MAAM;QAAC;QACjE,MAAMqU,eAAeve,KAAKwe,OAAO,CAACL;QAElC,MAAM1X,QAAQC,GAAG,CACf0X,UAAU/Y,KAAK,CAACM,GAAG,CAAC,OAAO8Y;YACzB,MAAMJ,SAASK,OAAO;YAEtB,MAAMC,iBAAiB3e,KAAK6G,IAAI,CAAC0X,cAAcE;YAC/C,MAAMG,iBAAiB5e,KAAK6G,IAAI,CAC9ByW,YACAtd,KAAKyW,QAAQ,CAACyG,aAAayB;YAG7B,IAAI,CAACb,YAAYtZ,GAAG,CAACoa,iBAAiB;gBACpCd,YAAYxS,GAAG,CAACsT;gBAEhB,MAAM1e,GAAG2e,KAAK,CAAC7e,KAAKwe,OAAO,CAACI,iBAAiB;oBAAEZ,WAAW;gBAAK;gBAC/D,MAAMc,UAAU,MAAM5e,GAAG6e,QAAQ,CAACJ,gBAAgB5D,KAAK,CAAC,IAAM;gBAE9D,IAAI+D,SAAS;oBACX,IAAI;wBACF,MAAM5e,GAAG4e,OAAO,CAACA,SAASF;oBAC5B,EAAE,OAAO3W,GAAQ;wBACf,IAAIA,EAAE+W,IAAI,KAAK,UAAU;4BACvB,MAAM/W;wBACR;oBACF;gBACF,OAAO;oBACL,MAAM/H,GAAG+e,QAAQ,CAACN,gBAAgBC;gBACpC;YACF;YAEA,MAAMP,SAASa,OAAO;QACxB;IAEJ;IAEA,eAAeC,mBAAmB/P,IAA4B;YAa1DA,YACAA;QAbF,eAAegQ,WAAW5b,IAAY;YACpC,MAAM6b,eAAerf,KAAK6G,IAAI,CAACoM,SAASzP;YACxC,MAAMob,iBAAiB5e,KAAK6G,IAAI,CAC9ByW,YACAtd,KAAKyW,QAAQ,CAACyG,aAAajK,UAC3BzP;YAEF,MAAMtD,GAAG2e,KAAK,CAAC7e,KAAKwe,OAAO,CAACI,iBAAiB;gBAAEZ,WAAW;YAAK;YAC/D,MAAM9d,GAAG+e,QAAQ,CAACI,cAAcT;QAClC;QACA,MAAMnY,QAAQC,GAAG,CAAC;YAChB0I,KAAK/J,KAAK,CAACM,GAAG,CAACyZ;aACfhQ,aAAAA,KAAKuJ,IAAI,qBAATvJ,WAAWzJ,GAAG,CAAC,CAACnC,OAAS4b,WAAW5b,KAAKkT,QAAQ;aACjDtH,eAAAA,KAAKkQ,MAAM,qBAAXlQ,aAAazJ,GAAG,CAAC,CAACnC,OAAS4b,WAAW5b,KAAKkT,QAAQ;SACpD;IACH;IAEA,MAAM6I,uBAAuC,EAAE;IAE/C,KAAK,MAAMjS,cAAchI,OAAOka,MAAM,CAACtW,mBAAmBoE,UAAU,EAAG;QACrE,IAAI3F,qBAAqB2F,WAAWuL,IAAI,GAAG;YACzC0G,qBAAqBnY,IAAI,CAAC+X,mBAAmB7R;QAC/C;IACF;IAEA,KAAK,MAAM8B,QAAQ9J,OAAOka,MAAM,CAACtW,mBAAmBuW,SAAS,EAAG;QAC9DF,qBAAqBnY,IAAI,CAAC+X,mBAAmB/P;IAC/C;IAEA,MAAM3I,QAAQC,GAAG,CAAC6Y;IAElB,KAAK,MAAMnQ,QAAQ4N,SAAU;QAC3B,IAAI9T,mBAAmBuW,SAAS,CAACjN,cAAc,CAACpD,OAAO;YACrD;QACF;QACA,MAAM5G,QAAQ3G,kBAAkBuN;QAEhC,IAAIiO,YAAY7Y,GAAG,CAACgE,QAAQ;YAC1B;QACF;QAEA,MAAMkX,WAAW1f,KAAK6G,IAAI,CACxBoM,SACA,UACA,SACA,GAAGpR,kBAAkBuN,MAAM,GAAG,CAAC;QAEjC,MAAMuQ,gBAAgB,GAAGD,SAAS,SAAS,CAAC;QAC5C,MAAMxB,iBAAiByB,eAAe5E,KAAK,CAAC,CAACpB;YAC3C,IAAIA,IAAIqF,IAAI,KAAK,YAAa5P,SAAS,UAAUA,SAAS,QAAS;gBACjE7N,IAAIwY,IAAI,CAAC,CAAC,gCAAgC,EAAE2F,UAAU,EAAE/F;YAC1D;QACF;IACF;IAEA,IAAIsD,aAAa;QACf,KAAK,MAAM7N,QAAQ6N,YAAa;YAC9B,IAAI/T,mBAAmBuW,SAAS,CAACjN,cAAc,CAACpD,OAAO;gBACrD;YACF;YACA,MAAMsQ,WAAW1f,KAAK6G,IAAI,CAACoM,SAAS,UAAU,OAAO,GAAG7D,KAAK,GAAG,CAAC;YACjE,MAAMuQ,gBAAgB,GAAGD,SAAS,SAAS,CAAC;YAC5C,MAAMxB,iBAAiByB,eAAe5E,KAAK,CAAC,CAACpB;gBAC3CpY,IAAIwY,IAAI,CAAC,CAAC,gCAAgC,EAAE2F,UAAU,EAAE/F;YAC1D;QACF;IACF;IAEA,IAAIyD,wBAAwB;QAC1B,MAAMc,iBACJle,KAAK6G,IAAI,CAACoM,SAAS,UAAU;IAEjC;IAEA,MAAMiL,iBAAiBle,KAAK6G,IAAI,CAACoM,SAAS;IAC1C,MAAM2M,mBAAmB5f,KAAK6G,IAAI,CAChCyW,YACAtd,KAAKyW,QAAQ,CAACyG,aAAalK,MAC3B;IAEF,MAAM9S,GAAG2e,KAAK,CAAC7e,KAAKwe,OAAO,CAACoB,mBAAmB;QAAE5B,WAAW;IAAK;IAEjE,MAAM9d,GAAG2f,SAAS,CAChBD,kBACA,GACErC,aACI,CAAC;;;;;;AAMX,CAAC,GACS,CAAC,4BAA4B,CAAC,CACnC;;;;;;;;;;;mBAWc,EAAEI,KAAKmC,SAAS,CAACtC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;GA0B7C,CAAC;AAEJ;AAEA,OAAO,SAASuC,eAAe3Q,IAAY;IACzC,OAAO/L,cAAc2c,IAAI,CAAC5Q;AAC5B;AAEA,OAAO,SAAS6Q,yBAAyB7Q,IAAY;IACnD,OAAO,8DAA8D4Q,IAAI,CACvE5Q;AAEJ;AAEA,OAAO,SAAS8Q,kBAAkB9Q,IAAY;IAC5C,OAAOA,SAAS,UAAUA,SAAS;AACrC;AAEA,OAAO,SAAS+Q,iBAAiB3c,IAAY;IAC3C,OACEA,SAAS,CAAC,CAAC,EAAE/C,qBAAqB,IAAI+C,SAAS,CAAC,KAAK,EAAE/C,qBAAqB;AAEhF;AAEA,OAAO,SAAS2f,0BAA0B5c,IAAY;IACpD,OACEA,SAAS,CAAC,CAAC,EAAE9C,+BAA+B,IAC5C8C,SAAS,CAAC,KAAK,EAAE9C,+BAA+B;AAEpD;AAEA,OAAO,SAAS2f,wCACdC,MAAc,EACdC,UAAoB;IAEpB,MAAMlb,QAAQ,EAAE;IAChB,KAAK,MAAMmb,aAAaD,WAAY;QAClClb,MAAM+B,IAAI,CACRpH,KAAK6G,IAAI,CAACyZ,QAAQ,GAAG5f,8BAA8B,CAAC,EAAE8f,WAAW,GACjExgB,KAAK6G,IAAI,CAACyZ,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG5f,8BAA8B,CAAC,EAAE8f,WAAW;IAE5E;IAEA,OAAOnb;AACT;AAEA,OAAO,SAASob,+BACdH,MAAc,EACdC,UAAoB;IAEpB,OAAOA,WAAW5a,GAAG,CAAC,CAAC6a,YACrBxgB,KAAK6G,IAAI,CAACyZ,QAAQ,GAAG7f,oBAAoB,CAAC,EAAE+f,WAAW;AAE3D;AAEA,OAAO,MAAME,8BAA8BnR;IACzCoR,YACEC,eAAyB,EACzBC,OAAe,EACfC,aAAqB,CACrB;QACA,KAAK,CACH,CAAC,0CAA0C,CAAC,GAC1C,GAAGF,gBAAgBjb,GAAG,CAAC,CAACnC,OAAS,CAAC,KAAK,EAAEA,MAAM,EAAEqD,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/D,CAAC,0CAA0C,EAAE7G,KAAK6G,IAAI,CACpD7G,KAAK+gB,KAAK,CAACC,GAAG,EACdhhB,KAAKyW,QAAQ,CAACoK,SAAS7gB,KAAKihB,OAAO,CAACH,eAAe,QACnD,cACA,WAAW,CAAC,GACd,CAAC,8DAA8D,CAAC;IAEtE;AACF;AAEA,OAAO,SAASI,qBACdlO,GAAW,EACXmO,aAAsB;IAEtB,IAAIC;IACJ,IAAI;QACF,MAAMC,qBAAqBhhB,aAAaihB,UAAU,CAAC;YACjDthB,MAAMgT;YACN+D,KAAKoK,gBAAgB,gBAAgB;QACvC;QACA,8FAA8F;QAC9F,IAAIE,sBAAsBA,mBAAmBnX,MAAM,GAAG,GAAG;YACvDkX,WAAW/gB,aAAaghB;QAC1B;IACF,EAAE,OAAM,CAAC;IAET,6CAA6C;IAC7C,IAAID,YAAYA,SAASlX,MAAM,GAAG,GAAG;QACnC,OAAOkX;IACT;IAEA,uCAAuC;IACvC,OAAOxgB;AACT;AAEA,OAAO,SAAS2gB,yBACdC,KAA0C;IAE1C,OAAOC,QACLD,SAAS7gB,eAAe+gB,KAAK,CAACC,UAAU,CAAChX,QAAQ,CAAC6W;AAEtD;AAEA,OAAO,SAASI,yBACdJ,KAA0C;IAE1C,OAAOC,QACLD,SAAS7gB,eAAe+gB,KAAK,CAACG,UAAU,CAAClX,QAAQ,CAAC6W;AAEtD;AAEA,OAAO,SAASM,sBACdN,KAA0C;IAE1C,OAAOA,UAAU,QAAQA,UAAU/Z;AACrC;AAEA,OAAO,SAASsa,sBACdP,KAA0C;IAE1C,OAAOC,QAAQD,SAAS7gB,eAAe+gB,KAAK,CAACM,OAAO,CAACrX,QAAQ,CAAC6W;AAChE;AAEA,OAAO,SAASS,uBACdT,KAA0C;IAE1C,OAAOC,QAAQD,SAAS7gB,eAAe+gB,KAAK,CAACQ,QAAQ,CAACvX,QAAQ,CAAC6W;AACjE;AAEA,OAAO,SAASW,YAAY,EAC1BC,MAAM,EACNpU,OAAO,EAIR;IAIC,MAAMqU,OAGF,CAAC;IAEL,IAAID,WAAW,KAAK;QAClBC,KAAKD,MAAM,GAAGA;IAChB;IAEA,IAAIpU,WAAW1I,OAAOqB,IAAI,CAACqH,SAAS9D,MAAM,EAAE;QAC1CmY,KAAKrU,OAAO,GAAG,CAAC;QAEhB,4CAA4C;QAC5C,iCAAiC;QACjC,IAAK,MAAMpI,OAAOoI,QAAS;YACzB,qEAAqE;YACrE,sEAAsE;YACtE,IAAIpI,QAAQ,2BAA2B;YAEvC,IAAIkJ,QAAQd,OAAO,CAACpI,IAAI;YAExB,IAAIoL,MAAMC,OAAO,CAACnC,QAAQ;gBACxB,IAAIlJ,QAAQ,cAAc;oBACxBkJ,QAAQA,MAAMjI,IAAI,CAAC;gBACrB,OAAO;oBACLiI,QAAQA,KAAK,CAACA,MAAM5E,MAAM,GAAG,EAAE;gBACjC;YACF;YAEA,IAAI,OAAO4E,UAAU,UAAU;gBAC7BuT,KAAKrU,OAAO,CAACpI,IAAI,GAAGkJ;YACtB;QACF;IACF;IAEA,OAAOuT;AACT"}