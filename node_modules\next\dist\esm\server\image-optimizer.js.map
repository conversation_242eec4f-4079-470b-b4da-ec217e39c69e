{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "sourcesContent": ["import { createHash } from 'crypto'\nimport { promises } from 'fs'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport { mediaType } from 'next/dist/compiled/@hapi/accept'\nimport contentDisposition from 'next/dist/compiled/content-disposition'\nimport imageSizeOf from 'next/dist/compiled/image-size'\nimport isAnimated from 'next/dist/compiled/is-animated'\nimport { join } from 'path'\nimport nodeUrl, { type UrlWithParsedQuery } from 'url'\n\nimport { getImageBlurSvg } from '../shared/lib/image-blur-svg'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport { hasLocalMatch } from '../shared/lib/match-local-pattern'\nimport { hasRemoteMatch } from '../shared/lib/match-remote-pattern'\nimport type { NextConfigComplete } from './config-shared'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport type { NextUrlWithParsedQuery } from './request-meta'\nimport {\n  CachedRouteKind,\n  type CachedImageValue,\n  type IncrementalCacheEntry,\n  type IncrementalCacheItem,\n  type IncrementalCacheValue,\n} from './response-cache'\nimport { sendEtagResponse } from './send-payload'\nimport { getContentType, getExtension } from './serve-static'\nimport * as Log from '../build/output/log'\nimport isError from '../lib/is-error'\nimport { parseUrl } from '../lib/url'\n\ntype XCacheHeader = 'MISS' | 'HIT' | 'STALE'\n\nconst AVIF = 'image/avif'\nconst WEBP = 'image/webp'\nconst PNG = 'image/png'\nconst JPEG = 'image/jpeg'\nconst GIF = 'image/gif'\nconst SVG = 'image/svg+xml'\nconst ICO = 'image/x-icon'\nconst TIFF = 'image/tiff'\nconst BMP = 'image/bmp'\nconst CACHE_VERSION = 4\nconst ANIMATABLE_TYPES = [WEBP, PNG, GIF]\nconst VECTOR_TYPES = [SVG]\nconst BLUR_IMG_SIZE = 8 // should match `next-image-loader`\nconst BLUR_QUALITY = 70 // should match `next-image-loader`\n\nlet _sharp: typeof import('sharp')\n\nexport function getSharp(concurrency: number | null | undefined) {\n  if (_sharp) {\n    return _sharp\n  }\n  try {\n    _sharp = require('sharp')\n    if (_sharp && _sharp.concurrency() > 1) {\n      // Reducing concurrency should reduce the memory usage too.\n      // We more aggressively reduce in dev but also reduce in prod.\n      // https://sharp.pixelplumbing.com/api-utility#concurrency\n      const divisor = process.env.NODE_ENV === 'development' ? 4 : 2\n      _sharp.concurrency(\n        concurrency ?? Math.floor(Math.max(_sharp.concurrency() / divisor, 1))\n      )\n    }\n  } catch (e: unknown) {\n    if (isError(e) && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        'Module `sharp` not found. Please run `npm install --cpu=wasm32 sharp` to install it.'\n      )\n    }\n    throw e\n  }\n  return _sharp\n}\n\nexport interface ImageParamsResult {\n  href: string\n  isAbsolute: boolean\n  isStatic: boolean\n  width: number\n  quality: number\n  mimeType: string\n  sizes: number[]\n  minimumCacheTTL: number\n}\n\ninterface ImageUpstream {\n  buffer: Buffer\n  contentType: string | null | undefined\n  cacheControl: string | null | undefined\n  etag: string\n}\n\nfunction getSupportedMimeType(options: string[], accept = ''): string {\n  const mimeType = mediaType(accept, options)\n  return accept.includes(mimeType) ? mimeType : ''\n}\n\nexport function getHash(items: (string | number | Buffer)[]) {\n  const hash = createHash('sha256')\n  for (let item of items) {\n    if (typeof item === 'number') hash.update(String(item))\n    else {\n      hash.update(item)\n    }\n  }\n  // See https://en.wikipedia.org/wiki/Base64#URL_applications\n  return hash.digest('base64url')\n}\n\nexport function extractEtag(\n  etag: string | null | undefined,\n  imageBuffer: Buffer\n) {\n  if (etag) {\n    // upstream etag needs to be base64url encoded due to weak etag signature\n    // as we store this in the cache-entry file name.\n    return Buffer.from(etag).toString('base64url')\n  }\n  return getImageEtag(imageBuffer)\n}\n\nexport function getImageEtag(image: Buffer) {\n  return getHash([image])\n}\n\nasync function writeToCacheDir(\n  dir: string,\n  extension: string,\n  maxAge: number,\n  expireAt: number,\n  buffer: Buffer,\n  etag: string,\n  upstreamEtag: string\n) {\n  const filename = join(\n    dir,\n    `${maxAge}.${expireAt}.${etag}.${upstreamEtag}.${extension}`\n  )\n\n  await promises.rm(dir, { recursive: true, force: true }).catch(() => {})\n\n  await promises.mkdir(dir, { recursive: true })\n  await promises.writeFile(filename, buffer)\n}\n\n/**\n * Inspects the first few bytes of a buffer to determine if\n * it matches the \"magic number\" of known file signatures.\n * https://en.wikipedia.org/wiki/List_of_file_signatures\n */\nexport function detectContentType(buffer: Buffer) {\n  if ([0xff, 0xd8, 0xff].every((b, i) => buffer[i] === b)) {\n    return JPEG\n  }\n  if (\n    [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a].every(\n      (b, i) => buffer[i] === b\n    )\n  ) {\n    return PNG\n  }\n  if ([0x47, 0x49, 0x46, 0x38].every((b, i) => buffer[i] === b)) {\n    return GIF\n  }\n  if (\n    [0x52, 0x49, 0x46, 0x46, 0, 0, 0, 0, 0x57, 0x45, 0x42, 0x50].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return WEBP\n  }\n  if ([0x3c, 0x3f, 0x78, 0x6d, 0x6c].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if ([0x3c, 0x73, 0x76, 0x67].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if (\n    [0, 0, 0, 0, 0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return AVIF\n  }\n  if ([0x00, 0x00, 0x01, 0x00].every((b, i) => buffer[i] === b)) {\n    return ICO\n  }\n  if ([0x49, 0x49, 0x2a, 0x00].every((b, i) => buffer[i] === b)) {\n    return TIFF\n  }\n  if ([0x42, 0x4d].every((b, i) => buffer[i] === b)) {\n    return BMP\n  }\n  return null\n}\n\nexport class ImageOptimizerCache {\n  private cacheDir: string\n  private nextConfig: NextConfigComplete\n\n  static validateParams(\n    req: IncomingMessage,\n    query: UrlWithParsedQuery['query'],\n    nextConfig: NextConfigComplete,\n    isDev: boolean\n  ): ImageParamsResult | { errorMessage: string } {\n    const imageData = nextConfig.images\n    const {\n      deviceSizes = [],\n      imageSizes = [],\n      domains = [],\n      minimumCacheTTL = 60,\n      formats = ['image/webp'],\n    } = imageData\n    const remotePatterns = nextConfig.images?.remotePatterns || []\n    const localPatterns = nextConfig.images?.localPatterns\n    const { url, w, q } = query\n    let href: string\n\n    if (domains.length > 0) {\n      Log.warnOnce(\n        'The \"images.domains\" configuration is deprecated. Please use \"images.remotePatterns\" configuration instead.'\n      )\n    }\n\n    if (!url) {\n      return { errorMessage: '\"url\" parameter is required' }\n    } else if (Array.isArray(url)) {\n      return { errorMessage: '\"url\" parameter cannot be an array' }\n    }\n\n    if (url.length > 3072) {\n      return { errorMessage: '\"url\" parameter is too long' }\n    }\n\n    if (url.startsWith('//')) {\n      return {\n        errorMessage: '\"url\" parameter cannot be a protocol-relative URL (//)',\n      }\n    }\n\n    let isAbsolute: boolean\n\n    if (url.startsWith('/')) {\n      href = url\n      isAbsolute = false\n      if (\n        /\\/_next\\/image($|\\/)/.test(\n          decodeURIComponent(parseUrl(url)?.pathname ?? '')\n        )\n      ) {\n        return {\n          errorMessage: '\"url\" parameter cannot be recursive',\n        }\n      }\n      if (!hasLocalMatch(localPatterns, url)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    } else {\n      let hrefParsed: URL\n\n      try {\n        hrefParsed = new URL(url)\n        href = hrefParsed.toString()\n        isAbsolute = true\n      } catch (_error) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!['http:', 'https:'].includes(hrefParsed.protocol)) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!hasRemoteMatch(domains, remotePatterns, hrefParsed)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    }\n\n    if (!w) {\n      return { errorMessage: '\"w\" parameter (width) is required' }\n    } else if (Array.isArray(w)) {\n      return { errorMessage: '\"w\" parameter (width) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(w)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    if (!q) {\n      return { errorMessage: '\"q\" parameter (quality) is required' }\n    } else if (Array.isArray(q)) {\n      return { errorMessage: '\"q\" parameter (quality) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(q)) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    const width = parseInt(w, 10)\n\n    if (width <= 0 || isNaN(width)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    const sizes = [...(deviceSizes || []), ...(imageSizes || [])]\n\n    if (isDev) {\n      sizes.push(BLUR_IMG_SIZE)\n    }\n\n    const isValidSize =\n      sizes.includes(width) || (isDev && width <= BLUR_IMG_SIZE)\n\n    if (!isValidSize) {\n      return {\n        errorMessage: `\"w\" parameter (width) of ${width} is not allowed`,\n      }\n    }\n\n    const quality = parseInt(q, 10)\n\n    if (isNaN(quality) || quality < 1 || quality > 100) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    const mimeType = getSupportedMimeType(formats || [], req.headers['accept'])\n\n    const isStatic = url.startsWith(\n      `${nextConfig.basePath || ''}/_next/static/media`\n    )\n\n    return {\n      href,\n      sizes,\n      isAbsolute,\n      isStatic,\n      width,\n      quality,\n      mimeType,\n      minimumCacheTTL,\n    }\n  }\n\n  static getCacheKey({\n    href,\n    width,\n    quality,\n    mimeType,\n  }: {\n    href: string\n    width: number\n    quality: number\n    mimeType: string\n  }): string {\n    return getHash([CACHE_VERSION, href, width, quality, mimeType])\n  }\n\n  constructor({\n    distDir,\n    nextConfig,\n  }: {\n    distDir: string\n    nextConfig: NextConfigComplete\n  }) {\n    this.cacheDir = join(distDir, 'cache', 'images')\n    this.nextConfig = nextConfig\n  }\n\n  async get(cacheKey: string): Promise<IncrementalCacheEntry | null> {\n    try {\n      const cacheDir = join(this.cacheDir, cacheKey)\n      const files = await promises.readdir(cacheDir)\n      const now = Date.now()\n\n      for (const file of files) {\n        const [maxAgeSt, expireAtSt, etag, upstreamEtag, extension] =\n          file.split('.', 5)\n        const buffer = await promises.readFile(join(cacheDir, file))\n        const expireAt = Number(expireAtSt)\n        const maxAge = Number(maxAgeSt)\n\n        return {\n          value: {\n            kind: CachedRouteKind.IMAGE,\n            etag,\n            buffer,\n            extension,\n            upstreamEtag,\n          },\n          revalidateAfter:\n            Math.max(maxAge, this.nextConfig.images.minimumCacheTTL) * 1000 +\n            Date.now(),\n          curRevalidate: maxAge,\n          isStale: now > expireAt,\n          isFallback: false,\n        }\n      }\n    } catch (_) {\n      // failed to read from cache dir, treat as cache miss\n    }\n    return null\n  }\n  async set(\n    cacheKey: string,\n    value: IncrementalCacheValue | null,\n    {\n      revalidate,\n    }: {\n      revalidate?: number | false\n    }\n  ) {\n    if (value?.kind !== CachedRouteKind.IMAGE) {\n      throw new Error('invariant attempted to set non-image to image-cache')\n    }\n\n    if (typeof revalidate !== 'number') {\n      throw new Error('invariant revalidate must be a number for image-cache')\n    }\n    const expireAt =\n      Math.max(revalidate, this.nextConfig.images.minimumCacheTTL) * 1000 +\n      Date.now()\n\n    try {\n      await writeToCacheDir(\n        join(this.cacheDir, cacheKey),\n        value.extension,\n        revalidate,\n        expireAt,\n        value.buffer,\n        value.etag,\n        value.upstreamEtag\n      )\n    } catch (err) {\n      Log.error(`Failed to write image to cache ${cacheKey}`, err)\n    }\n  }\n}\nexport class ImageError extends Error {\n  statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n\n    // ensure an error status is used > 400\n    if (statusCode >= 400) {\n      this.statusCode = statusCode\n    } else {\n      this.statusCode = 500\n    }\n  }\n}\n\nfunction parseCacheControl(\n  str: string | null | undefined\n): Map<string, string> {\n  const map = new Map<string, string>()\n  if (!str) {\n    return map\n  }\n  for (let directive of str.split(',')) {\n    let [key, value] = directive.trim().split('=', 2)\n    key = key.toLowerCase()\n    if (value) {\n      value = value.toLowerCase()\n    }\n    map.set(key, value)\n  }\n  return map\n}\n\nexport function getMaxAge(str: string | null | undefined): number {\n  const map = parseCacheControl(str)\n  if (map) {\n    let age = map.get('s-maxage') || map.get('max-age') || ''\n    if (age.startsWith('\"') && age.endsWith('\"')) {\n      age = age.slice(1, -1)\n    }\n    const n = parseInt(age, 10)\n    if (!isNaN(n)) {\n      return n\n    }\n  }\n  return 0\n}\nexport function getPreviouslyCachedImageOrNull(\n  upstreamImage: ImageUpstream,\n  previousCacheEntry: IncrementalCacheItem | undefined\n): CachedImageValue | null {\n  if (\n    previousCacheEntry?.value?.kind === 'IMAGE' &&\n    // Images that are SVGs, animated or failed the optimization previously end up using upstreamEtag as their etag as well,\n    // in these cases we want to trigger a new \"optimization\" attempt.\n    previousCacheEntry.value.upstreamEtag !== previousCacheEntry.value.etag &&\n    // and the upstream etag is the same as the previous cache entry's\n    upstreamImage.etag === previousCacheEntry.value.upstreamEtag\n  ) {\n    return previousCacheEntry.value\n  }\n  return null\n}\n\nexport async function optimizeImage({\n  buffer,\n  contentType,\n  quality,\n  width,\n  height,\n  concurrency,\n  limitInputPixels,\n  sequentialRead,\n  timeoutInSeconds,\n}: {\n  buffer: Buffer\n  contentType: string\n  quality: number\n  width: number\n  height?: number\n  concurrency?: number | null\n  limitInputPixels?: number\n  sequentialRead?: boolean | null\n  timeoutInSeconds?: number\n}): Promise<Buffer> {\n  const sharp = getSharp(concurrency)\n  const transformer = sharp(buffer, {\n    limitInputPixels,\n    sequentialRead: sequentialRead ?? undefined,\n  })\n    .timeout({\n      seconds: timeoutInSeconds ?? 7,\n    })\n    .rotate()\n\n  if (height) {\n    transformer.resize(width, height)\n  } else {\n    transformer.resize(width, undefined, {\n      withoutEnlargement: true,\n    })\n  }\n\n  if (contentType === AVIF) {\n    transformer.avif({\n      quality: Math.max(quality - 20, 1),\n      effort: 3,\n    })\n  } else if (contentType === WEBP) {\n    transformer.webp({ quality })\n  } else if (contentType === PNG) {\n    transformer.png({ quality })\n  } else if (contentType === JPEG) {\n    transformer.jpeg({ quality, mozjpeg: true })\n  }\n\n  const optimizedBuffer = await transformer.toBuffer()\n\n  return optimizedBuffer\n}\n\nexport async function fetchExternalImage(href: string): Promise<ImageUpstream> {\n  const res = await fetch(href, {\n    signal: AbortSignal.timeout(7_000),\n  }).catch((err) => err as Error)\n\n  if (res instanceof Error) {\n    const err = res as Error\n    if (err.name === 'TimeoutError') {\n      Log.error('upstream image response timed out for', href)\n      throw new ImageError(\n        504,\n        '\"url\" parameter is valid but upstream response timed out'\n      )\n    }\n    throw err\n  }\n\n  if (!res.ok) {\n    Log.error('upstream image response failed for', href, res.status)\n    throw new ImageError(\n      res.status,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n\n  const buffer = Buffer.from(await res.arrayBuffer())\n  const contentType = res.headers.get('Content-Type')\n  const cacheControl = res.headers.get('Cache-Control')\n  const etag = extractEtag(res.headers.get('ETag'), buffer)\n  return { buffer, contentType, cacheControl, etag }\n}\n\nexport async function fetchInternalImage(\n  href: string,\n  _req: IncomingMessage,\n  _res: ServerResponse,\n  handleRequest: (\n    newReq: IncomingMessage,\n    newRes: ServerResponse,\n    newParsedUrl?: NextUrlWithParsedQuery\n  ) => Promise<void>\n): Promise<ImageUpstream> {\n  try {\n    const mocked = createRequestResponseMocks({\n      url: href,\n      method: _req.method || 'GET',\n      headers: _req.headers,\n      socket: _req.socket,\n    })\n\n    await handleRequest(mocked.req, mocked.res, nodeUrl.parse(href, true))\n    await mocked.res.hasStreamed\n\n    if (!mocked.res.statusCode) {\n      Log.error('image response failed for', href, mocked.res.statusCode)\n      throw new ImageError(\n        mocked.res.statusCode,\n        '\"url\" parameter is valid but internal response is invalid'\n      )\n    }\n\n    const buffer = Buffer.concat(mocked.res.buffers)\n    const contentType = mocked.res.getHeader('Content-Type')\n    const cacheControl = mocked.res.getHeader('Cache-Control')\n    const etag = extractEtag(mocked.res.getHeader('ETag'), buffer)\n\n    return { buffer, contentType, cacheControl, etag }\n  } catch (err) {\n    Log.error('upstream image response failed for', href, err)\n    throw new ImageError(\n      500,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n}\n\nexport async function imageOptimizer(\n  imageUpstream: ImageUpstream,\n  paramsResult: Pick<\n    ImageParamsResult,\n    'href' | 'width' | 'quality' | 'mimeType'\n  >,\n  nextConfig: {\n    experimental: Pick<\n      NextConfigComplete['experimental'],\n      | 'imgOptConcurrency'\n      | 'imgOptMaxInputPixels'\n      | 'imgOptSequentialRead'\n      | 'imgOptTimeoutInSeconds'\n    >\n    images: Pick<\n      NextConfigComplete['images'],\n      'dangerouslyAllowSVG' | 'minimumCacheTTL'\n    >\n  },\n  opts: {\n    isDev?: boolean\n    silent?: boolean\n    previousCacheEntry?: IncrementalCacheItem\n  }\n): Promise<{\n  buffer: Buffer\n  contentType: string\n  maxAge: number\n  etag: string\n  upstreamEtag: string\n  error?: unknown\n}> {\n  const { href, quality, width, mimeType } = paramsResult\n  const { buffer: upstreamBuffer, etag: upstreamEtag } = imageUpstream\n  const maxAge = getMaxAge(imageUpstream.cacheControl)\n\n  const upstreamType =\n    detectContentType(upstreamBuffer) ||\n    imageUpstream.contentType?.toLowerCase().trim()\n\n  if (upstreamType) {\n    if (\n      upstreamType.startsWith('image/svg') &&\n      !nextConfig.images.dangerouslyAllowSVG\n    ) {\n      if (!opts.silent) {\n        Log.error(\n          `The requested resource \"${href}\" has type \"${upstreamType}\" but dangerouslyAllowSVG is disabled`\n        )\n      }\n      throw new ImageError(\n        400,\n        '\"url\" parameter is valid but image type is not allowed'\n      )\n    }\n    if (ANIMATABLE_TYPES.includes(upstreamType) && isAnimated(upstreamBuffer)) {\n      if (!opts.silent) {\n        Log.warnOnce(\n          `The requested resource \"${href}\" is an animated image so it will not be optimized. Consider adding the \"unoptimized\" property to the <Image>.`\n        )\n      }\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge,\n        etag: upstreamEtag,\n        upstreamEtag,\n      }\n    }\n    if (VECTOR_TYPES.includes(upstreamType)) {\n      // We don't warn here because we already know that \"dangerouslyAllowSVG\"\n      // was enabled above, therefore the user explicitly opted in.\n      // If we add more VECTOR_TYPES besides SVG, perhaps we could warn for those.\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge,\n        etag: upstreamEtag,\n        upstreamEtag,\n      }\n    }\n    if (!upstreamType.startsWith('image/') || upstreamType.includes(',')) {\n      if (!opts.silent) {\n        Log.error(\n          \"The requested resource isn't a valid image for\",\n          href,\n          'received',\n          upstreamType\n        )\n      }\n      throw new ImageError(400, \"The requested resource isn't a valid image.\")\n    }\n  }\n\n  let contentType: string\n\n  if (mimeType) {\n    contentType = mimeType\n  } else if (\n    upstreamType?.startsWith('image/') &&\n    getExtension(upstreamType) &&\n    upstreamType !== WEBP &&\n    upstreamType !== AVIF\n  ) {\n    contentType = upstreamType\n  } else {\n    contentType = JPEG\n  }\n  const previouslyCachedImage = getPreviouslyCachedImageOrNull(\n    imageUpstream,\n    opts.previousCacheEntry\n  )\n  if (previouslyCachedImage) {\n    return {\n      buffer: previouslyCachedImage.buffer,\n      contentType,\n      maxAge: opts?.previousCacheEntry?.curRevalidate || maxAge,\n      etag: previouslyCachedImage.etag,\n      upstreamEtag: previouslyCachedImage.upstreamEtag,\n    }\n  }\n\n  try {\n    let optimizedBuffer = await optimizeImage({\n      buffer: upstreamBuffer,\n      contentType,\n      quality,\n      width,\n      concurrency: nextConfig.experimental.imgOptConcurrency,\n      limitInputPixels: nextConfig.experimental.imgOptMaxInputPixels,\n      sequentialRead: nextConfig.experimental.imgOptSequentialRead,\n      timeoutInSeconds: nextConfig.experimental.imgOptTimeoutInSeconds,\n    })\n    if (opts.isDev && width <= BLUR_IMG_SIZE && quality === BLUR_QUALITY) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      const meta = await getImageSize(optimizedBuffer)\n      const blurOpts = {\n        blurWidth: meta.width,\n        blurHeight: meta.height,\n        blurDataURL: `data:${contentType};base64,${optimizedBuffer.toString(\n          'base64'\n        )}`,\n      }\n      optimizedBuffer = Buffer.from(unescape(getImageBlurSvg(blurOpts)))\n      contentType = 'image/svg+xml'\n    }\n    return {\n      buffer: optimizedBuffer,\n      contentType,\n      maxAge: Math.max(maxAge, nextConfig.images.minimumCacheTTL),\n      etag: getImageEtag(optimizedBuffer),\n      upstreamEtag,\n    }\n  } catch (error) {\n    if (upstreamType) {\n      // If we fail to optimize, fallback to the original image\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge: nextConfig.images.minimumCacheTTL,\n        etag: upstreamEtag,\n        upstreamEtag,\n        error,\n      }\n    } else {\n      throw new ImageError(\n        400,\n        'Unable to optimize image and unable to fallback to upstream image'\n      )\n    }\n  }\n}\n\nfunction getFileNameWithExtension(\n  url: string,\n  contentType: string | null\n): string {\n  const [urlWithoutQueryParams] = url.split('?', 1)\n  const fileNameWithExtension = urlWithoutQueryParams.split('/').pop()\n  if (!contentType || !fileNameWithExtension) {\n    return 'image.bin'\n  }\n\n  const [fileName] = fileNameWithExtension.split('.', 1)\n  const extension = getExtension(contentType)\n  return `${fileName}.${extension}`\n}\n\nfunction setResponseHeaders(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  etag: string,\n  contentType: string | null,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  res.setHeader('Vary', 'Accept')\n  res.setHeader(\n    'Cache-Control',\n    isStatic\n      ? 'public, max-age=315360000, immutable'\n      : `public, max-age=${isDev ? 0 : maxAge}, must-revalidate`\n  )\n  if (sendEtagResponse(req, res, etag)) {\n    // already called res.end() so we're finished\n    return { finished: true }\n  }\n  if (contentType) {\n    res.setHeader('Content-Type', contentType)\n  }\n\n  const fileName = getFileNameWithExtension(url, contentType)\n  res.setHeader(\n    'Content-Disposition',\n    contentDisposition(fileName, { type: imagesConfig.contentDispositionType })\n  )\n\n  res.setHeader('Content-Security-Policy', imagesConfig.contentSecurityPolicy)\n  res.setHeader('X-Nextjs-Cache', xCache)\n\n  return { finished: false }\n}\n\nexport function sendResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  extension: string,\n  buffer: Buffer,\n  etag: string,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  const contentType = getContentType(extension)\n  const result = setResponseHeaders(\n    req,\n    res,\n    url,\n    etag,\n    contentType,\n    isStatic,\n    xCache,\n    imagesConfig,\n    maxAge,\n    isDev\n  )\n  if (!result.finished) {\n    res.setHeader('Content-Length', Buffer.byteLength(buffer))\n    res.end(buffer)\n  }\n}\n\nexport async function getImageSize(buffer: Buffer): Promise<{\n  width?: number\n  height?: number\n}> {\n  const { width, height } = imageSizeOf(buffer)\n  return { width, height }\n}\n"], "names": ["createHash", "promises", "mediaType", "contentDisposition", "imageSizeOf", "isAnimated", "join", "nodeUrl", "getImageBlurSvg", "hasLocalMatch", "hasRemoteMatch", "createRequestResponseMocks", "CachedRouteKind", "sendEtagResponse", "getContentType", "getExtension", "Log", "isError", "parseUrl", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "TIFF", "BMP", "CACHE_VERSION", "ANIMATABLE_TYPES", "VECTOR_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "_sharp", "getSharp", "concurrency", "require", "divisor", "process", "env", "NODE_ENV", "Math", "floor", "max", "e", "code", "Error", "getSupportedMimeType", "options", "accept", "mimeType", "includes", "getHash", "items", "hash", "item", "update", "String", "digest", "extractEtag", "etag", "imageBuffer", "<PERSON><PERSON><PERSON>", "from", "toString", "getImageEtag", "image", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "upstreamEtag", "filename", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "detectContentType", "every", "b", "i", "ImageOptimizerCache", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "url", "w", "q", "href", "length", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "test", "decodeURIComponent", "pathname", "hrefParsed", "URL", "_error", "protocol", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "IMAGE", "revalidateAfter", "curRevalidate", "isStale", "<PERSON><PERSON><PERSON><PERSON>", "_", "set", "revalidate", "err", "error", "ImageError", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "getMaxAge", "age", "endsWith", "slice", "n", "getPreviouslyCachedImageOrNull", "upstreamImage", "previousCacheEntry", "optimizeImage", "contentType", "height", "limitInputPixels", "sequentialRead", "timeoutInSeconds", "sharp", "transformer", "undefined", "timeout", "seconds", "rotate", "resize", "withoutEnlargement", "avif", "effort", "webp", "png", "jpeg", "mozjpeg", "optimizedBuffer", "<PERSON><PERSON><PERSON><PERSON>", "fetchExternalImage", "res", "fetch", "signal", "AbortSignal", "name", "ok", "status", "arrayBuffer", "cacheControl", "fetchInternalImage", "_req", "_res", "handleRequest", "mocked", "method", "socket", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageOptimizer", "imageUpstream", "paramsResult", "opts", "upstreamBuffer", "upstreamType", "dangerouslyAllowSVG", "silent", "previouslyCachedImage", "experimental", "imgOptConcurrency", "imgOptMaxInputPixels", "imgOptSequentialRead", "imgOptTimeoutInSeconds", "meta", "getImageSize", "blurOpts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "finished", "type", "contentDispositionType", "contentSecurityPolicy", "sendResponse", "result", "byteLength", "end"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAQ;AACnC,SAASC,QAAQ,QAAQ,KAAI;AAE7B,SAASC,SAAS,QAAQ,kCAAiC;AAC3D,OAAOC,wBAAwB,yCAAwC;AACvE,OAAOC,iBAAiB,gCAA+B;AACvD,OAAOC,gBAAgB,iCAAgC;AACvD,SAASC,IAAI,QAAQ,OAAM;AAC3B,OAAOC,aAA0C,MAAK;AAEtD,SAASC,eAAe,QAAQ,+BAA8B;AAE9D,SAASC,aAAa,QAAQ,oCAAmC;AACjE,SAASC,cAAc,QAAQ,qCAAoC;AAEnE,SAASC,0BAA0B,QAAQ,qBAAoB;AAE/D,SACEC,eAAe,QAKV,mBAAkB;AACzB,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,cAAc,EAAEC,YAAY,QAAQ,iBAAgB;AAC7D,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,aAAa,kBAAiB;AACrC,SAASC,QAAQ,QAAQ,aAAY;AAIrC,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACT;IAAMC;IAAKE;CAAI;AACzC,MAAMO,eAAe;IAACN;CAAI;AAC1B,MAAMO,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,OAAO,SAASC,SAASC,WAAsC;IAC7D,IAAIF,QAAQ;QACV,OAAOA;IACT;IACA,IAAI;QACFA,SAASG,QAAQ;QACjB,IAAIH,UAAUA,OAAOE,WAAW,KAAK,GAAG;YACtC,2DAA2D;YAC3D,8DAA8D;YAC9D,0DAA0D;YAC1D,MAAME,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,IAAI;YAC7DP,OAAOE,WAAW,CAChBA,eAAeM,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACV,OAAOE,WAAW,KAAKE,SAAS;QAEvE;IACF,EAAE,OAAOO,GAAY;QACnB,IAAI3B,QAAQ2B,MAAMA,EAAEC,IAAI,KAAK,oBAAoB;YAC/C,MAAM,IAAIC,MACR;QAEJ;QACA,MAAMF;IACR;IACA,OAAOX;AACT;AAoBA,SAASc,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWhD,UAAU+C,QAAQD;IACnC,OAAOC,OAAOE,QAAQ,CAACD,YAAYA,WAAW;AAChD;AAEA,OAAO,SAASE,QAAQC,KAAmC;IACzD,MAAMC,OAAOtD,WAAW;IACxB,KAAK,IAAIuD,QAAQF,MAAO;QACtB,IAAI,OAAOE,SAAS,UAAUD,KAAKE,MAAM,CAACC,OAAOF;aAC5C;YACHD,KAAKE,MAAM,CAACD;QACd;IACF;IACA,4DAA4D;IAC5D,OAAOD,KAAKI,MAAM,CAAC;AACrB;AAEA,OAAO,SAASC,YACdC,IAA+B,EAC/BC,WAAmB;IAEnB,IAAID,MAAM;QACR,yEAAyE;QACzE,iDAAiD;QACjD,OAAOE,OAAOC,IAAI,CAACH,MAAMI,QAAQ,CAAC;IACpC;IACA,OAAOC,aAAaJ;AACtB;AAEA,OAAO,SAASI,aAAaC,KAAa;IACxC,OAAOd,QAAQ;QAACc;KAAM;AACxB;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdZ,IAAY,EACZa,YAAoB;IAEpB,MAAMC,WAAWpE,KACf8D,KACA,GAAGE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEX,KAAK,CAAC,EAAEa,aAAa,CAAC,EAAEJ,WAAW;IAG9D,MAAMpE,SAAS0E,EAAE,CAACP,KAAK;QAAEQ,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAM7E,SAAS8E,KAAK,CAACX,KAAK;QAAEQ,WAAW;IAAK;IAC5C,MAAM3E,SAAS+E,SAAS,CAACN,UAAUF;AACrC;AAEA;;;;CAIC,GACD,OAAO,SAASS,kBAAkBT,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACU,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACvD,OAAO7D;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC4D,KAAK,CACpD,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAE1B;QACA,OAAO9D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC6D,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAO5D;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAAC2D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAO/D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC8D,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACnE,OAAO3D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC0D,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAO3D;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC0D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAOhE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC+D,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAO1D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOzD;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAACwD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACjD,OAAOxD;IACT;IACA,OAAO;AACT;AAEA,OAAO,MAAM0D;IAIX,OAAOC,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA;QATtB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGd;QACtB,IAAIe;QAEJ,IAAIR,QAAQS,MAAM,GAAG,GAAG;YACtBxF,IAAIyF,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACL,KAAK;YACR,OAAO;gBAAEM,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACR,MAAM;YAC7B,OAAO;gBAAEM,cAAc;YAAqC;QAC9D;QAEA,IAAIN,IAAII,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAEE,cAAc;YAA8B;QACvD;QAEA,IAAIN,IAAIS,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIV,IAAIS,UAAU,CAAC,MAAM;gBAKA3F;YAJvBqF,OAAOH;YACPU,aAAa;YACb,IACE,uBAAuBC,IAAI,CACzBC,mBAAmB9F,EAAAA,YAAAA,SAASkF,yBAATlF,UAAe+F,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLP,cAAc;gBAChB;YACF;YACA,IAAI,CAACjG,cAAc0F,eAAeC,MAAM;gBACtC,OAAO;oBAAEM,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIQ;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIf;gBACrBG,OAAOW,WAAWlD,QAAQ;gBAC1B8C,aAAa;YACf,EAAE,OAAOM,QAAQ;gBACf,OAAO;oBAAEV,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAACvD,QAAQ,CAAC+D,WAAWG,QAAQ,GAAG;gBACtD,OAAO;oBAAEX,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAChG,eAAeqF,SAASG,gBAAgBgB,aAAa;gBACxD,OAAO;oBAAER,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA2C;QACpE,OAAO,IAAI,CAAC,WAAWK,IAAI,CAACV,IAAI;YAC9B,OAAO;gBACLK,cAAc;YAChB;QACF;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA6C;QACtE,OAAO,IAAI,CAAC,WAAWK,IAAI,CAACT,IAAI;YAC9B,OAAO;gBACLI,cACE;YACJ;QACF;QAEA,MAAMY,QAAQC,SAASlB,GAAG;QAE1B,IAAIiB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLZ,cAAc;YAChB;QACF;QAEA,MAAMe,QAAQ;eAAK5B,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACT+B,MAAMC,IAAI,CAAC3F;QACb;QAEA,MAAM4F,cACJF,MAAMtE,QAAQ,CAACmE,UAAW5B,SAAS4B,SAASvF;QAE9C,IAAI,CAAC4F,aAAa;YAChB,OAAO;gBACLjB,cAAc,CAAC,yBAAyB,EAAEY,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASjB,GAAG;QAE5B,IAAIkB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLlB,cACE;YACJ;QACF;QAEA,MAAMxD,WAAWH,qBAAqBkD,WAAW,EAAE,EAAEV,IAAIsC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW1B,IAAIS,UAAU,CAC7B,GAAGpB,WAAWsC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLxB;YACAkB;YACAX;YACAgB;YACAR;YACAM;YACA1E;YACA8C;QACF;IACF;IAEA,OAAOgC,YAAY,EACjBzB,IAAI,EACJe,KAAK,EACLM,OAAO,EACP1E,QAAQ,EAMT,EAAU;QACT,OAAOE,QAAQ;YAACxB;YAAe2E;YAAMe;YAAOM;YAAS1E;SAAS;IAChE;IAEA+E,YAAY,EACVC,OAAO,EACPzC,UAAU,EAIX,CAAE;QACD,IAAI,CAAC0C,QAAQ,GAAG7H,KAAK4H,SAAS,SAAS;QACvC,IAAI,CAACzC,UAAU,GAAGA;IACpB;IAEA,MAAM2C,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAW7H,KAAK,IAAI,CAAC6H,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMrI,SAASsI,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYhF,MAAMa,cAAcJ,UAAU,GACzDqE,KAAKG,KAAK,CAAC,KAAK;gBAClB,MAAMrE,SAAS,MAAMvE,SAAS6I,QAAQ,CAACxI,KAAK6H,UAAUO;gBACtD,MAAMnE,WAAWwE,OAAOH;gBACxB,MAAMtE,SAASyE,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAMrI,gBAAgBsI,KAAK;wBAC3BtF;wBACAY;wBACAH;wBACAI;oBACF;oBACA0E,iBACE1G,KAAKE,GAAG,CAAC2B,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3DyC,KAAKD,GAAG;oBACVY,eAAe9E;oBACf+E,SAASb,MAAMjE;oBACf+E,YAAY;gBACd;YACF;QACF,EAAE,OAAOC,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJnB,QAAgB,EAChBW,KAAmC,EACnC,EACES,UAAU,EAGX,EACD;QACA,IAAIT,CAAAA,yBAAAA,MAAOC,IAAI,MAAKrI,gBAAgBsI,KAAK,EAAE;YACzC,MAAM,IAAIpG,MAAM;QAClB;QAEA,IAAI,OAAO2G,eAAe,UAAU;YAClC,MAAM,IAAI3G,MAAM;QAClB;QACA,MAAMyB,WACJ9B,KAAKE,GAAG,CAAC8G,YAAY,IAAI,CAAChE,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/DyC,KAAKD,GAAG;QAEV,IAAI;YACF,MAAMrE,gBACJ7D,KAAK,IAAI,CAAC6H,QAAQ,EAAEE,WACpBW,MAAM3E,SAAS,EACfoF,YACAlF,UACAyE,MAAMxE,MAAM,EACZwE,MAAMpF,IAAI,EACVoF,MAAMvE,YAAY;QAEtB,EAAE,OAAOiF,KAAK;YACZ1I,IAAI2I,KAAK,CAAC,CAAC,+BAA+B,EAAEtB,UAAU,EAAEqB;QAC1D;IACF;AACF;AACA,OAAO,MAAME,mBAAmB9G;IAG9BmF,YAAY4B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAInB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACuB,KAAKpB,MAAM,GAAGmB,UAAUE,IAAI,GAAGxB,KAAK,CAAC,KAAK;QAC/CuB,MAAMA,IAAIE,WAAW;QACrB,IAAItB,OAAO;YACTA,QAAQA,MAAMsB,WAAW;QAC3B;QACAL,IAAIT,GAAG,CAACY,KAAKpB;IACf;IACA,OAAOiB;AACT;AAEA,OAAO,SAASM,UAAUP,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIO,MAAMP,IAAI7B,GAAG,CAAC,eAAe6B,IAAI7B,GAAG,CAAC,cAAc;QACvD,IAAIoC,IAAI3D,UAAU,CAAC,QAAQ2D,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIpD,SAASiD,KAAK;QACxB,IAAI,CAAChD,MAAMmD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AACA,OAAO,SAASC,+BACdC,aAA4B,EAC5BC,kBAAoD;QAGlDA;IADF,IACEA,CAAAA,uCAAAA,4BAAAA,mBAAoB9B,KAAK,qBAAzB8B,0BAA2B7B,IAAI,MAAK,WACpC,wHAAwH;IACxH,kEAAkE;IAClE6B,mBAAmB9B,KAAK,CAACvE,YAAY,KAAKqG,mBAAmB9B,KAAK,CAACpF,IAAI,IACvE,kEAAkE;IAClEiH,cAAcjH,IAAI,KAAKkH,mBAAmB9B,KAAK,CAACvE,YAAY,EAC5D;QACA,OAAOqG,mBAAmB9B,KAAK;IACjC;IACA,OAAO;AACT;AAEA,OAAO,eAAe+B,cAAc,EAClCvG,MAAM,EACNwG,WAAW,EACXpD,OAAO,EACPN,KAAK,EACL2D,MAAM,EACN9I,WAAW,EACX+I,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAWjB;IACC,MAAMC,QAAQnJ,SAASC;IACvB,MAAMmJ,cAAcD,MAAM7G,QAAQ;QAChC0G;QACAC,gBAAgBA,kBAAkBI;IACpC,GACGC,OAAO,CAAC;QACPC,SAASL,oBAAoB;IAC/B,GACCM,MAAM;IAET,IAAIT,QAAQ;QACVK,YAAYK,MAAM,CAACrE,OAAO2D;IAC5B,OAAO;QACLK,YAAYK,MAAM,CAACrE,OAAOiE,WAAW;YACnCK,oBAAoB;QACtB;IACF;IAEA,IAAIZ,gBAAgB7J,MAAM;QACxBmK,YAAYO,IAAI,CAAC;YACfjE,SAASnF,KAAKE,GAAG,CAACiF,UAAU,IAAI;YAChCkE,QAAQ;QACV;IACF,OAAO,IAAId,gBAAgB5J,MAAM;QAC/BkK,YAAYS,IAAI,CAAC;YAAEnE;QAAQ;IAC7B,OAAO,IAAIoD,gBAAgB3J,KAAK;QAC9BiK,YAAYU,GAAG,CAAC;YAAEpE;QAAQ;IAC5B,OAAO,IAAIoD,gBAAgB1J,MAAM;QAC/BgK,YAAYW,IAAI,CAAC;YAAErE;YAASsE,SAAS;QAAK;IAC5C;IAEA,MAAMC,kBAAkB,MAAMb,YAAYc,QAAQ;IAElD,OAAOD;AACT;AAEA,OAAO,eAAeE,mBAAmB9F,IAAY;IACnD,MAAM+F,MAAM,MAAMC,MAAMhG,MAAM;QAC5BiG,QAAQC,YAAYjB,OAAO,CAAC;IAC9B,GAAG1G,KAAK,CAAC,CAAC4E,MAAQA;IAElB,IAAI4C,eAAexJ,OAAO;QACxB,MAAM4G,MAAM4C;QACZ,IAAI5C,IAAIgD,IAAI,KAAK,gBAAgB;YAC/B1L,IAAI2I,KAAK,CAAC,yCAAyCpD;YACnD,MAAM,IAAIqD,WACR,KACA;QAEJ;QACA,MAAMF;IACR;IAEA,IAAI,CAAC4C,IAAIK,EAAE,EAAE;QACX3L,IAAI2I,KAAK,CAAC,sCAAsCpD,MAAM+F,IAAIM,MAAM;QAChE,MAAM,IAAIhD,WACR0C,IAAIM,MAAM,EACV;IAEJ;IAEA,MAAMpI,SAASV,OAAOC,IAAI,CAAC,MAAMuI,IAAIO,WAAW;IAChD,MAAM7B,cAAcsB,IAAIzE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAM0E,eAAeR,IAAIzE,OAAO,CAACO,GAAG,CAAC;IACrC,MAAMxE,OAAOD,YAAY2I,IAAIzE,OAAO,CAACO,GAAG,CAAC,SAAS5D;IAClD,OAAO;QAAEA;QAAQwG;QAAa8B;QAAclJ;IAAK;AACnD;AAEA,OAAO,eAAemJ,mBACpBxG,IAAY,EACZyG,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASxM,2BAA2B;YACxCyF,KAAKG;YACL6G,QAAQJ,KAAKI,MAAM,IAAI;YACvBvF,SAASmF,KAAKnF,OAAO;YACrBwF,QAAQL,KAAKK,MAAM;QACrB;QAEA,MAAMH,cAAcC,OAAO5H,GAAG,EAAE4H,OAAOb,GAAG,EAAE/L,QAAQ+M,KAAK,CAAC/G,MAAM;QAChE,MAAM4G,OAAOb,GAAG,CAACiB,WAAW;QAE5B,IAAI,CAACJ,OAAOb,GAAG,CAACzC,UAAU,EAAE;YAC1B7I,IAAI2I,KAAK,CAAC,6BAA6BpD,MAAM4G,OAAOb,GAAG,CAACzC,UAAU;YAClE,MAAM,IAAID,WACRuD,OAAOb,GAAG,CAACzC,UAAU,EACrB;QAEJ;QAEA,MAAMrF,SAASV,OAAO0J,MAAM,CAACL,OAAOb,GAAG,CAACmB,OAAO;QAC/C,MAAMzC,cAAcmC,OAAOb,GAAG,CAACoB,SAAS,CAAC;QACzC,MAAMZ,eAAeK,OAAOb,GAAG,CAACoB,SAAS,CAAC;QAC1C,MAAM9J,OAAOD,YAAYwJ,OAAOb,GAAG,CAACoB,SAAS,CAAC,SAASlJ;QAEvD,OAAO;YAAEA;YAAQwG;YAAa8B;YAAclJ;QAAK;IACnD,EAAE,OAAO8F,KAAK;QACZ1I,IAAI2I,KAAK,CAAC,sCAAsCpD,MAAMmD;QACtD,MAAM,IAAIE,WACR,KACA;IAEJ;AACF;AAEA,OAAO,eAAe+D,eACpBC,aAA4B,EAC5BC,YAGC,EACDpI,UAYC,EACDqI,IAIC;QAeCF;IANF,MAAM,EAAErH,IAAI,EAAEqB,OAAO,EAAEN,KAAK,EAAEpE,QAAQ,EAAE,GAAG2K;IAC3C,MAAM,EAAErJ,QAAQuJ,cAAc,EAAEnK,MAAMa,YAAY,EAAE,GAAGmJ;IACvD,MAAMtJ,SAASiG,UAAUqD,cAAcd,YAAY;IAEnD,MAAMkB,eACJ/I,kBAAkB8I,qBAClBH,6BAAAA,cAAc5C,WAAW,qBAAzB4C,2BAA2BtD,WAAW,GAAGD,IAAI;IAE/C,IAAI2D,cAAc;QAChB,IACEA,aAAanH,UAAU,CAAC,gBACxB,CAACpB,WAAWG,MAAM,CAACqI,mBAAmB,EACtC;YACA,IAAI,CAACH,KAAKI,MAAM,EAAE;gBAChBlN,IAAI2I,KAAK,CACP,CAAC,wBAAwB,EAAEpD,KAAK,YAAY,EAAEyH,aAAa,qCAAqC,CAAC;YAErG;YACA,MAAM,IAAIpE,WACR,KACA;QAEJ;QACA,IAAI/H,iBAAiBsB,QAAQ,CAAC6K,iBAAiB3N,WAAW0N,iBAAiB;YACzE,IAAI,CAACD,KAAKI,MAAM,EAAE;gBAChBlN,IAAIyF,QAAQ,CACV,CAAC,wBAAwB,EAAEF,KAAK,8GAA8G,CAAC;YAEnJ;YACA,OAAO;gBACL/B,QAAQuJ;gBACR/C,aAAagD;gBACb1J;gBACAV,MAAMa;gBACNA;YACF;QACF;QACA,IAAI3C,aAAaqB,QAAQ,CAAC6K,eAAe;YACvC,wEAAwE;YACxE,6DAA6D;YAC7D,4EAA4E;YAC5E,OAAO;gBACLxJ,QAAQuJ;gBACR/C,aAAagD;gBACb1J;gBACAV,MAAMa;gBACNA;YACF;QACF;QACA,IAAI,CAACuJ,aAAanH,UAAU,CAAC,aAAamH,aAAa7K,QAAQ,CAAC,MAAM;YACpE,IAAI,CAAC2K,KAAKI,MAAM,EAAE;gBAChBlN,IAAI2I,KAAK,CACP,kDACApD,MACA,YACAyH;YAEJ;YACA,MAAM,IAAIpE,WAAW,KAAK;QAC5B;IACF;IAEA,IAAIoB;IAEJ,IAAI9H,UAAU;QACZ8H,cAAc9H;IAChB,OAAO,IACL8K,CAAAA,gCAAAA,aAAcnH,UAAU,CAAC,cACzB9F,aAAaiN,iBACbA,iBAAiB5M,QACjB4M,iBAAiB7M,MACjB;QACA6J,cAAcgD;IAChB,OAAO;QACLhD,cAAc1J;IAChB;IACA,MAAM6M,wBAAwBvD,+BAC5BgD,eACAE,KAAKhD,kBAAkB;IAEzB,IAAIqD,uBAAuB;YAIfL;QAHV,OAAO;YACLtJ,QAAQ2J,sBAAsB3J,MAAM;YACpCwG;YACA1G,QAAQwJ,CAAAA,yBAAAA,2BAAAA,KAAMhD,kBAAkB,qBAAxBgD,yBAA0B1E,aAAa,KAAI9E;YACnDV,MAAMuK,sBAAsBvK,IAAI;YAChCa,cAAc0J,sBAAsB1J,YAAY;QAClD;IACF;IAEA,IAAI;QACF,IAAI0H,kBAAkB,MAAMpB,cAAc;YACxCvG,QAAQuJ;YACR/C;YACApD;YACAN;YACAnF,aAAasD,WAAW2I,YAAY,CAACC,iBAAiB;YACtDnD,kBAAkBzF,WAAW2I,YAAY,CAACE,oBAAoB;YAC9DnD,gBAAgB1F,WAAW2I,YAAY,CAACG,oBAAoB;YAC5DnD,kBAAkB3F,WAAW2I,YAAY,CAACI,sBAAsB;QAClE;QACA,IAAIV,KAAKpI,KAAK,IAAI4B,SAASvF,iBAAiB6F,YAAY5F,cAAc;YACpE,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF,MAAMyM,OAAO,MAAMC,aAAavC;YAChC,MAAMwC,WAAW;gBACfC,WAAWH,KAAKnH,KAAK;gBACrBuH,YAAYJ,KAAKxD,MAAM;gBACvB6D,aAAa,CAAC,KAAK,EAAE9D,YAAY,QAAQ,EAAEmB,gBAAgBnI,QAAQ,CACjE,WACC;YACL;YACAmI,kBAAkBrI,OAAOC,IAAI,CAACgL,SAASvO,gBAAgBmO;YACvD3D,cAAc;QAChB;QACA,OAAO;YACLxG,QAAQ2H;YACRnB;YACA1G,QAAQ7B,KAAKE,GAAG,CAAC2B,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC1DpC,MAAMK,aAAakI;YACnB1H;QACF;IACF,EAAE,OAAOkF,OAAO;QACd,IAAIqE,cAAc;YAChB,yDAAyD;YACzD,OAAO;gBACLxJ,QAAQuJ;gBACR/C,aAAagD;gBACb1J,QAAQmB,WAAWG,MAAM,CAACI,eAAe;gBACzCpC,MAAMa;gBACNA;gBACAkF;YACF;QACF,OAAO;YACL,MAAM,IAAIC,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAASoF,yBACP5I,GAAW,EACX4E,WAA0B;IAE1B,MAAM,CAACiE,sBAAsB,GAAG7I,IAAIyC,KAAK,CAAC,KAAK;IAC/C,MAAMqG,wBAAwBD,sBAAsBpG,KAAK,CAAC,KAAKsG,GAAG;IAClE,IAAI,CAACnE,eAAe,CAACkE,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsBrG,KAAK,CAAC,KAAK;IACpD,MAAMxE,YAAYtD,aAAaiK;IAC/B,OAAO,GAAGoE,SAAS,CAAC,EAAE/K,WAAW;AACnC;AAEA,SAASgL,mBACP9J,GAAoB,EACpB+G,GAAmB,EACnBlG,GAAW,EACXxC,IAAY,EACZoH,WAA0B,EAC1BlD,QAAiB,EACjBwH,MAAoB,EACpBC,YAAiC,EACjCjL,MAAc,EACdoB,KAAc;IAEd4G,IAAIkD,SAAS,CAAC,QAAQ;IACtBlD,IAAIkD,SAAS,CACX,iBACA1H,WACI,yCACA,CAAC,gBAAgB,EAAEpC,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAIzD,iBAAiB0E,KAAK+G,KAAK1I,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAE6L,UAAU;QAAK;IAC1B;IACA,IAAIzE,aAAa;QACfsB,IAAIkD,SAAS,CAAC,gBAAgBxE;IAChC;IAEA,MAAMoE,WAAWJ,yBAAyB5I,KAAK4E;IAC/CsB,IAAIkD,SAAS,CACX,uBACArP,mBAAmBiP,UAAU;QAAEM,MAAMH,aAAaI,sBAAsB;IAAC;IAG3ErD,IAAIkD,SAAS,CAAC,2BAA2BD,aAAaK,qBAAqB;IAC3EtD,IAAIkD,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEG,UAAU;IAAM;AAC3B;AAEA,OAAO,SAASI,aACdtK,GAAoB,EACpB+G,GAAmB,EACnBlG,GAAW,EACX/B,SAAiB,EACjBG,MAAc,EACdZ,IAAY,EACZkE,QAAiB,EACjBwH,MAAoB,EACpBC,YAAiC,EACjCjL,MAAc,EACdoB,KAAc;IAEd,MAAMsF,cAAclK,eAAeuD;IACnC,MAAMyL,SAAST,mBACb9J,KACA+G,KACAlG,KACAxC,MACAoH,aACAlD,UACAwH,QACAC,cACAjL,QACAoB;IAEF,IAAI,CAACoK,OAAOL,QAAQ,EAAE;QACpBnD,IAAIkD,SAAS,CAAC,kBAAkB1L,OAAOiM,UAAU,CAACvL;QAClD8H,IAAI0D,GAAG,CAACxL;IACV;AACF;AAEA,OAAO,eAAekK,aAAalK,MAAc;IAI/C,MAAM,EAAE8C,KAAK,EAAE2D,MAAM,EAAE,GAAG7K,YAAYoE;IACtC,OAAO;QAAE8C;QAAO2D;IAAO;AACzB"}